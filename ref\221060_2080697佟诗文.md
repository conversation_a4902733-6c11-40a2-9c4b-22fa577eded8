NORTHEASTERN 
U N I V E R S I T Y      
硕士学位论文 
THESIS FOR MASTER'S DEGREE     
论文题目 
DF 银行外汇交易数据报表平台开发项目风
险管理研究 
作    者 
佟诗文 
学    号 
2080697 
学 院(部) 
工商管理学院 
专    业 
工程管理 
指导教师 
黄训江                   
2023 年    
  06 月 
 
分类号                                  密级     公开            
UDC                     
 
学  位  论  文 
 
DF 银行外汇交易数据报表平台开发项目风险管理研究 
 
 
 
作者姓名： 
作者学号： 
佟诗文 
2080697 
指导教师： 黄训江 副教授 
 
东北大学工商管理学院 
申请学位级别： 硕士 
学科类别： 工程管理硕士专业学位 
学科专业名称： 工程管理 
论文提交日期： 2023 年6 月 论文答辩日期： 2023 年6 月 
学位授予日期： 2023 年7 月 答辩委员会主席： 张翠华 教授 
评
阅
人
： 喻海飞副教授、张展教授 
 
 
 
 
 
 
 
 
 
东  北  大  学 
2023 年 06 月
 
 
A Thesis for the Degree of Master in Engineering Management 
 
 
 
 
Research on Risk Management of DF FX Data 
Exchange Platform 
 
 
 
 
 
 
 
By:Tong Shiwen 
 
 
 
Supervisor: Associate Professor <PERSON>  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Northeastern University 
 
June 2023
东北大学硕士学位论文                                                              摘要 
II 
 
摘 要 
在金融行业内，瞬息万变的国际局势与交易形势使得外汇市场变化莫测。投资经理
人对外汇数据的倚重使得DF 银行外汇交易数据报表平台开发项目必须将重点聚焦在如
何将庞大的原始数据进行梳理筛选重建上。在项目进行过程中，该平台必须为数据的准
确性，安全性以及及时性提供保障，并且能够对市场的变化及时做出反应和调整。如果
项目的效果达不到预设的标准，那么投资经理人很有可能会基于错误的数据记录对账户
内产品进行仓位调整和交易，从而导致客户的利益受到伤害。由于企业的数字化转型趋
势加上全球疫情的不断反复，传统的项目管理方法已无法有效控制该外汇项目风险并满
足客户需求，所以一套新型项目风险管理方案需要被及时制定及有效落实。 
通过阅读大量文献，对比和整理当前项目的特征，梳理出DF 银行外汇交易数据报
表平台开发项目面临的普遍风险和独有风险，并在此基础上总结出初始风险清单。而后
使用此清单当作参照标的物，结合项目本身的开发计划召集项目开发相关人员使用头脑
风暴法对当前项目潜在项目风险进行进一步讨论。此番讨论涵盖了产品构思，需求分析
以及后期运营等多个角度，对初始风险清单进行了完善形成DF 银行外汇交易数据报表
平台开发项目最终风险清单。根据最终风险清单，总结出各个风险存在原因及潜在影响
周期，将它们归纳为不同的风险类型——包括常规的管理风险和人员风险以及金融行业
内专有的技术风险与行业风险。在对风险进行系统识别的基础上，构建了项目风险评价
指标体系，进而利用层次分析法和模糊评价法对其风险等级进行了评估，评估结果显示
数据合规、关系人沟通、数据安全、需求变动、系统相应不及时、人员变动、资金紧缺
等风险是项目面临的主要风险。最后针对风险类别和风险等级的不同，构建了各风险应
对策略，并从培育文化氛围、调整组织架构等方面构建了风险控制措施。 
本研究针对外汇交易数据平台搭建过程中的风险特点，构建了相关的风险应对计划
及管理措施，但是研究过程中也发现，尽管关于数据平台开发风险的研究文献很多，关
于金融风险的研究也很多，但是综合两种风险并使用中外合作团队最终完成的项目很少。
因此本文尝试在此方面做出总结归纳和初步探索，为同类型项目风险管理提供参考依据。
该项目的不足之处在于项目展开风险识别时，受疫情影响没能够让所有国家的终端客户
参与讨论确保涵盖其风险视角。未来还有此类项目需求，建议将所有项目相关方邀请到
位再进行风险识别。 
 
关键词：风险管理；软件开发；金融数据；数据平台 
 
东北大学硕士学位论文                                                              摘要 
III 
 
Abstract 
In the financial industry, the rapidly changing international situation and trading situation 
make the foreign exchange market unpredictable. Investment managers' reliance on foreign 
exchange data makes foreign exchange trading data platform development projects must focus 
on how to sort out, screen and reconstruct huge raw data. During the project, the platform must 
provide guarantees for data accuracy, security and timeliness, and be able to react and adjust to 
market changes in a timely manner. If the effect of the project does not meet the preset standards, 
then the investment manager is likely to adjust and trade their products in the account based on 
incorrect data records which will result in financial loss. Because of the trend of digital 
transformation of enterprises and the continuous recurrence of global epidemic, traditional 
project management methods can no longer effectively control the risks of the foreign exchange 
project and meet the needs of customers, a new project risk management plan needs to be 
formulated and implemented effectively and timely. 
By reading a large number of literature, comparing and sorting out the characteristics of 
the current project, the general risks and unique risks faced by the project are sorted out, and 
the initial risk list is summarized on this basis. Then, use this checklist as a reference object, 
and use the development plan of the project itself to confront the project development personnel 
to use the brainstorming method to further discuss the potential project risks of the current 
project. The discussion covered multiple perspectives such as product conception, demand 
analysis and post-operation, and improved the initial risk list to form the final risk list of the 
project. According to the final risk list, the reasons for the existence and potential impact cycle 
of each risk are summarized, and they are summarized into different risk types, including 
conventional management risk and personnel risk, as well as proprietary technical risk and 
industry risk in the financial industry. On the basis of systematic identification of risks, the 
project risk evaluation index system was constructed, and then the risk level was evaluated by 
using the analytic hierarchy method and fuzzy evaluation method, and the evaluation results 
showed that the risks such as data compliance, related party communication, data security, 
demand changes, system untimeliness, personnel changes, and shortage of funds were the main 
risks faced by the project. Finally, according to the different risk categories and risk levels, each 
risk response strategy was constructed, and risk control measures were constructed from the 
东北大学硕士学位论文                                                              摘要 
IV 
 
aspects of cultivating cultural atmosphere and adjusting organizational structure. 
In the process of research, it was found that although there is a lot of research literature on 
the risks of data platform development and a lot of research on financial risks, there are few 
projects that synthesize the two risks and use Sino-foreign cooperation teams to complete them. 
This paper attempts to summarize and explore this aspect to provide reference for the risk 
management of the same type of project. There is still a deficiency in the fact that when the 
project run into risk identification phase, it was not possible to involve end customers in risk 
identification due to the impact of covid-19. If there still such project needs in the future, and it 
is recommended to invite all project stakeholders in place before risk identification. 
Key words: Risk management; Software development; Financial data; Data platform 
东北大学硕士学位论文                                                              目录 
II 
 
目录 
独创性声明 .......................................................................................................................... I 
摘 要 ................................................................................................................................... II 
Abstract .............................................................................................................................. III 
第1 章 绪论 ................................................................................................................... - 1 - 
1.1 研究背景与意义 ..................................................................................................... - 1 - 
1.1.1 研究背景 ....................................................................................................... - 1 - 
1.1.2 研究的目的与意义 ....................................................................................... - 2 - 
1.2 国内外研究现状 ..................................................................................................... - 3 - 
1.2.1 国外研究现状 ............................................................................................... - 3 - 
1.2.2 国内研究现状 ............................................................................................... - 3 - 
1.2.3 相关研究评述 ............................................................................................... - 4 - 
1.3 研究内容与技术路线 ............................................................................................. - 5 - 
1.3.1 研究内容 ....................................................................................................... - 5 - 
1.3.2 研究方法与技术路线 ................................................................................... - 5 - 
1.3.3 论文结构 ....................................................................................................... - 6 - 
第2 章 项目风险管理相关理论与概述 .................................................................... - 8 - 
2.1 项目风险概念及分类 ............................................................................................. - 8 - 
2.1.1 项目风险概念 ............................................................................................... - 8 - 
2.1.2 项目风险分类 ............................................................................................... - 8 - 
2.1.3 项目风险的特点 ........................................................................................... - 9 - 
2.1.4 项目风险管理的主要步骤 ......................................................................... - 10 - 
2.2 项目风险识别主要方法 ........................................................................................ - 11 - 
2.2.1 头脑风暴法 .................................................................................................. - 11 - 
2.2.2 德尔菲法 ..................................................................................................... - 12 - 
2.3 项目风险评价主要方法 ....................................................................................... - 13 - 
2.3.1 层次分析法 ................................................................................................. - 13 - 
东北大学硕士学位论文                                                              目录 
III 
 
2.3.2 模糊评价法 ................................................................................................. - 14 - 
2.4 项目风险应对主要方法 ....................................................................................... - 14 - 
2.4.1 主动式风险应对 ......................................................................................... - 14 - 
2.4.2 被动式风险应对 ......................................................................................... - 15 - 
2.5 小结 ....................................................................................................................... - 15 - 
第3 章 DF 银行外汇交易数据报表平台项目概况 .............................................. - 16 - 
3.1 DF 银行外汇交易数据报表平台开发项目简介 .................................................. - 16 - 
3.1.1 DF 银行外汇交易数据报表平台开发项目背景 ........................................ - 16 - 
3.1.2 DF 银行外汇交易数据报表平台开发项目目标及主要建设内容 ............ - 16 - 
3.2 DF 银行外汇交易数据报表平台开发项目的组织结构 ...................................... - 19 - 
3.3 DF 银行外汇交易数据报表平台开发项目风险管理情况 .................................. - 21 - 
3.3.1 DF 银行外汇交易数据报表平台开发项目风险管理组织 ........................ - 21 - 
3.3.2 DF 银行外汇交易数据报表平台开发项目风险管理制度 ........................ - 22 - 
3.3.3 DF 银行外汇交易数据报表平台开发项目风险管理流程 ........................ - 22 - 
3.4 小结 ....................................................................................................................... - 23 - 
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 ............................. - 25 - 
4.1 DF 银行外汇交易数据报表平台开发项目风险识别依据 .................................. - 25 - 
4.1.1 风险管理计划 ............................................................................................. - 25 - 
4.1.2 项目实施计划 ............................................................................................. - 25 - 
4.1.3 项目相关规定及办法 ................................................................................. - 26 - 
4.2 DF 银行外汇交易数据报表平台开发项目风险的识别 ...................................... - 27 - 
4.2.1 DF 银行外汇交易数据报表平台开发项目风险的识别特点与原则 ........ - 27 - 
4.2.2 DF 银行外汇交易数据报表平台开发项目风险的识别目的与方法 ........ - 28 - 
4.2.3 DF 银行外汇交易数据报表平台开发项目风险的识别过程 .................... - 28 - 
4.3 风险的识别结果 ................................................................................................... - 29 - 
4.3.1 头脑风暴法分析项目存在的风险 .............................................................. - 29 - 
4.3.2 德尔菲法分析项目存在的风险 .................................................................. - 33 - 
4.3.3 DF 银行外汇交易数据报表平台开发项目风险清单 ................................ - 34 - 
4.4 小结 ....................................................................................................................... - 35 - 
东北大学硕士学位论文                                                              目录 
IV 
 
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 ............................. - 36 - 
5.1 DF 银行外汇交易数据报表平台开发项目风险评价方法确定 .......................... - 36 - 
5.2 运用模糊评价法进行项目指标风险评价 ........................................................... - 36 - 
5.2.1 建立风险评价模型与矩阵 ......................................................................... - 36 - 
5.2.2 确定评价指标 ............................................................................................. - 41 - 
5.2.3 确定评价因素集的权重 ............................................................................. - 42 - 
5.2.4 模糊评价 ..................................................................................................... - 43 - 
5.2.5 评价结果分析 ............................................................................................. - 44 - 
5.3 小结 ....................................................................................................................... - 44 - 
第6 章 DF 银行外汇交易数据报表平台开发项目风险的控制 ......................... - 45 - 
6.1 DF 银行外汇交易数据报表平台开发项目风险应对策略 .................................. - 45 - 
6.1.1 管理风险应对策略 ..................................................................................... - 45 - 
6.1.2 人员风险应对策略 ..................................................................................... - 46 - 
6.1.3 数据与技术风险应对策略 ......................................................................... - 47 - 
6.1.4 合规与信用风险应对策略 ......................................................................... - 48 - 
6.2 DF 银行外汇交易数据报表平台开发项目风险控制措施 .................................. - 48 - 
6.2.1 培育文化氛围 ............................................................................................. - 49 - 
6.2.2 调整组织架构 ............................................................................................. - 49 - 
6.2.3 加强项目监管 ............................................................................................. - 50 - 
6.2.4 强化员工培训 ............................................................................................. - 51 - 
6.2.5 修整技术细节 ............................................................................................. - 52 - 
6.3 小结 ....................................................................................................................... - 53 - 
第7 章 结论与展望 ................................................................................................... - 54 - 
参考文献 ...................................................................................................................... - 56 - 
致谢 .............................................................................................................................. - 58 - 
东北大学硕士学位论文 
第1 章 绪论 
 
- 1 - 
第1 章 绪论 
信息化是20 世纪的标签，数字化是21 世纪的标签。受疫情的持续影响，数字化变革不仅
席卷各行各业且其变革程度也在加深。人们的生活方式和贸易方式正在逐步从线下转向线上，
新的信息化工程项目的管理方式也正替代传统的瀑布式开发方式形成主流。中国在信息化领域
教育培养了众多人才。随着计算机科技人才国内占比的提高，再加上中国的人口基数优势，越
来越多的项目开始寻求中外合作机会。本章将主要介绍中外合作的金融类信息化平台项目风险
管理相关的研究背景、研究现状、方法与技术路线。 
1.1 研究背景与意义 
1.1.1 研究背景 
从2019 年疫情开始到2023 年疫情逐步走向常态化，随着这几年疫情情况的不断变化，商
业银行在国内的运营方式有了很大的变化，从工作地点到工作内容。而此次疫情带给商业银行
最大的变化是它促进了商业银行的数字化转型与引发其在技术方式上的变革。当然无论是传统
的银行业，像商业银行，投资银行乃至中央银行抑或是证券交易所，各级券商，公私募基金还
是保险公司，大家都意识到了全面数字化转型对于自身发展的必要性。于是各大机构纷纷成立
数字化部门，寻求数字化转型，想在数字化的进程中寻找到自己的位置或者拓展自己业务布局，
通过回顾自身的业务内容与流程重构商业模式。而这转变的过程，增加了银行软件项目开发的
风险。陈振华在2021 年提出了大数据技术对于银行风险控制的重要性[1]。 
第一点为商业银行对业务数据容错率低。一方面金融业务每天都会产生海量的数据，其中
包括买卖行为，报价，净值，币种，交易时间，买卖双方，交易地点，潜在的买卖市场以及交
易机会等等。而这些基础数据同时又能组成各种各样的统计报表。类型丰富的初始数据和复杂
的加工数据组合导致数据的维度多样但质量参差不齐，与应用场景的相关性高。 
第二点为商业银行需求的数据体量大又对数据的准确性要求高。一则数据信息供应商会使
用属于他们自己的系统，提供的API 接口各有不同，二则不同客户会有不同的需求与想法，基
于客户至上的原则，为使得数据可以顺利地从数据供应商处流到客户端，金融企业会给每个相
关方都做了对应的定制化开发。正因如此定制化的开发，企业职能与组织部门的精细化分使得
各个部门产生内容相同但命名不同的数据；而统筹类信息化职能部门的缺失导致数据过于分散
形成了数据孤岛，即没有办法形成结构化体系的数据，进而使得组织没有办法对企业内部数据
东北大学硕士学位论文 
第1 章 绪论 
 
- 2 - 
进行分析。疫情对银行线下办公以及线下业务造成了巨大的冲击，导致已经独立的数据孤岛规
模进一步增加，形成的数据结构进一步复杂，大部分公司的部门级应用根本无法被满足，传统
的开发模式难以适应市场需求的变化速度，软件系统开发周期长且成本高，很难跟随企业业务
变化同频做出改变。在此背景下，数据中台，数据清洗以及大数据分析便成为了金融业转型的
重要课题，而数据治理过程中可能给业务总流程带来的风险也不可忽视。 
第三点为转型期间管理方式混乱。为了更好地匹配公司的战略方针-- KYC（know your 
customer，此次项目管理层决定采用“敏捷方法论”，一个诞生于21 世纪的，针对于信息化工
程项目，目前最为流行的方法论来管理此次项目。采用敏捷方法论的目的是为了让开发出来的
系统可以更好地满足用户的需求。敏捷方法论非常看重开发人员与用户的沟通，但在疫情反复
局部爆发的今天，员工没有办法再统一时间聚集去讨论也增加了项目顺利进行如期上线的风险。 
第四点为很容易受外部环境影响，为了应对疫情危机下的恐慌情绪，全球央行纷纷调整经
济政策，采用量化宽松的方式缓解危机。在此期间出现的美股多次熔断，数字货币的全面发展，
双碳政策的落地，新交易所的成立都对外汇市场造成了极大影响，进一步增加了数据获取与处
理的难度。 
1.1.2 研究的目的与意义 
(1) 论文研究的目的 
通过使用项目风险理论管理知识对当前项目中的风险进行识别，建立风险评价模型对潜在
的管理、人员、数据、技术与合规信用等风险进行模糊评价，最后按风险因素给出合理建议并
控制项目中的风险。 
(2) 论文研究的理论意义 
项目风险管理的理论常被应用在建筑工程管理项目中，在软件工程的项目中应用较少，在
应用敏捷与瀑布式管理方式结合的数字化报表平台开发项目中应用更少。此类型项目在开发过
程中对风险的识别与评估依靠产品经理的个人能力，本论文可以给该类型项目的风险管控提供
借鉴。本论文研究的项目虽然是数字化报表平台开发项目，但软件工程开发流程具有共性，所
以不仅仅是数字化报表类平台开发项目可以借鉴本论文的工作内容，只要是敏捷与瀑布式管理
方式结合的软件工程开发类型项目均可以参考借鉴。 
(3) 论文研究的实践意义 
通过本论文的研究，为公司构建了新型管理模式下系统开发的项目风险识别、评估与控制
的流程。也帮助项目识别了具体风险、降低了可识别的潜在风险对项目或公司产生影响的可能
性。 
东北大学硕士学位论文 
第1 章 绪论 
 
- 3 - 
1.2 国内外研究现状 
1.2.1 国外研究现状 
经信息与文献整理，对于风险管理的具体提出是由法国还是德国各说纷纭，但是可以肯定
的一点是，风险管理的概念是在一战前后时间的形成，并在美国被应用于企业管理和保险与金
融领域的研究。敏捷方法论同样最先由美国提出，并在硅谷迅速取代了传统的瀑布式开发的地
位成为了主流。敏捷能这么快的攻占市场背后的一大原因是数字化转型的进程。欧美国家对数
字化转型投入的早且投入金额大。主要原因在于美国在互联网的科技浪潮下涌出了许多创业企
业，其背后有VC/PE 的投资人支持他们在创新的道路上持续前行，而这些VC/PE 的投资人经
常会关注新鲜的，有突破性的可被投资为产品的想法，所以整个数字化创业的大环境相比起中
国市场会更有活力，也因此欧美的企业选择的方案更灵活且也更贴近市场需求，更受需求方的
喜欢。但比起国内更面向结果的项目管理方式想必会面临过更多样的变化，分析的也更细致，
在当时参考性受限。 
在风险控制的研究方向，Marnada，Raharjo 等也关注到了在疫情之后，项目管理方法对于
灵活性的需求程度高于疫情前期，于是他们采用SLR（systematic literature review）来应对需求
的变更，并得出人员与组织，用户的需求排序，超出范围的需求与沟通协调为主要影响项[2]。
在跨国项目中，Rocío 等一起研究5 个项目中不同类型的风险是怎么影响到项目的，并用模糊
评价法进行了打分得出合理调节文化差异对结果有积极影响[3]。Buganováa，Šimíčková等在论
文中研究了传统项目管理与敏捷项目管理的区别，并针对团队组建方式，预算与流程管理等方
向做了细致研究[4]。A. Lill，Wald b 研究了在复杂类型项目中，采用哪种管理方式会更省时与
顺利的控制项目[5]。 
1.2.2 国内研究现状 
我国从80 年代初期才开始引入项目管理方法，但当时尚未引入风险管理方法，主要原因
是当时经济发展速度缓慢和经济结构体制没有市场化，随着大型的土木实施工程的出现，对于
风险管理的需求才逐步提上日程。陆怡舟，赵韩婷，张萌在商业银行项目提出了一种基于AHP-
DEA 方法的风险绩效评价框架。模型生成的风险效率评价结果补充了原有风险评价指标体系
的加权评价结果[6]。梅中鹤在房地产项目中混合使用 ANP 与 FCE 方法，构建出了适用项目
的风险测度模型并基于此进行权重结果排序与测评[7]。蒋晨丽，林丽春，郑惠婷在养生旅游项
目中细致的使用了文献研究法，问卷调查，访谈法进行风险识别也采用专家打分法，矩阵数据
分析，头脑风暴法，层次分析法，决策树分析，德尔菲法进行风险评价[8]。段爱玲，颜宇航，
东北大学硕士学位论文 
第1 章 绪论 
 
- 4 - 
苑天文在银行项目中先采用运用SWOT 构造递阶层次结构再用层次单排序及一致性检验的风
险分析方法[9]。李俊蕾，刘成程，王超亮，李庆阳热电厂迁建项目中用总结归纳案例和日常稳
评操作经验的方法总结出不可预见因素影响参照评价指南判断风险事件的后果与影响[10]。李
晓璇，方逸文等在集团对外股权投资项目中使用德尔菲法、层次分析法并结合定性与定量评估，
测算得出投资风险系数，构建了完整科学的风险评估体系[11]。蒋文婷，路子威等在航天研发项
目中从项目现存的管理问题入手分析项目流程管理和项目组合风险带来的影响为了管理效能。
并运用设计结构矩阵方法对项目组合关系及风险网络进行可视化展示，构建模型，分析风险与
组织的关系，并梳理组合风险的因素，展开研讨。为后续项目提供了案例和指导[12]。卢子健,郭
丽华等在铁道投融资项目中构建了评价体系，针对每个标准层指标进行单独评价[13]。杨家辉，
贺健在分析传统企业与数据转型企业的区别的过程中，来探讨用数据模型进行量化研究的可能
性，并试图论证准确性。该模型是基于AHP 的大数据企业财务预警模型[14]。李杨，王珊使用
复杂网络分析的方法识别项目风险，依据指标识别风险较大的因素后通过动态计算最终确定关
键风险[15]。赵子怡在进行文献调查后建立了一定知识储备的情况下应用Logistic 回归方法，并
结合调查问卷的结果，对国际工程项目风险管理进行了研究并对内外部风险的管理方法做了建
议[16]。谭元戎，冯丽娜等使用过程改善模型与叶贝斯网络来分析风险并基于风险评估网与缓降
网来对风险进行评价[17]。张亚莉，杨朝君提出了一种基于风险管理模型，通过对过往文献中项
目的风险模式及对应的管理方法形成知识储备，并通过细节上的匹配对应来选取适合当前组织
项目风险管理的方法[18]。王蔚在软件项目风险中使用熵权TOPSIS 的分析方法，以弱化专家的
主观判断对于项目因素的影响[19]。代理想,张先飞等在物流工程中进行项目风险分析的过程中，
先按照业务流程分析出风险再评估风险为内部风险或外部风险进而进行评估。卢士达，张露维
等使用贝叶斯网络来搭建风险评估模型，因考虑到项目中不确定风险因素过多，并结合模糊集
理论转定性到定量分析[20]。潘亮亮在国有企业的海外施工项目中按照阶段来分析项目可能会
遇到的风险[21]。王京晶使用三要素风险评估步骤与专家访谈法来对项目风险进行识别与评估
[22]。 
1.2.3 相关研究评述 
在文献研究的基础上总结得出目前项目风险管理的理论已经被应用在各行各业，施工项目
与软件工程居多。在风险识别的环节使用较多的方法为流程图法、头脑风暴法。在风险评估的
环节使用的方法多为AHP 法与其他定量方法结合。各项目风险识别与评价流程均对项目风险
管理有所帮助，给本论文的研究思路奠定了基础。综合来看，国外在疫情期间对于风险的管理
手段相较于疫情前要灵活，对于风险类型的关注相较于疫情之前要更多样。 
东北大学硕士学位论文 
第1 章 绪论 
 
- 5 - 
1.3 研究内容与技术路线  
1.3.1 研究内容 
本论文以DF 银行外汇交易数据平台开发项目为研究对象。对该项目从预研、启动、实施
与运营进行全周期的风险管理分析研究，运用风险管理的相关理论与工具，识别项目风险并对
识别出的项目风险进行评价与控制。为了确保在项目周期中可以使用采用较为前沿和受关注的
时髦的项目风险管理相关的知识理论，本论文在参考国内外的项目风险管理相关的期刊文献，
领域范围内的专业论文和现代项目管理书籍中的结构化的先进的知识与产业体系后，结合当前
项目的具体情况，确认了了本论文的研究主题。管理方法计划采用瀑布式与敏捷式结合的开发
方法，所以对应的风险管理方法无法照搬瀑布式的风险管理方法来进行管理和监测。 
1.3.2 研究方法与技术路线 
本论文采用实际案例研究，结合项目本身的情况，采用的研究方法如下陈列，技术路线如
图1.1 所示。 
(1) 文献调查法 
本论文研究过程中使用文献调查法对项目风险管理基础理论进行概述；理解项目风险管理
的具体步骤与项目风险管理方法在不同项目中的适应性以选择适应当前项目的最优秀方案。阅
读案例，期刊，论文通过对比背景寻找比较好的风险管理方法。使用项目管理方法分析当前项
目的行业发展方向与管理现状和存在问题。 
(2) 头脑风暴法 
本论文研究过程中使用头脑风暴法对项目风险进行识别。邀请团队成员成立头脑风暴讨论
小组，组织工作坊。按照开发流程一同讨论项目从立项到运营的过程中间可能会出现什么问题。
通过头脑风暴法集思广益能识别到更多潜在风险帮助项目做好风险规划。 
(3) 德尔菲法 
本论文研究过程中使用德尔菲法对项目风险进行识别。邀请的专家范围从单位内部扩展到
外部，与专家成立风险分析评估小组，基于前期总结出的风险清单进行细致分类与筛选。筛选
出风险等级较小的风险因素，整理出一级风险与二级风险并关联风险因子，帮助项目进一步的
梳理风险结构。 
(4) 层次分析法 
本论文研究过程中使用层次分析法创建风险评价模型，基于风险结构，使用1-9 标度法两
两因素对比演算评价矩阵计算最大特征值分析一致性，得出每项风险因素的权重，选出对项目
东北大学硕士学位论文 
第1 章 绪论 
 
- 6 - 
成功影响较大的九个指标，针对这九个指标再进行打分，最后得到评价矩阵计算结果。 
(5) 模糊评价法 
本论文研究过程中使用进行模糊评价法对风险进行评价，隶属函数构建得出评价矩阵并对
评价结果进行分析，以确保评价性质由定性转至定量。 
 
图1.1 论文技术路线 
Fig.1.1 Technology road map 
1.3.3 论文结构 
第一章，绪论。本章内容包括 4 小节，先介绍了项目开展的原因和意义，通过阅读文献和
期刊了解国内外现状，结合现状选取可用的技术方法，并规划出技术路线来完善研究内容。 
第二章，项目风险管理理论与方法概述。本章内容包括 5 小节，对风险的概念，分类，特
点以及项目中会使用到的头脑风暴法，德尔菲法以及风险评价会使用的层次分析法和模糊评价
法作以介绍。 
第三章，DF 银行外汇交易数据报表平台项目概况 。本章内容包括 5 小节，对DF 银行外
汇交易数据报表平台项目进行了介绍，对主要建设内容和组织结构以及管理机制以及所面临的
东北大学硕士学位论文 
 
 
- 7 - 
一些问题做了陈述。 
第四章，DF 银行外汇交易数据报表平台开发项目风险识别。本章内容包括 5 小节，运用
课堂或书本中学习到的项目管理与项目风险识别的方法以项目当前的实际开发计划和相关规
定作为依据，头脑风暴结合专家定评的方式进行项目风险识别，最后汇总成项目风险清单。 
第五章，DF 银行外汇交易数据报表平台开发项目风险评价。本章内容包括 3 小节，结合
上一章节中得到的项目风险清单，对建立风险评价模型与项目指标评价体系，组织专家组进行
打分的过程进行了细致介绍。 
第六章，DF 银行外汇交易数据报表平台开发项目风险的控制 。本章内容包括 3 小节，结
合上一章节得到的项目风险评价结果一一进行策略分析提出健全的措施，并修订项目管理制度。 
第七章，结论与展望。 
 
 
 
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 8 - 
第2 章 项目风险管理相关理论与概述 
2.1 项目风险概念及分类  
2.1.1 项目风险概念  
项目风险一般被认定为是项目中可能会发生的，一种不确定的事件或者条件，一旦发生会
产生不良影响否则应该定义为机遇或者机会。不良影响具体指对项目的目标（进度，成本，质
量，范围）会造成影响，也会对组织的目标（声誉，财务）造成影响。在PMP 中，风险被定
义为一种不确定的事件或者条件，一旦发生，对项目的目标（进度，成本，质量，范围）会造
成影响，也会对组织的目标（声誉，财务）造成影响。通过项目风险管理，一方面想达到减少
可预知风险对项目进度、质量、成本的影响的目的，同事也想提高项目成功的机率，减少项目
整个过程中的不确定性，有利于计划的准确性；缓和及转移风险，另一方面对未预测到的风险
定义出快速上报流程，以通过管理的方式将风险可能产生的影响控制到最小，使项目达到预期
的结果。 
敏捷项目管理相比较于一般项目管理最大的区别就在于，敏捷项目拥抱变革的口号，所谓
拥抱变革换个角度去阐述也可以表达为项目本身并不会去细致计划每一个交付物。风险管理是
一项旨在评估、减轻和监测风险的活动。许多敏捷使用者认为，敏捷项目的风险管理过程与传
统项目没有太大区别。虽然在敏捷中这个过程可能更轻松一些，但是查找，筛选，排序和创建
风险解决方案的步骤仍然接近传统项目。 
2.1.2 项目风险分类 
项目的参与方不同，项目的背景不同，风险的分类方式各有不同。从影响范围上看可以分
局部的和具体的，从影响时间上看可以分长期的和短期的。根据文献研究整理，目前没有发现
对项目风险由统一的分类方法。 
袁世东在建筑项目中通过经验总结的方式归纳了目前项目中存在的经济风险、组织管理风
险、技术以及自然风险，通过这些风险梳理了其中存在的建筑项目风险管理中存在的问题及解
决方法、其中针对风险管理机制的不健全与风险管理意识不强和缺乏必要学习与研究的问题找
到了对应的方法[23]。赵子林和王金亮通过调研与专家分析法得出数据中台项目中，风险包含过
程管理风险，技术风险，组织自身与外部的风险[24]。马健研究了主数据管理的项目风险，并得
出结论风险有组织和管理风险，人员和知识风险还有系统和数据风险[25]。庞宏秋研究了软件实
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 9 - 
施的项目风险，并得出结论风险有财务风险，技术风险，管理风险，外部风险[26]。刘馨远在大
数据研究项目中提到大数据项目可能包含数据监听方面的风险，隐私被侵犯的风险，法律监管
方面的风险[27]。朱紫涵在海外施工项目中通过总结的方法归纳出了项目中可能存在施工风险、
社会文化风险、汇率波动风险[28]。蔡小路在信息化项目中研究得出项目的问题有人员和知识风
险，技术风险与组织管理风险[29]。冉孟超在银行风险管理风险中研究得出项目的问题基础设施
风险，系统架构风险，开发项目风险，运维管理风险[30]。张莉在软件项目中研究得出项目的问
题有环境风险，管理风险[31]；童泽华在数据中心迁移项目中研究得出项目的问题有有技术风险，
进度管理风险，质量保障风险，数据安全风险，分包商风险，业务连续性风险，甲方客户风险
[32]。骆鉴通过经验总结在金融业信息化风险管控中有的问题为社会面基础环境不健全，治理机
制不健全，科技风险不健全，主动防范工作不足[33]。具体统计如表2.1 所示。 
 
表2.1 不同类型项目中的风险 
Table 2.1 Risk in different kinds of project 
项目类型 
项目风险 
相关性 
PPP 项目 
金融风险、技术风险 
中 
建筑项目 
经济风险、组织管理风险、技术以及自然风险 
低 
数据中台 
管理风险，技术风险，组织自身风险，组织外部风险； 
高 
主数据管理的项目风险 
组织和管理风险，人员和知识风险、系统和数据风险； 
高 
软件实施的项目风险 
财务风险，技术风险，管理风险，外部风险； 
中 
大数据研究项目 
数据监听方面的风险，隐私被侵犯的风险，法律监管方面的
风险； 
高 
海外施工项目 
施工风险、社会文化风险、汇率波动风险； 
低 
信息化项目 
信息化项目顶层设计缺乏长远性 
信息化项目品目多，个性化需求大，评审难度大 
信息化项目业务流程复杂，存在双重逻辑 
中 
银行风险管理 
基础设施风险，系统架构风险，开发项目风险，运维管理风
险 
高 
软件项目 
环境风险，管理风险 
中 
数据中心迁移项目 
技术风险，进度管理风险，质量保障风险，数据安全风险，
分包商风险，业务连续性风险，甲方客户风险 
中 
金融业信息化风险管控 
社会面基础环境不健全，治理机制不健全，科技风险不健
全，主动防范工作不足 
中 
2.1.3 项目风险的特点 
项目的风险特点大多可以概括为以下几类，无论瀑布式项目也好，敏捷类项目也好。 
(1) 客观性，项目风险总是存在的，风险是客观存在的事实。即使风险可以被提前预知也
无法完全避免其的发生进一步转变为现实。风险因其组成的各种因素的存在而存在，只要影响
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 10 -
风险发生的前置条件都能达到风险发生的要求，风险就会发生，对项目产生影响。虽然管理层
一直希望能了解并控制风险，但直到现在也只能在一定的条件下通过适当调整和改变风险发生
的前置条件，以降低其发生的概率，减少风险对项目造成损失程度，但要完成消除所有风险是
不可能的。 
(2) 普遍性，任何项目，无论是工业工程项目还是信息工程项目都会受到社会监督，自然
环境和技术约束等内外部条件的限制与影响，而这些因素的变化与不确定性必然会导致风险的
存在。只要是项目都会有风险，具体风险可能不同，但是风险必然会存在。Elmar Kutsch 在论
文研究了风险控制清单对于项目成功与否的影响，并提出除非我们是神一样的存在，不然缺少
足够的知识，信任和舒适的环境我们很难确保项目的成功[34]。 
(3) 不确定性，常规来说项目的目标是固定的，但受到客观性或者普遍性的影响会有调整
的可能性，常规来说项目的时间也是固定的，但是会因需求调整而改变，如项目的周期缩短或
者延长，项目团队几乎是时刻变动的只能确保核心成员是相对固定。简而言之，项目就是时刻
变化的，是动态存在，这给项目风险带来了很多不确定性。任何一种风险的发生是错综复杂的
不确定因素与先决条件交织作用互相影响而生成的，是一种随机现象。也因为采用敏捷的方式
进行管理，需求需要随时调整，每个需求可能会牵扯的风险内容和风险严重程度都有可能会有
细微的差别，不确定的可能性更大。 
(4) 多样性，项目风险的多样性受项目的大小和周期的影响，既可能会有几种类型的项目
风险。项目的范围扩大甚至能使项目的风险类型产生结构化的联系。项目的规模与项目的多样
性呈正相关关系，项目的规模越大，牵扯的相关方越多，风险的类型越多样化。其中还包含人
员与文化的多样性，因为引入的考察专家团队不乏美国人，在美印度人，不同的文化会对风险
由有不同的了解最终得出不同的结论。 
2.1.4 项目风险管理的主要步骤 
风险管理是贯穿项目开发始末的一项重要任务，柳小军提出项目风险管理的方法，规划风
险管理计划，科学进行识别风险，应用定性和定量风险分析方法并制定风险应对计划，最后实
施风险监控，合理的风险规划能让风险管理者提前主动的“规避”风险[35]。无论是传统项目还
是现在的敏捷项目，项目风险管理的步骤无明显变化，依然可以归纳为风险识别、风险评估、
风险计划、制定风险应对方案和风险监控这 5 个步骤。 
(1) 风险识别：广泛的收集与项目相关的可变因素，在传统项目中，此环节在项目开展之
前，在敏捷项目中，此环节贯穿整个项目。有效的风险识别方法可以帮助项目管理团队对项目
流程进行有效的梳理，通过风险识别过程可以达到在前期进行合理的管控和引导的目的，虽然
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 11 - 
风险的发生无法控制，但风险的影响范围可以干预，尤其在IT 项目管理的领域中，项目开始
前期进行合理的规划会让项目获益，如Vuk Vujovi´c a,Deni´因当局的数字化进度提速而做出
研究进而得到的结论[35]。项目风险研究过程中为了保证研究的方法具有一定意义的创新型，计
划了解多种风险识别方法，通过与其他客观实物如项目背景加以比较，来达到对当前项目进行
风险管理有大致判断与评价。在风险识别的过程中最好对风险进行分类，可以提高风险评估的
效率。 
(2) 风险评估：基于已识别的风险进行风险评估，也说明了风险评估的范围仅能涵盖住我
们已知的风险，风险评估的主要任务是确定风险发生的概率与后果，通过评出的风险等级可以
为后续项目规划提供建议也给予选择风险应对方法提供了信息基础。在风险评估的环节需要重
点注意的是使用的评估方法一定要是定量的方法，在大型项目中尽量不要只选用主观性太强的
定性风险评估办法，如情景分析、工作访谈等。 
(3) 风险计划：在传统项目中，此环节在项目开展之前，在敏捷项目中，此环节贯穿整个
项目。按照评估后的风险结果，制定相应的风险管理进度表，结合项目开发计划的时间进度一
起给到项目团队，为后续的风险管理提供参考。黄梅香通过对项目的分析总结与回顾，得出了
需要使用风险登记册来记录风险，并且还需要记录引发风险的根本原因[37]。 
(4) 制定风险应对方案：识别到风险就要提供对应对的解决方案，不然识别没有任何意义，
具体的风险应对方法在第2.4 章节有详细介绍，对风险的应对，无论是传统项目还是敏捷项目
都是贯穿生命周期的一项活动。风险管理不仅要落实到人，在公司层面也要培养风险应对的氛
围。 
(5) 风险监控：同理与风险应对风险监控也是无论在传统项目范畴还是敏捷项目范畴都是
贯穿生命周期的一项活动。风险的监控包含了两部分，一部分是对风险是否发生或就是否要发
生进行回顾与监控，还有就是对风险控制的方法是否按时尽责的监控。两者同样重要，时间和
程度都能影响对项目的控制力度。 
2.2 项目风险识别主要方法 
2.2.1 头脑风暴法 
头脑风暴法的定义：头脑风暴（Brain Storm）法简单来说就是提供一个让团队的全体成员
能够自由自在地提出自己的主张和想法的一种会议方式，它是寻找解决问题时常用的一种方法。
通过头脑风暴法，主要是为了集思广益，通过发挥集体的智慧，来提高群体决策的创造性，也
通过增加成员的参与感来使得决策能以更高的质量完成。头脑风暴法比较鲜明的特点是，需求
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 12 -
参与者畅所欲言，来参加头脑风暴的每一个参与者都要认真对待任何一种意见，不带有预设态
度的去鼓励大家表达观点不做评价，尤其是判断性的评价，以免去对讨论造成负面影响，后续
汇总的过程中可以进行点评。头脑风暴法的优点是善于发挥相关专家和分析人员的创造性思维，
可对项目风险进行全面的识别。头脑风暴法使用时的禁忌是批评创意与想法与在创意或想法提
出时就做判断。 
头脑风暴法的使用方式：组织团队内外的人员聚集先定义主题再开会讨论，参与人员轮流
提出主张和想法；第一步，明确问题：先把需要解决的问题清晰的表达出来，并把问题的方案
定义为会议的中心。第二步，思考问题：提出问题之后，给大家一定的思考时间，适当的可以
借助思考问题的工具。第三步，提出创意与想法：每个人先提出自己的想法、创意或意见的同
时每个人的想法需要被收集并记录。如果会议时间预留较长，可以组织第二个环节进行创意与
想法的投票，并选取出相对靠前的几个想法，进行深入的讨论。也可以对大家的创意与想法进
行整合、合并或归类，找出创意与想法最多或者最有可能成为结果的一类。 
头脑风暴法与德尔菲法的不同之处在于，德尔菲法为了降低主观因素会匿名且多轮的反复
讨论，而头脑风暴法要求实时的互动，尽可能的在同一时间段集中讨论[38]。 
头脑风暴法与传统会议的不同之处在于，会记录所有的提议而非只记录会议的决议。且不
同于传统会议限定人数，头脑风暴法鼓励尽可能多的人会参与到会议讨论的过程中，这样能得
到更多的视角和观点。 
2.2.2 德尔菲法 
德尔菲法，也称为估计-谈话-估计技术（ETE），是一种系统的、定性的预测方法，通过
多轮提问收集专家组的意见。德尔菲法由数学家 Norman Dalkey 和 Olaf Hermes 在 1950 年
代开发，大约在冷战时期，用于预测技术对世界的影响。Delphi 方法依赖于对特定主题有知识
的专家，因此他们可以预测未来情景的结果、预测事件的可能性或就特定主题达成共识。德尔
菲法展开的前提是要选定成立项目风险组，并选定专家与其他项目相关人士，德尔菲法由几轮
书面问卷组成，让专家发表意见。特别注意的是这里要采用匿名问答的方式征集意见，在专家
回答每一轮问卷后，主持人收集所有答案，汇总后匿名反馈给每位专家。然后，专家审查总结
报告，再度匿名反馈同意或不同意其他专家的回答，这一环节让他们有机会根据他们从总结报
告中了解到的内容提供更新的意见。当预测达成或者接近共识时，德尔菲法就完成了。德尔菲
法的使用方式：先确定问题和目标定义，要解决的问题以及使用 Delphi 方法想要实现的目标。
并选择一组专家和一名主持人，专家可以是组织内部或外部的个人。协调人应该采取中立的立
场并且是具有研究和数据收集经验的人。 
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 13 -
主持人为专家提供第一份问卷。第一轮的问题类型通常是开放式的，因为它允许专家集思
广益。主持人收集问卷中的所有答案，并向专家分发答案摘要报告。在总结报告中，专家的身
份保持匿名，以鼓励他们自由发表意见。第二份问卷应通过分析第一轮观察到的答案来创建。
确定答案之间的任何相似之处并消除不相关的内容，以便第二份问卷可以朝着专家内部达成共
识的方向发展。专家回答第二次问卷时，他们的意见可能保持不变，也可能在阅读第一轮总结
报告并了解其他专家的意见后改变意见。第二份问卷完成后，主持人向专家分发第二份回答总
结报告。第三轮问卷：按照与第二轮相同的思路进行第三轮。第三份问卷应通过分析第二份问
卷的答案来创建。专家将根据他们对第二轮答案总结报告的意见回答第三份问卷。如果您觉得
专家之间达成了足够的共识，并且他们的所有预测彼此一致，您可以继续进行额外的调查问卷
或选择停止。 
德尔菲法的特点：是通过组织项目相关的行业内专业人士以及其他项目的管理专家聚集讨
论形成专家组，依靠不同专家的知识技能以及相关的经验对项目每种风险因素发生的概率进行
打分并评价。但需要注意的是专家评分法具有主观性和局限性，原因是因为每位专家的专业知
识程度和行业经验各不相同。德尔菲法中征求意见是匿名进行的，这有助于排除些许非技术性
的干扰因素。德尔菲法要反复进行多轮的询问、反应，这有助于逐步披沙拣金，得到稳定的结
果。工作小组要对每轮的专家咨询结果进行统计、总结，这样可以综合不同专家的意见，不断
求精，最后形成统一的结论。项目还会采用如下方法，但因不是主要且突出的识别方法，所以
没有展开。 
2.3 项目风险评价主要方法   
2.3.1 层次分析法  
层次分析法AHP（Analytic Hierarchy process）是一种定性与定量相结合的决策工具，根据
谢亚妮在企业金融分析模型的项目研究中，AHP 被描述为一种决策方法，适用于将总目标分
解为多个子目标，并基于目标之间的关联关系来构成多层次的分析结构模型，适用于含有多个
目标的综合评价[39]。赵彩，许大炜使用该方法评估了网络涉密信息风险，提高了评估准确率，
通过将风险分解建立三层指标使得结果规范化[40]。吕杏用基于三角模糊数的模糊AHP 方法分
析设备采购的前、中、后期风险，提高设备采买的速度[41]。程满亚，孔祥峰使用层次分析法分
析了H 学校的财务风险情况，并基于指标层的权重排序结果给出对应的管理建议[42]。赵现胜
在融资租赁项目风险中使用AHP 法将风险评价指标体系分为至了四层，在MATLAB 的帮助
下计算得出权重[43]。综合来说，层次分析法 (AHP)是一种解决问题的数学工具，可以将多目标
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 14 -
待决策的复杂问题进一步分解为多个准则，对这些准则进行分层，利用定性指标模糊量化的方
法计算各层次的权重，从而将排序作为优化决策的目标，是在了解问题的结构以及管理者在解
决问题时面临的真正障碍之后创建的一种方法，是建模比赛中比较基础的模型之一，层次分析
法能主要解决评价类的问题。如选择哪种方案最好，哪位员工表现最好等。根据不同的目标和
不同的功能将与决策有关的要素划分为目标、标准、计划等层次，确定上述层次结构中相邻层
级之间的相关程度。通过运用成对比较判断矩阵和矩阵运算的数学方法，确定该级别相关元素
对于前一级别的给定元素的重要性顺序。计算各层元素相对于系统目标的组合权重，并进行总
体排序，确定递进结构图中底层各元素影响总体目标实现的重要程度，为风险管理提供依据。 
2.3.2 模糊评价法 
模糊综合评价，该方法根据隶属度将定性评价转化为定量评价，利用模糊数学对受多种因
素制约的事物或对象进行整体评价。是一种基于模糊数学的评价方法。模糊综合评价法是以模
糊数学为基础的，由美国数学家Lotfi A. Zadeh 在1965 年提出[44]。其基本原则是从评价主体根
据具体情况给出的评价量表中，一致地、不矛盾地衡量价值，得到大多数人都能接受的评价结
果，为正确决策提供必要的信息；这种综合评价方法综合了对受多因素影响的对象的评价，将
定性评价转化为定量评价[45]。 
2.4 项目风险应对主要方法    
项目风险的应对方法基于项目的开展进程可以分成两个部分，在项目开展之前或者项目风
险发生之前识别出风险即为主动式防御。但总有风险会无法规划出来，对应这些风险也需要讨
论出可以采用的风险评价方法以及可以选用的应对方法。主动风险控制也可以一步拆分，根据
项目时间节点的紧迫程度将风险可以采取的条件进行进一步归类选择更为合适的风险应对方
法。 
2.4.1 主动式风险应对 
风险转移是将风险转移给参与项目的其他个人或组织，通过签订合同或协议，将部分损失
转移给一旦发生危险事件有能力承担或控制项目风险的个人或组织身上，把威胁造成的影响连
同应对责任一起转移给第三方的风险应对策略。采用风险转移策略，需要向风险承担者支付风
险费用。风险转移可采用多种工具，包括（但不限于）保险、履约保函、担保书和保证书等。
把不擅长的工作分包给更专业的团队就是转移风险的例子。 
风险缓释是通过缓和的方式减轻风险，降低风险发生的可能性或减轻风险的负面影响，以
东北大学硕士学位论文 
第2 章 项目风险管理相关理论与概述 
 
- 15 -
达到降低风险的目的。通过事先消除可以催化风险发生的条件来降低风险发生的概率，或采取
预防措施减轻风险发生时的损失。风险缓解的应用案例包括采用更简单的工艺，更可靠的流程，
增加设计冗余，更多的测试或选用更熟悉的供应商。 
风险规避适用于当项目的潜在风险能造成严重的后果且影响面积广，别无选择的时候，需
要提前进行项目风险管理。是一种相对消极的主动式风险控制方法，主要是为了避免造成不得
不放弃项目的可能性发生。黄晓斌指出，风险规避是相对保守的风险应对对策，适合应对超过
承受能力的风险[46]。 
风险自主接受适用于当在规划风险措施时通过分析得出采用其他的风险规避或者缓解的
方法造成的成本超过了风险产生影响带来的成本的结论时可采用风险接受的应对策略。是有选
择性的承担风险的后果。项目团队决定接受风险的存在，而不提前布置对应措施的风险应对策
略。最常见的接受策略是建立应急储备，安排一定的时间、资金或资源应对风险。 
2.4.2 被动式风险应对 
风险上报适用于在项目开展之后，我们发现项目风险不应该由此项目承担，或者是超出了
项目管理层可以控制的范围，这个时候会采取项目上报的方式，上报给更高层的管理者来对应
的做决策。上报之前也要尽职尽责的将风险产生的背景和项目可能带来的影响解释清楚，确保
风险接受方理解并接受风险之后，方可把风险从风险清单上去除。 
风险被迫接受适用于风险在一开始规划时候没有意识到，但是风险发生了还要应对。项目
规划时候要避免这种情况对项目产生不可预计的后果，所以会提前规划一部分费用额度来应对
万一。 
2.5 小结    
为了辅助项目中的风险可以尽量全面的被分析、被评估、被控制，深入研究了风险的概念
与分类方式以帮助后续进行风险分析时候项目团队可以更准确的描述风险，同时研究了最为重
要的风险管理的步骤并对其中关键步骤，风险分析、评价和应对可选用的方法进行了分析。
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 16 -
第3 章 DF 银行外汇交易数据报表平台项目概况  
3.1 DF 银行外汇交易数据报表平台开发项目简介    
3.1.1 DF 银行外汇交易数据报表平台开发项目背景 
DF 银行成立于1769 年，为了走入全面金融数字化，从20 世纪开始每年在IT 上的投资有
10 亿美金之高。2000 年，DF 银行在杭州成立了属于自己的信息部，广泛招纳国内IT 人才，
借助国内的高效率人才完成银行的数字化转型，对整个集团进行科技赋能。 
项目开展之前，DF 银行使用的系统是上世纪80 年代的老旧系统，不仅运行速度慢而且界
面不友好，对于前台用户来说操作难度大。对于后期运营人员来说老旧系统的影响点在于需要
使用的开发语言过于古朴，对开发人员的技术能力要求高。而开发人员的流动率高，而老旧的
系统也增加了短时间内可以让新员工直接操作的难度，所以系统不仅不好用也不好维护。过去
可能需要花费数周时间和大量文件(超过100 页)检阅才能开立一个新账户。而通过这个数字化
转型，开一个新账户大约仅需要30 分钟。新系统能改善的不仅仅是开户这一个动作，也包含
开会，会前员工常需要从多达26 个不同的系统中提取客户的信息，并整理成报告，其中光是
收集信息就需要三到四个小时。收集信息需要超长的时间的原因在于现在是一个信息爆炸的时
代，互联网让过去没被曝光的数据得以暴露在公众的视野里，信息的准确性和关联性受到高度
重视。而新系统支持，一键生成报表。在这些或微观或宏观的背景下，展开了这次信息化整改
项目。此次参与的DF 银行外汇交易数据报表平台开发项目归属于DF 银行数字化升级项目中
的一个分支。DF 银行数字化升级项目是公司战略项目，项目周期暂定为5 年。最终目标是完
成对DF 目前在用的全部的2010 年前开发完成的系统进行重构。打造一款可以提供优质服务
的自动化低代码数据报表平台。公司对此项目定义的战略目标是让信息更透明，让决策更高效。
以此为卖点，维稳住现有客户，拓展新客户，帮助公司提高收入，节省开支。 
3.1.2 DF 银行外汇交易数据报表平台开发项目目标及主要建设内容 
(1) 项目的目标 
在规定的时间和预算内完成需求的开发与交付。交付的内容有：通过用自动化取代人工重
复的工作以达到降低成本增加效率；通过数据收集，数据清洗，对应的建模生成报表提供描述
性、诊断性、预测性甚至决策性建议；通过数字化打破组织的孤岛，让信息关联，再分析相关
变量筛选贴合的模型，来发现潜在的机会让业务板块逐渐完善。 
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 17 -
产品实现角度目标分为三个，技术目标，由开发部门负责、业务目标，由产品部门负责、
营收目标，也由产品团队负责。业务目标和营收目标也会转化为技术目标并落地到项目的开发
计划中去。基于商业画布与各组织的项目目标分化得出建设内容的框架，如表3.1 所示。 
表3.1 DF 银行外汇交易数据报表平台开发目标 
Tab.3.1Target of DF FX Data exchange platform project  
目标类别 
目标内容 
拆分建设方案 
业务目标 
提高流程的自动化占比率至50% 
完善合规流程管理 
接入五家数据供应商。 
梳理流程并落实自动化RPA 流程。 
对当前的开户、销户、交易意向创建等流程开发自动化工具。 
营收目标 
客户留存率至80% 
客户增长率达到5% 
了解基金、投资经理人关注的数据及报表样式。 
了解重要的客户想实时获取的报表。 
技术目标 
完成低代码报表平台的搭建 
建立数据湖 
梳理现有数据内容与数据来源。 
将无效数据清理干净。 
将有效数据迁移到数据湖。 
定义数据的接入类型，即数据接口API。 
接入数据。 
对数据进行结构化存储，使用合理的数据库结构模型。 
80%按照客户与用户的想法展示数据，留20%作为可拓展空间。 
(2) 项目的主要建设内容 
结合项目商业画布，基于业务流程，定义出来了的架构图。架构图要有全局观，不然会不
断反工并对进度造成严重影响。开发顺序为前期主要负责开发新的系统实现对旧系统的功能性
替代更新，后期也陆续开展前沿技术的研究与实施。 
 
图3.1 DF 银行外汇交易数据报表平台系统架构图 
Fig.3.1 Technical architecture of DF FX Data exchange platform project  
为了提高流程的自动化占比率需要接入更多的数据供应商。经过产品组内部讨论，DF 银
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 18 -
行决定接入国际知名的数据服务商，同时也是业务运营商，swift、alert、fx-all。确认好了数据
的来源后，需要梳理与定义数据的接入类型，即数据接口API。同时要定义传输数据的频次，
是否可以支持多种编码格式等系列问题。随着数据流的滚动也要考虑下游数据库的选型与匹配
能力，例如是否可以满足客户对数据及时性的要求。数据存储的过程中要遵循DF 系统开发的
要求做好日志信息记录，用户信息记录，业务数据监控和运营数据监控。 
最重要的展示层，将收集到的数据以报表的形式展示给终端用户。在数据的接入过程中需
要对数据进行敏感数据清理、数据加密、数据分类、同类型数据分类、同一应用场景分类、异
常情况提示、空白格校验、大小写校验等处理。在数据展示的过程中需要对数据进行敏感信息
校验、权限范围校验、数据范围校验、下拉清单、真假校验。并实现日报月报自动更新、在内
部用户有需要的时候，从数据库中查询出最新交易状态，完成DF 银行外汇交易数据报表平台
的开发与生产部署，以达到用最短时间内能帮助业务人员查询到最新最全的数据的目的，并及
时的更新数据状态，提供报表和数据看板等功能。 
 
表3.2 DF 银行外汇交易数据报表平台开发内容 
Tab.3.2 Project content DF FX Data exchange platform project  
序号 
任务 
开始时间 
结束时间 
1 
项目管理组织与制度的确立 
  
 -  
1.1 
确立项目团队 
2020/6/1 
2020/6/10 
1.2 
会议与沟通逻辑与平台 
2020/6/10 
2020/9/7 
1.3 
技术方案设计 
2020/7/9 
2020/9/6 
1.4 
定制人员招聘计划 
2020/8/15 
2020/9/3 
2 
基础设备采买 
  
 -  
2.1 
软件权限 
2020/6/10 
2020/7/9 
2.2 
云端存储权限 
2020/6/10 
2020/7/9 
2.3 
硬件采买 
2020/6/10 
2020/7/9 
3 
接入五家数据供应商 
  
 -  
3.1 
牵头对接供应商 
2021/6/10 
2021/9/7 
3.2 
定义数据的接入类型 
2021/9/7 
2021/12/5 
3.3 
清理无效数据 
2021/9/7 
2021/12/5 
3.4 
迁移有效数据到数据湖 
2021/12/5 
2022/3/4 
4 
梳理现有数据内容与数据来源 
  
 -  
4.1 
清理无效数据 
2021/6/10 
2021/9/7 
4.2 
整合数据结构 
2021/6/10 
2021/9/7 
4.3 
迁移有效数据到数据湖 
2021/6/10 
2021/9/7 
5 
开发报表 
  
 -  
5.1 
收集基金、投资经理人关注的数据及报表样式需求 
2022/6/10 
2022/9/7 
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 19 -
5.2 
开发基金、投资经理人关注的数据及报表样式需求 
2022/6/10 
2022/9/7 
5.3 
收集重要的客户想实时获取的报表需求 
2022/8/10 
2023/2/5 
5.4 
开发重要的客户想实时获取的报表 
2022/8/10 
2023/2/5 
5.5 
低代码的报表编辑器 
2022/6/10 
2022/12/6 
5.6 
迁移 
2022/6/10 
2022/9/7 
5.7 
正式上线 
2022/6/11 
2022/9/8 
6 
梳理现操作流程 
  
 -  
6.1 
收集需求自动化开发的流程 
2023/6/10 
2023/9/7 
6.2 
识别流程中可以自动化开发的节点 
2023/9/7 
2023/12/5 
6.3 
整合资源做自动化开发 
2023/12/5 
2024/3/3 
6.4 
迁移 
2024/3/3 
2024/5/31 
6.5 
正式上线 
2024/5/31 
2024/8/28 
7 
非功能性需求开发 
  
 -  
7.1 
数据结构调整 
2020/6/16 
2020/9/13 
7.2 
系统架构调整 
2020/6/16 
2020/9/13 
8 
运营与维护 
  
 -  
8.1 
整体上线 
2024/6/16 
2024/9/13 
8.2 
增加补丁 
2024/6/16 
2024/9/13 
3.2 DF 银行外汇交易数据报表平台开发项目的组织结构 
公司层面的组织结构分为三个层级，决策层，管理层和执行层。如图3.2 所示。 
(1) 决策层为公司法人需明确公司级别的愿景，明确中长期计划，审批预算并定期回顾项
目进度，根据市场情况提出改进建议，宏观调控资源。 
(2) 管理层是决策层的下属机构，由使用部门即业务部门指认项目与产品经理，产品经理
负责明确产品服务对象，定义产品的愿景、合理分配资源，控制整体预算，项目经理负责统一
协调管理，控制周期预算，把控项目进度。 
(3) 执行层由各职能部门派专人对接项目并负责把项目工作计划变为具体执行内容，三个
层次相对独立。 
 
 
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 20 -
 
图3.2 DF 银行外汇交易数据报表平台开发项目组织架构图 
Fig.3.2 Project O-chart of DF FX Data exchange platform project  
项目团队的组织结构如图3.3 所示，是矩阵型组织结构。业务团队、IT 产品经理和项目经
理从管理结构上相互独立，但是从业务线又彼此联系，即业务线有业务线的主管，但管理也有
管理线的主管。各自管理人员有所重叠，但管理内容独立。团队按照工作职能划分可分为，业
务分析师、开发、测试 ，以矩阵型组织结构的方式进行整体项目实施及运行。此类管理方式
需要业务分析师经理，开发经理与测试经理之间有良好的业务工作关系，项目成员需要分别协
助两至三个经理处理工作，这点与大部分的项目组织结构有区别。浙江大学的刘婧的论文项目
中指出产品经理应有权力决定开发团队[47]。 
 
图3.3 DF 银行外汇交易数据报表平台开发项目小组组织架构图 
Fig.3.3 Group o-chart DF FX Data exchange platform project  
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 21 -
从业务的维度，一个业务分析师负责一个至两个项目，对接三个开发和三个测试，一个开
发对接一个测试，但是业务分析师对于开发与测试没有绩效考核与升职的决定权，各功能模块
有自己的汇报线。汇报线上的经理对于项目内容没有深入了解，对于员工项目中的表现没有直
接感受。功能模块汇报线上的经理的工作内容重点在于项目上的人力资源与平衡。因开发与测
试员工的编制多为非公司直签的外包工种，福利保障与加班等薪资条件一般，还会因进度赶工
时而加班。故人员流动性大。而功能模块汇报线上的经理的工作内容在于下属的管理与产品开
发的技术选择和产品基础知识的留存。基于管理制度，定义每个角色的具体的工作内容，以免
出现工作内容没有涵盖上的风险发生。 
3.3 DF 银行外汇交易数据报表平台开发项目风险管理情况   
DF 银行外汇交易数据报表平台开发项目是DF 在中国境内第一批同时使用瀑布与敏捷两
种项目管理办法来进行管理的项目。适用当前项目的风险管理办法在项目立项前并未有成型的
规划，仍以传统瀑布式项目中的风险管理办法作为基础进行管控。 
3.3.1 DF 银行外汇交易数据报表平台开发项目风险管理组织 
公司层面，在公司执行董事的领导下，设立了风险管理委员会，是公司风险管理的最高层
级，对执行董事负责。在经理层面下设风险控制部门，是风险管理的主管部门，对可能对公司
业务产生影响的风险进行分析并统筹制定风险管理流程、管理风险控制措施的部门；合规管理
部门，是辅助风险管理进行风险监督的部门，对各国法规进行分析，识别现有工作流程中的风
险并提出修改意见；业务部门，是风险管理的责任部门，本着谁主管业务谁负责风险的原则，
对风险进行管理。 
项目层面，管理层为更好的规划风险、控制风险，决定成立风险管理小组。风险管理小组
仅对当前项目负责，汇报给产品经理。风险管理小组的工作职责位包含但不限于在项目开始之
前规划风险、讨论风险清单列表、对风险等级进行打分与排序、在项目的实施到运营阶段对已
识别的项目风险进行管控，不断完善项目风险清单。项目风险管理小组的工作重点是保证在前
期风险规划的阶段考虑角度全面，能涵盖住尽可能多的风险。为了满足这一点要求，小组成员
必须包含以下组成成员，有参与项目核心设计与开发，便于在做顶层设计的时候考虑风险。有
后期帮忙组织协调，方便了解风险确认的背景。有不参与此项目，但是经历过很多项目的外部
专家，以提供不同的角度。最重要的是有管理层参与，能更好进行消息的跨级传递与进展推动。
人数上，小组总人数需要在10 人以上。选定产品经理为风险管理小组组长，项目经理为副组
长，特聘内外部专家。最后确定项目风险管理小组成员如表3.3 所示。 
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 22 -
 
表3.3 DF 银行外汇交易数据报表平台开发项目风险管理小组 
Table 3.3 Risk management team of DF FX Data exchange platform project 
人员 
部门 
项目风险管理小组内职责，关注重点 
June He 
产品经理 
组长，所有风险 
Mike Li 
项目经理 
副组长，交付，管理相关 
Julia Luo 
开发专家，架构师 
副组长，技术风险 
Jesus Who 
合规部门 
专家，政策研究 
Guess What 
风控部门 
专家，市场情况 
Shannon When 
风控部门 
专家，市场情况 
Ann Meng 
测试 
成员，需求质量，质量验收 
Anita Su 
测试 
成员，需求质量，质量验收 
Sussie Xu 
业务分析师 
成员，需求质量，用户沟通 
Jack Ma 
业务分析师 
成员，需求质量，用户沟通 
Tony Liang 
开发 
成员，需求质量，开发 
Blue Song 
开发 
成员，需求质量，开发 
Cloudy Gu 
客户经理 
成员，各自业务线 
李丽 
客户经理 
成员，各自业务线 
Bao Bao 
业务同事 
成员，各自业务线 
Zeno Zheng 
敏捷教练 
副组长，组织结构，管理相关 
3.3.2 DF 银行外汇交易数据报表平台开发项目风险管理制度 
DF 银行外汇交易数据报表平台开发项目主体上需要遵循前期瀑布式开发项目的风险管理
制度。内容分为四部分，如表3.4 所示，较企事业单位不同的一点是，对于风险的控制与考核
与绩效并无挂钩，仅对处理突发重大风险的员工有表彰嘉奖。 
表3.4 DF 银行外汇交易数据报表平台开发项目风险管理制度 
Table 3.4 Risk management principle of DF FX Data exchange platform project 
章节 
基本内容 
总则 
明确以强化管理当前项目风险为目的。 
明确仅适用本项目，但可供其他相似项目做参考。 
明确主要内容与基本原则。 
组织与职责 
介绍了与本项目相关的风险管理组织与其主要职责。 
风险管理的基本流程 
管理内容包括，风险识别、风险评估、风险分析与应对、风险监控、突发风险
的处置、监督与改进。 
附则 
制度的制定与生效时间。 
3.3.3 DF 银行外汇交易数据报表平台开发项目风险管理流程 
DF 银行外汇交易数据报表平台开发项目的流程介绍如图3.4 所示，总共分五个环节，涉
及到四个组人员。第一环节是风险管理小组组织牵头收集信息并确立风险类型，第二个环节为
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 23 -
风险识别，这里包含了两个步骤及项目组成员进行头脑风暴针对项目的风险进行识别，第三环
节为风险评价与排序，第四环节为规划风险应对策略并制定应对方案形成风险控制手册。最后
进行风险跟踪与控制。需要强调的是风险管理贯穿了项目预研到项目结束，在项目的进行中也
有不断的发现风险并进行评价与规划应对策略的过程。 
 
图3.4 DF 银行外汇交易数据报表平台开发项目风险管理流程 
Fig.3.4 DF FX Data exchange platform project risk management process 
3.4 小结 
DF 银行外汇交易数据报表平台开发项目的立项背景是公司为了适应在大环境下的数字化
转型浪潮，完善企业的数字化布局，形成流程的数字化闭环，达到给客户提供便利的目的DF
东北大学硕士学位论文                   
第3 章 DF 银行外汇交易数据报表平台项目概况 
 
- 24 -
银行外汇交易数据报表平台开发项目是以此而开展的战略化项目中的一部分。项目的管理结构
分决策层，管理层和执行层，三个层级相对独立，每个层级内角色权责分明。项目管理方法采
用瀑布式与敏捷方法相结合的管理方式进行开发。
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 25 - 
第4 章 DF 银行外汇交易数据报表平台开发项
目风险识别 
4.1 DF 银行外汇交易数据报表平台开发项目风险识别依据        
4.1.1 风险管理计划 
基于DF 银行外汇交易数据报表平台开发项目所采取的特殊项目管理模式，瀑布与
敏捷式结合的管理方式，并结合当前的社会情况与背景，项目的风险管理分为两部分进
行。 
第一部分，搜集前期DF 内部信息化系统采取瀑布式开发项目管理方式时所采用的
风险管理方法等相关信息，结合当前项目的实施背景与实施计划调整预测出DF 银行外
汇交易数据报表平台开发项目可能会存在的风险因素清单，此过程由项目管理人员根据
项目的建设目标、建设内容、风险管理政策，编制《DF 银行外汇交易数据报表平台开
发项目风险管理计划》，成立项目风险管理小组并通过组织风险识别会议，收集头脑风
暴，小组讨论形成项目风险清单，通过德尔菲分析法完善风险的控制方法，再用层次分
析法建立风险评价模型形成《DF 银行外汇交易数据报表平台开发项目风险管理计划》。 
第二部分，在每个迭代的回顾会议中通过分析刚结束的迭代中出现的问题来进行总
结经验提炼出可能发现的潜在风险并讨论得出可以量化的改进及控制办法。再由小组票
选出前三个问题点，列入风险管理清单，不断的完善风险问题清单与对应的风险控制方
法。最重要的是在每次迭代会议中回顾是否有实施改进行为并有效避免问题的发生进行
风险控制。 
4.1.2 项目实施计划 
DF 银行外汇交易数据报表平台项目由业务方牵头立项，确定产品经理与项目经理，
并由业务方和IT 方共同选出项目小组成员，项目小组由产品经理，项目经理，交付经
理和一众分析师团队与研发团队构成，产品经理与交付经理有多年研发经验，和与之相
应的计算机编程知识，受过专业的信息化管理培训，为系统得使用保驾护航；同时，拥
有计算机开发经验，可以在开发过程中，协调用户方与开发方得关系，保证项目得顺利
进行，及时发现问题，项目经理虽没有研发技术背景，但是对于市场和业务有多年的洞
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 26 - 
察，可以帮助业务分析师一同分析也可以更好的协调项目的决策层，作为管理层的一员
对项目进度与质量进行监督。项目实施常规部分包含调研与设计阶段，编码和测试，运
行与验收培训与售后服务等项目得各个阶段，DF 信息化项目的年度交付与实施计划如
图4.1 所示。 
 
图4.1 DF 银行外汇交易数据报表平台开发项目年度计划图 
Fig.4.1 Yearly Implement plan of DF FX Data exchange platform project  
4.1.3 项目相关规定及办法 
项目开发要符合国家法则、公司的制度，依照国家网络安全相关规定和国家信息安
全等级保护制度的要求，金融信息服务提供商应遵循《计算机信息网络国际联网安全保
护管理办法》，开展信息系统安全管理工作。需要遵从各国数据管理条例，如欧盟的数
据通用保护条例，中国的个人信息保护法以及近期刚颁布的数据安全法；为了确保对开
发的内容保密，需要遵从与客户签署的保密协议；需要遵从投资合规风险建议，如敏感
国家（亚太区-中国-北京，中国-台北，韩国-首尔）的信息不能进入计算与统计。 
新的项目开发方法，需要遵循敏捷开发的12 条原则，分别是：要优先考虑客户满
意度，从尽快的开始交付最小价值的交付物到持续交付有价值的软件;以拥抱变化的态
度面对客户需求的变化在开发的任何一个环节，都要接受因原有需求不符合市场而需要
做出需求调整的空间，以免开发出不被使用的功能;采用短周期但持续交付的开发方式，
与客户进行高频互动，不断探索需求细节，明确开发方向;缩短业务同事与开发团队之间
的物理距离，使两个团队能够高效沟通，即促进开发团队更好地了解业务需求的使用场
景，同时也让业务团队看到开发团队的工作状态;以人为本，意识到团队中的每个角色都
是有进取心的个体，团队创造的是项目而不是个人，每个人都得到了他们需要和信任的
环境和支持;优先考虑面对面交流来传达信息;通过量化工作进度的软件实现用户价值;
需求方和开发团队要以可持续发展的态度对待开发，拒绝锤子买卖，保持稳定运行的步
调一致;持续改进技术和设计各方面，提高自身能力，增强敏捷能力;关注核心需求并尽
可能简化需求;多听取自组织团队的意见和想法，这样体系结构也可以遵循开发过程;团
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 27 - 
队需要定期进行回顾，以思考如何提高效率，并相应地协调和调整他们的行为。 
4.2 DF 银行外汇交易数据报表平台开发项目风险的识别 
4.2.1 DF 银行外汇交易数据报表平台开发项目风险的识别特点与原则   
该数据报表平台是为了全球为DF 银行工作的基金经理与在DF 银行办理业务的客
户来来服务，故DF 银行外汇交易数据报表平台开发项目有如下特点， 
(1) 严谨性 
金融行业有严格的法律法规要求与对数据保护的需求，对数据可信度、准确性和数
据完整性的要求程度。该特点对项目的影响是要信息来源明确，来自可以依赖的外部供
应商，信息要处理经过审查、甚至业务的确认，这对需求的范围有影响。 
(2) 灵活性 
本项目立项时的定调就是要在项目管理方式上有转型，虽然金融业是严谨的行业，
但敏捷项目管理最重要的就是贴合客户的想法，随着客户当前的想法调整项目的内容。
所以虽然项目由详细的开发计划，但开发计划一直在不断调整。这个特点会导致项目或
无法在规定时间内交付或交付的质量不达标。 
(3) 复杂性 
这个是金融项目的特点，对市场敏感。金融行业独有的特点就是虽然依赖于实体，
但是不依靠实体就可以产生价值，所以对市场的敏感程度高。对政治敏感，因为金融业
没有实体投入又能产生巨大的价值，所以市场监管严格，而跨国项目又需要适应不同国
家的政策。比如美国调整了对华控制名单，项目的开发内容就要做出对应的调整。 
(4) 完整性 
金融信息服务提供者在收集、处理、处理和提供财务信息时，应当保证信息要素完
整，不存在重大的信息遗漏和信息失真。缺乏历史数据是应用该模型的一个障碍。用数
据，最好是实际数据制造出情景是分析需求中的一个关键环节。通过数据找出要素间的
相关性。而受于行业内数据多且繁杂的情况，大多银行都缺乏对历史数据的积累，完整
性缺失[48]。项目要对上世纪的系统进行升级换代，有很多原始数据就是缺失的、没有被
统计到的。这对项目有很大影响。 
受行业敏感性与内部要求影响，DF 银行外汇交易数据报表平台开发项目还需要遵
循如下原则 
(1) 可复用原则 
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 28 - 
公司要求在开发流程的设计上要精炼出可以适用后面各项目开发的标准流程；细节
上定义出来的封装接口不能是一次性的接口，而是可以被多方调用，也可以抓取系统内
部的由不同供应商提供的信息。 
(2) 合规合法原则 
遵守和遵守法律、政策、法规、程序和合同的能力。金融信息服务提供者在收集、
处理、处理和提供信息时，不得违反知识产权、版权等法律法规的要求。 
(3) 严格保密原则 
不能向未经授权的个人、实体或流程提供或不向其披露信息。金融信息服务提供者
应当通过完善的信息安全体系，保证信息不被未经授权的人使用，在信息使用和传递过
程中不因非法泄露而传播。 
4.2.2 DF 银行外汇交易数据报表平台开发项目风险的识别目的与方法 
展开风险识别活动的目的是为了能够识别能影响项目如期上线的潜在风险因子，从
而在项目展开的过程中能规避掉风险或者能降低风险对项目的影响，从而使项目能顺利
进行至如期上线。 
本项目的风险的识别参照公司的风险管理制度结合敏捷开发的特点主要分为三个
阶段。第一阶段为项目规划初期，是立项风险评估。第二阶段在每年为下一年度申请资
金预算时，是年度风险识别。第三阶段为每次迭代开始之前，是常规风险识别。第一阶
段的风险识别涉及范围最广，参与人员最多。包含了决策层与核心管理层。第二阶段的
风险识别是在形成了项目风险清单后，所以参与人数减少。主要参与人员为管理层与核
心执行层。第三阶段的风险识别范围最小，主要参与人员为各开发组的团队人员，是每
次迭代都要进行的一个环节。 
论文使用的研究方法为头脑风暴法结合德尔菲法，以尽可能全面的分析项目风险，
并从中筛选出风险等级较高的风险因素。 
4.2.3 DF 银行外汇交易数据报表平台开发项目风险的识别流程 
本项目的风险的识别流程参照图4.2，在邀请风险小组参与头脑风暴会议之前明确
风险识别仅针对DF 银行外汇交易数据报表平台开发项目，风险管理小组成员结合项目
的风险特点与原则，依照项目的工作分解图对当前项目的风险展开讨论 
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 29 - 
 
图4.2 DF 银行外汇交易数据报表平台开发项目风险识别流程 
Fig.4.2 Risk analysis process of DF FX Data exchange platform project  
本项目在开发过程中，每次迭代的任务之前，也要花半天时间对接下来要开发的工
作进行风险识别。分析后续工作是否会对已开发的内容或者产品造成影响又或者当前的
法规法制与市场情况是否有变化。如发现新的重大风险需要及时上报风险管理小组评估
或展开新一轮全面的风险识别与评价，以完善风险控制清单。 
4.3 风险的识别过程 
4.3.1 头脑风暴法分析项目存在的风险 
在项目开始之前，使用头脑风暴法对项目风险进行分析，分析逻辑依照开发项目的
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 30 - 
重要节点，产品构思、需求分析、需求管理、需求实现、需求验收、产品运营，几个阶
段分段讨论风险。再结合通过文献阅读总结出的风险种类，技术、管理、产品规模、需
求、信用风险，这五大种类构成二维矩阵如表4.1 所示。 
 
表4.1DF 银行外汇交易数据报表平台开发项目风险二维矩阵 
Table 4.12D matrix of risks in of DF FX Data exchange platform project 
项目阶段 
风险分类 
技术风险 
管理风险 
产品规模风险 
需求风险 
信用风险 
产品构思 
 
 
√ 
 
 
需求分析 
√ 
√ 
√ 
√ 
√ 
需求管理 
 
√ 
 
√ 
 
需求实现 
√ 
√ 
√ 
√ 
 
需求验收 
√ 
√ 
√ 
√ 
 
产品运营 
 
 
 
√ 
 
由业务分析师主持会议进行讨论，按照每一个阶段顺序展开讨论的方式进行，在场
人员均需发言，从自己的责任角度发表看法。会议内容由业务分析师记录，项目经理与
产品经理进行核对。 
(1) 产品构思阶段可能会产生的风险 
产品规模风险：开发任务由商业画布逐步向下分解。规划初期，管理层为了尽量的
将开发内容贴合决策层的想法承诺过多的内容，分析师与开发团队也会乐观估计实际的
开发难度，导致目标定的过高。 
环境影响：整体团队对在疫情和政治环境情况估计乐观，很少会预测到社会及其他
问题对跨团队、跨部门、跨公司的沟通协调等活动的影响。 
(2) 需求分析阶段可能会产生的风险 
项目质量风险：在需求调研初期，正是疫情爆发的阶段，国内疫情情况得到了迅速
的控制，但是美国正是多轮的疫情反复中，业务分析师和产品经理经常无法在线参与高
强度、高频次的会议与开发。 
项目延期风险：需要基于新的管理方式进行开发与开发环境的部署，需要在国内申
请一套美国标准的开发软件，对交付进度有影响。 
数据完整性风险：要对旧数据进行数据清晰与重构，接入新的数据。开发人员多为
新人，对开发的内容没有概念，对数据的处理会激进。 
人员变动的风险：疫情情况下人员的短缺但编制有限，分析师需要一人兼顾多个项
目，对于需求分析的细致程度难以把控，对交付进度有影响。 
数据合规风险：银行业一直对数据的准确性与有效性要求极高，对于需求的内容还
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 31 - 
要借鉴国家的法律法规。 
系统相应不及时的风向：本项目最后呈现的交付物是一个可以供投资经理人使用的
低代码数据报表平台和客户与柜台人员能共同使用的自动化操作系统，受行业影响，集
团对该系统能提供的反馈及时性要求度高，要求系统最好能在三秒内给出反应，该想法
给项目的开发带来挑战与风险。 
(3) 需求管理阶段可能会产生的风险 
需求变动风险：受各因素需求在开发过程中会不断调整，需要有成熟的需求管理方
案来对需求进行优先级排序，不然就会面临无法完成需求交付的风险。 
数据管理风险：现如今国际关系变化快，国家对国家的限制条例经常调整，系统需
要第一时间做出调整。也有疫情情况导致的经济危机，各国频繁出台政策进行宏观调控 
(4) 需求实现阶段可能会产生的风险 
技术实现风险：因为DF 银行外汇交易数字报表平台采用的是新的管理方法与新的
技术，将数据整理迁移至云端中台，有因开发人员能力不足导致系统出现bug的可能性。 
需求沟通风险：需求实阶段的过程中开发人员会对开发细节不断的反复确认，需要
测试开发分析师和业务人员有频繁的沟通，但项目沟通链条长，很多情况下需求没有办
法被及时确认，会造成进度上不能及时完工。 
数据真实性风险：模拟产品交付的时候用的很多数据都似乎开发人员自行编造的，
缺少对真实场景多变环境校验的可能性。到实际场景中适用就会引发意想不到的bug. 
(5) 需求验收阶段可能会产生的风险 
向上管理风险：软件开发内容在验收阶段会被用户挑刺，对于前期没有明确出来的
需求点，用户会投诉开发部门的开发质量，需要管理层帮助沟通或者重新定义开发范围
有优先级，不然会导致产品无法正常上线。 
人员流动风险：外包团队的人员流动性大，会出现开发的人与负责交付的人不是一
个人的情况，如果没有做好交接，就会出现没有办法交付的情况。所以要重视人员流动
的风险、 
(6) 产品运营阶段可能会产生的风险 
信用风险：银行业一直对数据的准确性与有效性要求极高，对于开发的内容要定期
确认鉴国家的法律法规情况，以免出现合规问题。 
以上内容经过汇总，总结如表4.2 所示。 
 
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 32 - 
表4.2 初版DF 银行外汇交易数据报表平台开发项目风险清单 
Table 4.2 Draft risk list of DF FX Data exchange platform project 
序号 
种类 
风险因素 
1 
技术
风险 
开发人员的编码能力差，代码设计有问题，导致交付质量不达标。 
2 
硬件资源不足，架构不合理 
3 
第三方数据到报表数据库传递的正确性，包含人工操作产生的数据。 
4 
前期有无效数据没有被清理干净。 
5 
源头数据的完整性确实，数据与场景的关联关系紧密，脱离了场景的数据缺少关键信息
参数。 
6 
开发业务实现无法闭环，甚至无法功能设计 
7 
从业人员的安全性认识不足，IT 操作不规范，既懂IT 又懂业务的人相对匮乏 
8 
产品缺乏对产品独立的思考和功能涉及。 
9 
管理
风险 
业务线负责人，很多身兼数职无法兼顾全面 
10 
各方功能联调的部分，存在相互推脱的情况。 
自行安排优先级，导致各方依赖的功能，前置任务不完成，后置任务也无法开展 
11 
采用外包的模式，基础操作人员流动性大 
重要人员离职；招聘人员所花费时间比预期长 
12 
负责沟通的协调人员对需要协调的内容不清晰。分析人员没有承担起分析的工作，而只
是传递领导或同伴的传话。 
13 
产品
规模
风险 
开发范围过大涉及跨团队、跨部门、跨公司的沟通协调，存在各公司根据内部研发情
况。 
14 
需求
风险 
后期需求变动大，导致前期规划与实际开销不符合。 
15 
需求分析和设计工作做的不细致，对于需求内容没有理清楚，更无法细化成用户故事。 
16 
各方交付的功能，存在问题反复，问题不断，难以清理的问题。 
17 
前期没有深度调研，需求分析和设计工作做的不细致，对于需求内容没有理清楚业务使
用的数据不是从业务方得到而是开发人员自己编造的。 
18 
前期没有深度调研，对于需求内容没有梳理透彻形成闭环，导致在系统实现时候只有正
向流程没有特殊处理方式。 
19 
自行安排优先级，导致各方依赖的功能，前置任务不完成，后置任务也无法开展。 
20 
信用
风险 
在各国均需要遵守法规，并实时调整。 
头脑风暴法也被应用在每个迭代结束的之后，下一次迭代开始之前的讨论总结会上
面，参与人员为开发团队成员，在美国对接客户的业务分析师与敏捷教练可能会参加。
涉及到具体细节的讨论时还会采用情景分析法，尤其是分析为什么会出现问题，并怎么
进行风险规避。 
讨论的具体过程与要求为，首先，开发小组和与会人员均需要发言，发言内容要从
做的好的说起，再说哪里可以做的更好，并提出改善建议，内容由小组成员指认或轮流
记录。发言结束后，全员票选出排名前三的风险项目，并记录在风险清单中。记录在风
险清单中的风险其对应的解决方案需要提问题的人将情景描绘清楚，并由全员想出在此
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 33 - 
情景中可能有的选择方案。后票选出最优方案，记录在代办清单中。在每天站会的时候
都要回顾一下是否有按照记录的执行。使用工具如表4.3 所示。 
 
表4.3 DF 银行外汇交易数据报表平台开发项目迭代风险清单 
Table4.3 Iteration risk list of DF FX Data exchange platform project  
编号 
风险项目 
—迭代发现 
—迭代监控 
目前状态 
负责人 
其他 
1 
模糊项没有与业务分析确认 
2 
2 
打开 
开发 
 
2 
用户故事没有写自动化脚本 
3 
3 
关闭 
测试 
 
4.3.2 德尔菲法分析项目存在的风险 
基于头脑风暴法得到的风险分析清单，请专家重新梳理项目风险并对风险等级打分
和给出对应的控制建议。邀请外部专家参与德尔菲法的识别项目风险为项目风险识别引
入客观视角。邀请的专家所在公司与DF 银行有密切的合作关系，是战略合作公司，邀
请的专家都是在同行业工作多年有丰富工作经验的专业人士。项目风险专家委员会如表
4.4 所示。 
表4.4 DF 银行外汇交易数据报表平台开发项目风险专家委员会 
Table 4.4 Risk management expert team of DF FX Data exchange platform project 
人员 
公司 
职务 
包永涛 
杭州银行 
程序架构师，有阿里工作背景，10 年软件开发经验 
李华 
互联网公司有赞 
客户经理，销售冠军，了解用户 
梁丽 
Jira 公司 
高级敏捷教练 
郭锐 
宁波银行 
数据分析师 
陈潘耀 
蚂蚁金融 
外汇交易业务经理 
June He 
DF 银行 
产品经理 
Mike Li 
DF 银行 
项目经理 
Julia Luo 
DF 银行 
开发专家，架构师 
Jesus Who 
DF 银行 
合规部门 
Shannon When 
DF 银行 
风控部门 
使用如下表格向专家们发出第一轮意见征询，与专家们明确调查表的目的与收集时
间。在风险分类与因素一栏陈列头脑风暴法的结果，初版项目风险清单的结果，在风险
分类建议一栏系征求专家对当前风险分类的建议，风险等级依照1-5 打分，1 分为风险
等级小，2 分为风险等级较小，3 分为风险等级适中，以此类推。并向专家们征询对应
的风险控制办法。 
 
 
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 34 - 
表4.5 DF 银行外汇交易数据报表平台开发项目风险调查表 
Table 4.5 Experts survey of DF FX Data exchange platform project 
序号 
风险因素 
风险分类建议 
风险等级（1-5） 
风险控制方法 
1 
 
 
 
 
2 
 
3 
 
调查表结果由业务分析师整理，项目经理与产品经理进行核对。核对结果为专家小
组统一意见对一级风险进行了重新的定义与命名。因产品规模风险与需求风险有紧密关
联关系，即需求分析不清楚可能会导致产品规模的变化，故将产品规模风险取消，下属
风险并入到需求风险中。将一级风险增加一项人员风险，将涉及到人的风险统一归类在
此一级风险下，因风险因素中有很多人与人沟通或配合不顺引发的风险。合并人员变动
风险与人员流动风险。 
继续使用表4.5 向专家们发出第二轮意见征询，明确调查表填写逻辑一致，在风险
分类与因素一栏陈列第一轮整理过后的结果。调查表结果由业务分析师整理，项目经理
与产品经理进行核对，核对结果去除了专家组打分为较小风险的项目，专家意见汇总如
下表4.6 所示。 
表4.6 DF 银行外汇交易数据报表平台开发项目风险评分 
Table 4.6 Rate of DF FX Data exchange platform project risk list 
序号 
一级风险 
二级风险 
风险平均分 
1 
管理风险 
需求变动的风险 
4 
2 
管理风险 
资金紧缺的风险 
5 
3 
管理风险 
场景覆盖全面性的风险 
3 
4 
管理风险 
项目延期的风险 
4 
5 
管理风险 
项目质量的风险 
3 
6 
管理风险 
数据获取与确认的风险 
5 
7 
管理风险 
推卸责任的风险 
2 
8 
管理风险 
工作范围不清的风险 
1 
9 
人员风险 
关系人沟通的风险 
4 
10 
人员风险 
人员变动的风险 
4 
11 
人员风险 
缺少领导支持的风险 
3 
12 
人员风险 
负责人失职的风险 
2 
13 
数据与技术风险 
数据无效风险 
2 
14 
数据与技术风险 
数据安全风险 
5 
15 
数据与技术风险 
系统响应不及时的风险 
4 
16 
数据与技术风险 
新技术的风险 
3 
17 
数据与技术风险 
开发工具陌生 
1 
18 
合规与信用风险 
数据合规的风险 
4 
19 
合规与信用风险 
人为操作合规 
3 
20 
合规与信用风险 
数据真实性的风险 
2 
东北大学硕士学位论文     
第4 章 DF 银行外汇交易数据报表平台开发项目风险识别 
 
- 35 - 
4.3.3 DF 银行外汇交易数据报表平台开发项目风险清单 
经过对一级二级风险类型的梳理并剔除初步风险等级评分较低的风险因子，汇总
DF 银行外汇交易数据报表平台开发项目风险清单如表4.7 所示； 
 
表4.7 DF 银行外汇交易数据报表平台开发项目风险清单 
Table 4.7 Final risk list of DF FX Data exchange platform project 
序号 
一级风险 
二级风险 
1 
管理风险 
需求变动的风险 
2 
管理风险 
资金紧缺的风险 
3 
管理风险 
场景覆盖全面性的风险 
4 
管理风险 
项目延期的风险 
5 
管理风险 
项目质量的风险 
6 
管理风险 
数据获取与确认的风险 
7 
人员风险 
关系人沟通的风险 
8 
人员风险 
人员变动的风险 
9 
人员风险 
缺少领导支持的风险 
10 
数据与技术风险 
数据安全风险 
11 
数据与技术风险 
系统响应不及时的风险 
12 
数据与技术风险 
新技术的风险 
13 
合规与信用风险 
数据合规的风险 
14 
合规与信用风险 
人为操作合规 
15 
合规与信用风险 
数据真实性的风险 
4.4 小结 
基于前期通过文献阅读整理出的初版项目风险清单结合学习到的项目风险识别方
法与DF 银行外汇交易数据报表平台开发项目的风险管理计划、项目实施计划和相关规
定与办法作为的风险识别依据，选用最适合当前项目的头脑风暴法对当前项目潜在的风 
险进行识别，风险识别的阶段涵盖了从产品构思到需求的分析管理与实现及后期的投入 
运营。其中分析内容包括了，风险开始的原因及潜在风险会影响的时间长短，同时讨论 
得出对于每条风险控制的方案后成立专家组，使用德尔菲分析法票选出当前项目中最适 
用的解决方案并将风险总结成不同的风险类型。得到了共计15 项会影响项目交付的风
险，并归类为，管理风险、人员风险、数据与技术风险、合规与信用风险。 
 
 
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 36 - 
第5 章 DF 银行外汇交易数据报表平台开发项
目风险评价 
5.1 DF 银行外汇交易数据报表平台开发项目风险评价方法确定 
基于已经识别出来的一级风险与二级风险可以清晰的规划出项目风险的要素体系，
规划目标项为DF 银行外汇交易数据报表平台开发项目总风险，类型项对应一级风险，
因素项对应为二级风险。基于此适合采用层次分析法来将复杂的项目风险分解至因素级
别。对比因素间的重要度两两打分，建立判断矩阵并计算得出最大特征值与特征向量，
检验一致性后得出权重。层次分析法是一套成熟且适用性广的风险评价法，适用于本项
目。但单凭借层次分析法难以实现定性风险因素转为定量风险因素，所以需要使用模糊
评价法运用隶属函数构建得出评价矩阵并对评价结果进行分析。 
因DF 银行外汇交易数据报表平台开发项目是DF 公司第一个结合瀑布式与敏捷式
开发的项目，管理难度较大、建设周期长、投资金额大、流程繁琐，涉及的相关方众多。
公司需要对项目风险有全面细致的了解，不是仅凭项目经理的经验与能力，所以选用层
次分析法与模糊评价法来进行风险评价。 
5.2 运用模糊评价法进行项目指标风险评价  
5.2.1 建立风险评价模型与矩阵 
依照 DF 银行外汇交易数据报表平台项目的建设内容及目标，构建的模型。依据评
价模型的建立原则，通过专家和相关从业人员对上文形成的项目风险清单进行合理化的
分析并分出层次，架构出一个层次清晰的模型，由上至下的采用逻辑递进的方式枚举出
每一层次的印象因素，将风险细分到最小颗粒度。结合 DF 银行外汇交易数据报表平台
自身特点构建了风险评价模型，如图5.1 所示。 
(1) 目标层：分析DF 银行外汇交易数据报表平台开发项目建设目标的结果。 
(2) 准则层：分析DF 银行外汇交易数据报表平台开发项目达到目标结果所满足的
不同条件。 
(3) 指标层：各个准则存在的主要风险要素。 
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 37 - 
 
图5.1 DF 银行外汇交易数据报表平台开发项目风险评价模型 
Fig.5.1 Risk evaluation model of DF FX Data exchange platform project 
建立“准则层-目标层”评价矩阵，为了能将两两因素比较的过程进行量化描述，取
值采用1-9 标度法，取值含义如表5.1 所示。 
表5.1 1-9 标度法含义 
Table 5.1 1-9 Scale method and meaning 
标度 
含义 
1 
    表示两个因素比较，一个因素比一个因素同等重要 
3 
表示两个因素比较，一个因素比一个因素略微重要 
5 
表示两个因素比较，一个因素比一个因素较强重要 
7 
表示两个因素比较，一个因素比一个因素强烈重要 
9 
表示两个因素比较，一个因素比一个因素极端重要 
2，4，6，8 
两相邻判断的中间值 
倒数 
如果因素i 与因素j 的重要性之比为aij，那么因素j 与因素i 重要性之比为
aij 的倒数。 
专家组经过讨论综合考虑了某一层级的各个因素对上一层的影响，两两之间进行对
比，得到“标准层-目标层”评价矩阵，如表5.2 所示。 
 
 
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 38 - 
表5.2 DF 银行外汇交易数据报表平台开发项目准则B 层-目标A 层评价矩阵表 
Table 5.2 Weight index of DF FX Data exchange platform project criteria layer B - target layer A 
A 
B1 
B2 
B3 
B4 
B1 
1 
3 
2 
5 
B2 
1/3 
1 
1/3 
1/5 
B3 
1/2 
3 
1 
5 
B4 
1/5 
5 
1/5 
1 
 
根据评价矩阵信息，运用公式（5.1）计算评价矩阵每一行因素的乘积Gi，如表5.3 所示。 
              
 
（5.1） 
                  
 
表5.3 DF 银行外汇交易数据报表平台开发项目准则B 层-目标A 层评价矩阵表 Gi 值 
Table 5.3 Weight index Gi Value of DF FX Data exchange platform project criteria layer B - target layer A 
A 
B1 
B2 
B3 
B4 
Gi 
B1 
1.0000  
3.0000  
3.0000  
5.0000  
45.0000  
B2 
0.3333  
1.0000  
1.0000  
5.0000  
1.6667  
B3 
0.3333  
1.0000  
1.0000  
5.0000  
1.6667  
B4 
0.2000  
0.2000  
0.2000  
1.0000  
0.0080  
再运用公式（5.2）计算出几何平均值，如表5.4 所示。 
 
（5.2） 
 
             
  
表5.4DF 银行外汇交易数据报表平台开发项目准则B 层-目标A 层评价矩阵表平均几何值 
Table 5.4 Weight index W Value of DF FX Data exchange platform project criteria layer B - target layer A 
A 
B1 
B2 
B3 
B4 
W 
B1 
1.0000  
3.0000  
3.0000  
5.0000  
2.5900  
B2 
0.3333  
1.0000 
1.0000  
5.0000  
1.1362  
B3 
0.3333  
1.0000  
1.0000  
5.0000  
1.1362  
B4 
0.2000  
0.2000  
0.2000  
1.0000  
0.2991  
再运用公式（5.3）计算出最大特征值，λmax=4.1532； 
 
（5.3） 
 
 
 
 
)
....
3
,
2
,1
(
1
n
a
a
G
n
j
ij
i
=
= 
=

=
=
n
i
nw
Aw
i
i
1
)
(
max

n
i
G
w =
*
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 39 - 
表5.5 DF 银行外汇交易数据报表平台开发项目准则B 层-目标A 层评价矩阵表Wi 值 
Table 5.5 Weight index Wi Value of DF FX Data exchange platform project criteria layer B - target layer A 
A 
B1 
B2 
B3 
B4 
Gi 
W 
Wi 
B1 
1.0000  
3.0000  
3.0000  
5.0000  
45.0000  
2.5900  
0.5018  
B2 
0.3333  
1.0000  
1.0000  
5.0000  
1.6667  
1.1362  
0.2201  
B3 
0.3333  
1.0000  
1.0000  
5.0000  
1.6667  
1.1362  
0.2201  
B4 
0.2000  
0.2000  
0.2000  
1.0000  
0.0080  
0.2991  
0.0579  
 
（5.4） 
 
得到最大特征值之后再运用公式（5.4）对判断矩阵进行一致性检验，RI 为平均随
机一致性指标，RI 的值如表5.6 所示。 
表5.6 1-10 阶平均随机一致性指标RI 
Table 5.3 Order 1-10averagerandom consistency index RI 
n 阶 
3 
4 
5 
6 
7 
8 
9 
10 
RI 值 
0.52 
0.89 
1.12 
1.26 
1.36 
1.41 
1.46 
1.49 
建立“指标层-准则层”评价矩阵，按照同样的方式，专家们经过讨论得到了4 个
“指标层-准则层”的评价矩阵。如表5.7 至表5.10 所示。 
表5.7 DF 银行外汇交易数据报表平台开发项目准则C1 层-目标B1 层风险评价矩阵表 
Table 5.7 Risk Weight index of DF FX Data exchange platform project index layer C1 - Criterion layer B1 
B1 
C11 
C12 
C13 
C14 
C15 
C16 
Wi 
C11 
0.43  
0.57  
0.33  
0.50  
0.41  
0.37  
0.38  
C12 
0.14  
0.19  
0.24  
0.25  
0.14  
0.22  
0.20  
C13 
0.06  
0.04  
0.05  
0.04 
0.07  
0.04  
0.13  
C14 
0.11  
0.10  
0.14  
0.12  
0.14  
0.15  
0.13  
C15 
0.14  
0.19  
0.10  
0.12  
0.14  
0.15  
0.09  
C16 
0.09  
0.06  
0.10  
0.06  
0.07  
0.07  
0.07  
Λmax = 6.0713，CR=0.0113<0.1，因此满足一致性检验需求。 
表5.8 DF 银行外汇交易数据报表平台开发项目准则C2 层-目标B2 层风险评价矩阵表 
Table 5.8 Risk Weight index of DF FX Data exchange platform project index layer C2 - Criterion layer B2 
B2 
C21 
C22 
C23 
Wi 
C21 
1.0000  
3.0000  
7.0000  
0.6491  
C22 
0.3333  
1.0000  
5.0000  
0.2790  
C23 
0.1429  
0.2000  
1.0000  
0.0719  
Λmax = 3.0649，CR=0.0257<0.1，因此满足一致性检验需求。 
 
 
1
max
−
−
=
n
n
CI

东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 40 - 
表5.9 DF 银行外汇交易数据报表平台开发项目准则C3 层-目标B3 层风险评价矩阵表 
Table 5.9 Risk Weight index of DF FX Data exchange platform project index layer C3 - Criterion layer B3 
B3 
C31 
C32 
C33 
Wi 
C31 
1.0000  
2.0000  
3.0000  
0.5278  
C32 
0.5000  
1.0000  
3.0000  
0.3325  
C33 
0.3333  
0.3333  
1.0000  
0.1396  
Λmax = 3.0536，CR=0.0213<0.1，因此满足一致性检验需求。 
表5.10 DF 银行外汇交易数据报表平台开发项目准则C3 层-目标B3 层风险评价矩阵表 
Table 5.10 Risk Weight index of DF FX Data exchange platform project index layer C4 - Criterion layer B4 
B4 
C41 
C42 
C43 
Wi 
C41 
1.0000  
5.0000  
7.0000  
0.7306  
C42 
0.2000  
1.0000  
3.0000  
0.1884  
C43 
0.1429  
0.3333  
1.0000  
0.0810  
Λmax = 3.0649，CR=0.0257<0.1，因此满足一致性检验需求。 
根据计算结果进行当前项目的风险层级排序及项目组合权重计算的过程如表5.11
所示。 
表5.11 DF 银行外汇交易数据报表平台开发项目风险权重综合指数 
Table 5.11 Comprehensive index of risk weight of DF FX Data exchange platform project  
层级C 
W 
C11 
0.427408  
 
 
 
0.1068520  
C12 
0.190889  
 
 
 
0.0477223  
C13 
0.047523  
 
 
 
0.0118808  
C14 
0.123963  
 
 
 
0.0309907  
C15 
0.136439  
 
 
 
0.0341097  
C16 
0.073778  
 
 
 
0.0184445  
C21 
 
0.649118  
 
 
0.1622795  
C22 
 
0.278955  
 
 
0.0697386  
C23 
 
0.071927  
 
 
0.0179819  
C31 
 
 
0.527836  
 
0.1319590  
C32 
 
 
0.332516  
 
0.0831290  
C33 
 
 
0.139648  
 
0.0349120  
C41 
 
 
 
0.730645  
0.1826612  
C42 
 
 
 
0.188394  
0.0470985  
C43 
 
 
 
0.080961  
0.0202403  
为了确保项目能够在一定时间内有效的实现建设性目标，本项目采用专家打分法，
并将项目的评价结果划分为五个层级的风险等级，即为:V=[低，较低，中等，较高，高]。 
项目风险本身是一个随机变量，对风险的重要程度根据概率进行判断，根据专家评
议，判断标准表格如5.12 所示； 
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 41 - 
表5.12 DF 银行外汇交易数据报表平台开发项目风险等级划分表 
Table 5.12 Risk classification of DF FX Data exchange platform project  
风险评判范围 
风险等级 
0.05<权重<1 
较大风险 
0.03<权重<0.05 
一般风险 
0<权重<0.03 
较小风险 
根据风险等级划分表格，对应项目风险因素的权重进行分析与梳理，得到 DF 银行
外汇交易数据报表平台开发项目风险权重综合指数排序，如5.13 所示； 
表5.13 DF 银行外汇交易数据报表平台开发项目风险权重综合指数排序 
Table 5.13 Ranking of comprehensive index of risk weight of DF FX Data exchange platform project  
序
号 
风险项 风险内容 
风险类型 
风险等级 
权重 
1 
C41 
数据合规的风险 
合规与信用风险 
较大风险 
0.1826612  
2 
C21 
关系人沟通的风险 
人员风险 
较大风险 
0.1622795  
3 
C31 
数据安全风险 
数据与技术风险 
较大风险 
0.1319590  
4 
C11 
需求变动的风险 
管理风险 
较大风险 
0.1068520  
5 
C32 
系统响应不及时的风险 
数据与技术风险 
较大风险 
0.0831290  
6 
C22 
人员变动的风险 
人员风险 
较大风险 
0.0697386  
7 
C12 
资金紧缺的风险 
管理风险 
一般风险 
0.0477223  
8 
C42 
人为操作合规 
合规与信用风险 
一般风险 
0.0470985  
9 
C33 
新技术的风险 
数据与技术风险 
一般风险 
0.0349120  
10 
C15 
项目质量的风险 
管理风险 
一般风险 
0.0341097  
11 
C14 
项目延期的风险 
管理风险 
一般风险 
0.0309907  
12 
C43 
数据真实性的风险 
合规与信用风险 
较小风险 
0.0202403  
13 
C16 
数据获取与确认的风险 
管理风险 
较小风险 
0.0184445  
14 
C23 
缺少领导支持的风险 
人员风险 
较小风险 
0.0179819  
15 
C13 
场景覆盖全面性的风险 
管理风险 
较小风险 
0.0118808  
5.2.2 确定评价指标 
根据 DF 银行外汇交易数据报表平台开发项目风险权重综合指数排序，征求专家组
意见后，确定出对 DF 银行外汇交易数据报表平台开发项目有较大影响的9 个指标，指
标体系为：（数据合规的风险，关系人沟通的风险，数据安全风险，需求变动的风险，
系统响应不及时的风险，人员变动的风险，资金紧缺的风险，人为操作合规，新技术的
风险）如5.14 所示； 
 
 
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 42 - 
表5.14 DF 银行外汇交易数据报表平台开发项目指标评价表 
Table 5.14 Index evaluation of DF FX Data exchange platform project  
序号 
评价指标 
评价方法 
风险等级 
1 
数据合规的风险 
客观评价 
越少越好 
2 
关系人沟通的风险 
客观评价 
越少越好 
3 
数据安全风险 
客观评价 
越少越好 
4 
需求变动的风险 
客观评价 
越少越好 
5 
系统响应不及时的风险 
客观评价 
越少越好 
6 
人员变动的风险 
客观评价 
越少越好 
7 
资金紧缺的风险 
客观评价 
越少越好 
8 
人为操作合规 
客观评价 
越少越好 
9 
新技术的风险 
客观评价 
越少越好 
依据专家组的建议，经过总结得到如下风险类型汇总，并包含具体的风险，管理风
险（U1）需求变动的风险，资金紧缺的风险；人员风险（U2）关系人沟通的风险，人员
变动的风险；数据与技术风险（U3）数据安全风险，系统响应不及时的风险，新技术的
风险；合规与信用风险（U4）数据合规的风险，人为操作合规。 
评价的指标因素集按照层次关系递进，将上述评价采用的指标因素进行清晰排列。
首先，DF 银行外汇交易数据报表平台开发项目第一层次的评价因素集 U={U1，U2，U3，
U4}={管理风险、人员风险，数据与技术风险、合规与信用风险}；在第一层次的评价因
素集下构成DF 银行外汇交易数据报表平台开发项目评价因素的第二层级的下属的子因
素集，分别为：U1={U11，U12}={需求变动的风险，资金紧缺的风险}，U2={U21，
U22}={关系人沟通的风险，人员变动的风险}，U3={U31，U32，U33}={数据安全风险，
系统响应不及时的风险，新技术的风险}，U4={U41，U42}={数据合规的风险，人为操
作合规}。 
5.2.3 确定评价因素集的权重 
依照上一章节的内容已将总评价权重计算完成，汇总如表5.15 所示。 
表5.15 DF 银行外汇交易数据报表平台开发项目风险评价因素集权重表 
Table 5.15 Re-table of centralized evaluation factors of DF FX Data exchange platform project  
序号 
风险类型 
风险内容 
总评价权重 
1 
管理风险 
U11 需求变动的风险 
0.187313558 
2 
U12 资金紧缺的风险 
0.080496877 
3 
人员风险 
U21 关系人沟通的风险 
0.123335572 
4 
U22 人员变动的风险 
0.055084143 
5 
数据与技术风险 
U31 数据安全风险 
0.152315702 
6 
U32 系统响应不及时的风险 
0.095952879 
7 
U33 新技术的风险 
0.040297684 
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 43 - 
8 
合规与信用风险 
U41 数据合规的风险 
0.210839404 
9 
U42 人为操作合规 
0.054364181 
5.2.4 模糊评价 
(1) 确定指标的取值范围 
专家组根据项目本身的具体情况进行分析展开讨论，经过讨论决定将风险等级划分
为五个等级”风险大（0.9）风险较大（0.7）风险适中（0.5）风险较小（0.3）风险小（0.1）。 
(2) 确定隶属度 
在公开情况下，秉持着公平公正的态度，专家按照事先确定的评价标准结合自己领
域所有的专业知识给待评价项目的评价因素指标进行等级投票，投票结果如表5.16 所
示。通过表达式μ𝑉𝑗(𝑢𝑖) = Mij/n式中Mij为𝑢𝑖∈𝑉𝑗等级的评价专家的人数,𝑚为专家组的
人数。 
表5.16 DF 银行外汇交易数据报表平台开发项目风险专家评价表 
Table 5.16 Expert evaluation form of DF FX Data exchange platform project   
评价指标 
权重 
评价等级 
0.9 
0.7 
0.5 
0.3 
0.1 
数据合规的风险 
0.187313558 
0 
2 
4 
2 
2 
关系人沟通的风险 
0.080496877 
0 
0 
2 
4 
4 
数据安全风险 
0.123335572 
0 
5 
5 
0 
0 
需求变动的风险 
0.055084143 
0 
5 
3 
2 
0 
系统响应不及时的风险 
0.152315702 
4 
4 
2 
0 
0 
人员变动的风险 
0.095952879 
0 
0 
0 
2 
8 
资金紧缺的风险 
0.040297684 
0 
0 
2 
6 
2 
人为操作合规 
0.210839404 
0 
6 
2 
2 
0 
新技术的风险 
0.054364181 
0 
8 
2 
0 
0 
(3) 评价矩阵的计算 
设 ui 对各评语等级的隶属度值为𝑟𝑖𝑗，根据μ𝑉𝑗(𝑢𝑖)可得到单因素指标对于各评价
等级的矩阵形式𝑅= (𝑟𝑖𝑗)9 × 4，同理，可以得到系统单层次各指标因素的评价矩阵如表
5.17 所示。 
表5.17 DF 银行外汇交易数据报表平台开发项目风险评价矩阵 
Table 5.17 Risk evaluation matrix of DF FX Data exchange platform project  
0.000  
0.125  
0.250  
0.125  
0.125  
0.000  
0.000  
0.125  
0.250  
0.250  
0.000  
0.313  
0.313  
0.000  
0.000  
0.000  
0.313  
0.188  
0.125  
0.000  
0.250  
0.250  
0.125  
0.000  
0.000  
0.000  
0.000  
0.000  
0.125  
0.500  
0.000  
0.000  
0.125  
0.375  
0.125  
东北大学硕士学位论文     
第5 章 DF 银行外汇交易数据报表平台开发项目风险评价 
 
- 44 - 
0.000  
0.375  
0.125  
0.125  
0.000  
0.000  
0.500  
0.125  
0.000  
0.000  
5.2.5 评价结果分析 
已得到模糊评价矩阵 R，为了得到模糊综合评价模型 B 需要评价因素的权重分配
模糊向量 A 与R 的进行合成运算。经过如下推演计算得出B。 
B=A{0.187313558,0.080496877,0.123335572,0.055084143,0.152315702,0.095952879,
0.040297684,0.210839404,0.054364181}*R{}={0.0381,0.2235, 0.1630,0.1039 ,0.0966 } 
再通过与评语集的运算得出D，为项目风险评估结果为0.3130。运算方式方式为
D={0.9*0.0381+0.7*0.2235+0.5*0.1630+0.3*0.1039+0.1*0.0966} 
最后得出结论为 DF 银行外汇交易数据报表平台开发项目风险适中。 
5.3 小结 
主要对 DF 银行外汇交易数据报表平台开发项目风险进行评价，先确定评语集与评
价因素集，再使用层次分析法将风险拆分为三个层面的风险要素，基于体系建立风险评
价模型，后通过专家打分法确定向量的数值，对矩阵进行一致性校验。通过后再一次对
权重进行排序建立评价矩阵，将风险的影响因子量化为项目的风险控制提供依据。 
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 45 - 
第6 章 DF 银行外汇交易数据报表平台开发项
目风险的应对与控制 
6.1 DF 银行外汇交易数据报表平台开发项目风险应对策略 
通过对DF 银行外汇交易数据报表平台项目风险评价可以看出，风险共计15 项，涉
及了数据合规的风险、关系人沟通的风险、数据安全风险、需求变动的风险、系统响应
不及时的风险、人员变动的风险、资金紧缺的风险、人为操作合规、新技术的风险、项
目质量的风险、项目延期的风险，提出相应风险控制策略及措施如下： 
6.1.1 管理风险应对策略 
通过对DF 银行外汇交易数据报表平台项目的风险评价中可以看出，在管理风险中，
较大风险有1 项，一般风险有3 项，较小风险有2 项，分别为需求变动的风险，资金紧
缺的风险，场景覆盖全面性的风险，项目延期的风险，项目质量的风险，数据获取与确
认的风险，分析风险产生的原因如表6.1 所示。 
表6.1 管理风险分析表 
Table 6.1 Analysis list of Management risks 
风险项 
风险内容 
风险分析 
C11 
需求变动的风险 
需求分析和设计工作做的不细致，对于需求内容没有理清楚，更无法细化
成用户故事。 
C12 
资金紧缺的风险 
后期开发过程中需求变动大，导致前期预算与实际开销不符合。 
人员能力差，导致交付质量不达标，不停的修整交付物。 
C13 
场景覆盖全面性的
风险 
前期没有深度调研，对于需求内容没有梳理透彻形成闭环，导致在系统实
现时候只有正向流程没有特殊处理方式。 
C14 
项目延期的风险 
需求模糊会使设计效果图体验不流畅，无法展开设计。 
开发业务实现无法闭环，甚至无法功能设计。 
测试用例测试逻辑不严谨，导致项目计划排期乐观。 
自行安排优先级，忽略功能之间的依赖关系，导致前置任务不完成，后置
任务也无法开展。 
C15 
项目质量的风险 
业务分析师并没有从客观的视角梳理业务提供价值而是任由需求方提出
逻辑错误的需求。 
产品经理对产品没有独立的思考与认识和功能设计。 
C16 
数据获取与确认的
风险 
三方数据包含无效数据。 
对应的风险控制策略如表6.2 所示，对于较大风险和一般风险，建议一定要采取办
法进行干预，应对策略上首选为风险缓释，如第二章介绍，主要方法为通过预防措施来
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 46 - 
避免风险的发生。对于项目延期的风险，虽然风险等级评估为一般风险，但遵循瀑布项
目中对于时间的紧迫关注，可按照风险规避的方式进行处理与控制。其余较小风险采取
风险接受的策略进行应对。 
表6.2 管理风险应对策略表 
Table 6.2 Management risks response strategy 
风险项 
风险等级 
应对策略 
控制办法 
C11 
较大风险 
风险缓释 
花更多的时间进行前期需求分析。 
让用户参与需求评审，审核需求，达到需求更贴合用户想法的目的。 
落实需求变动流程，评估需求变动的影响，影响很大需要上会。 
C12 
一般风险 
风险缓释 
控制需求的变动。 
阶段性更新预算计划。 
C13 
较小风险 
风险接受 
通过流程匹配场景，保证流程闭环。 
提高自动化程度，简单事情机器测试。 
在可以测试全面，有人力有资金的情况下尽可能的测试全面，舍弃风
险最小且回报率不高的场景。 
C14 
一般风险 
风险规避 
通过燃尽图等方式提前预判项目进度问题。 
先处理严重级别的缺陷；尽量多交付价值的观点。 
C15 
一般风险 
风险缓释 
回滚进迭代，重新进行优先级排序。 
及时做好关键人沟通。 
C16 
较小风险 
风险接受 
增加调研时间的长度和频次。 
了解目标用户使用软件的真实目的。 
提高用户参与的积极性。 
6.1.2 人员风险应对策略  
通过对DF 银行外汇交易数据报表平台项目的风险评价中可以看出，在人员风险中，
较大风险有2 项，一般风险有0 项，较小风险有1 项，分别为关系人沟通的风险、人员
变动的风险、项目系统运行及缺少领导支持的风险，分析风险产生的原因如表6.3 所示。 
表6.3 人员风险分析表 
Table 6.3 Analysis list of personnel risks 
风险项 
风险内容 
风险分析 
C21 
关系人沟通的风险 
在项目预研阶段没有定义人员职责和沟通升级渠道。 
开发范围过大涉及跨团队、跨部门、跨公司的沟通协调，存在各公司根据
内部研发情况自行安排任务优先级和交付时间。 
C22 
人员变动的风险 
采用外包的模式，基础操作人员流动性大，重要人员离职，招聘人员所花
费时间比预期长。 
C23 
缺少领导支持的风
险 
业务线负责人，很多身兼数职，兼顾全面。对需要协调的内容不清晰，背
景信息缺乏，表达不清晰。 
对应的风险控制策略如表6.4 所示，应对策略原则与管理风险基本一致较大风险采
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 47 - 
取风险缓释应对策略，较小风险采取风险接受应对策略。其中对于缺乏领导支持的风险，
会采用增加会议曝光度的方式来进行，但是对于未能得到领导支持的部分的风险，可以
接受并采取横向沟通方法推动。 
表6.4 人员风险应对策略表 
Table 6.4 Personnel risks response strategy 
风险项 风险等级 
应对策略 
控制办法 
C21 
较大风险 
风险缓释 
项目建设之初，与项目各利益相关方就沟通渠道和方式达成一致。 
在项目建设过程中，与项目各利益相关方进行沟通。 
培养和锻炼自身的沟通技巧。 
C22 
较大风险 
风险缓释 
做好文件管理。 
做好人员储备。 
尽量做到互相替补；特别是关键人员要有多个替补角色。 
工作尽量前置，营造良好的工作环境。 
留住核心成员，招聘优秀的人，借调外团队人员，并明确项目目标。 
确保借调人员成为项目一分子，有归属感。 
C23 
较小风险 
风险接受 
主动争取领导对项目的重视。 
经常向领导汇报工作进展。 
确保和领导的沟通渠道畅通。 
跨团队跨部门协调时首先在领导层面达成一致。 
6.1.3 数据与技术风险应对策略  
通过对DF 银行外汇交易数据报表平台项目的风险评价中可以看出，在数据与技术
风险中，较大风险有2 项，一般风险有1 项，较小风险有0 项，分别为数据安全风险、
系统响应不及时的风险、新技术的风险，分析风险产生的原因如表6.5 所示。 
表6.5 数据与技术风险分析表 
Table 6.5 Analysis list of data and technical risks  
风险项 
风险内容 
风险分析 
C31 
数据安全风险 
从业人员的安全性认识不足，IT 操作不规范，既懂IT 又懂业务的人相对
匮乏。 
C32 
系统响应不及时的
风险 
优化数据存储结构，增加存储空间。 
C33 
新技术的风险 
为了创新盲目使用新的技术。 
对应的风险控制策略如表6.6 所示，较大风险采取风险缓释应对策略，对于新技术
的使用情况一般需要长周期来进行批准，所以这里也选择采用风险缓释的方法进行控制。 
表6.6 数据与技术风险应对策略表 
Table 6.6 Data and technical risks response strategy 
风险项 风险等级 
应对策略 
控制办法 
C31 
较大风险 
风险缓释 
设立数据安全保护组织。 
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 48 - 
定义数据安全管理策略。 
配套管理技术和工具。 
C32 
较大风险 
风险缓释 
做好前期的性能规划，对可能出现性能问题的环节做到充足的估计。 
数据库的设计引入更多人的关注。 
在开发过程中解决了性能问题，尽可能采取一些性能优化模式。 
C33 
一般风险 
风险缓释 
适用之前做好调研。 
及时观察，明确止损点。 
上线前定义好回滚计划。 
6.1.4 合规与信用风险应对策略  
通过对DF 银行外汇交易数据报表平台项目的风险评价中可以看出，在合规与信用
风险中，较大风险有1 项，一般风险有1 项，较小风险有1 项，分别为数据合规的风险、
人为操作合规、数据真实性的风险，分析风险产生的原因如表6.7 所示。 
表6.7 合规与信用风险分析表 
Table 6.7 Analysis list of compliance and credit risks 
风险项 
风险内容 
风险分析 
C41 
数据合规的风险 
外资企业在国内对数据处理规则了解不够清晰。 
人员变动快，对于用户需求及公司规定了解不够清晰。 
C42 
人为操作合规 
新人太多，对公司业务流程了解不细致，对各国的法律法规或客户喜好了
解不透彻。 
C43 
数据真实性的风险 
需求内容没有理清楚，业务使用的数据不是从业务方得到而是开发人员
自己编造的。 
对应的风险控制策略如表6.8 所示， 
表6.8 合规与信用风险应对策略表 
Table 6.8 Compliance and credit risks response strategy 
风险项 风险等级 
应对策略 
控制办法 
C41 
较大风险 
风险缓释 
明确需要遵守的国际、组织、公司与客户规定。 
对开发人员进行培训。 
在系统中上线校验功能。 
C42 
一般风险 
风险转移 
可以通过招聘或培训的方式提升人员数据安全保障能力。 
制订操作手册。 
C43 
较小风险 
风险接受 
让用户参与开发和设计。 
具有代表性的用户参与。 
需求规格说明书。 
6.2 DF 银行外汇交易数据报表平台开发项目风险控制措施 
风险措施按照覆盖范围顺序，从集体文化到组织管理再到实现细节的方式逐一介绍。 
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 49 - 
6.2.1 培育文化氛围 
(1) 培育便于进行风险管理的文化氛围 
a. 沟通渠道扁平化，产品组要和开发人员有积极的互动，以更好的传递产品愿景，
同样职能经理也要关注成员在团队中的情况，做到每月能有1 对1 的沟通计划，具体频
次可以随着项目节奏调整。 
b. 积极互动的氛围，除了日常的工作交流与会，每季度或者每半年都能有团队（项
目团队）的集体活动，帮助团队互相理解，以促进更好的沟通。 
(2) 在开发上维持原有的敏捷开发方法不变增加更多的汇报与互动环节 
a. 工作坊，让业务部门与IT 有更多的沟通与互动，提供可见性和透明度。在整个
过程中有持续的计划和反馈，从项目一开始就为企业创造价值。 
b. 有自动化的项目周报，方便管理团队及时把握项目的进展情况，也能节省团队
时间，专注开发。周报包含，每周的主要研发工作、按照时间进度表工作的完成情况、
存在的问题、需要协调的内容。 
c. 每周及每月召开项目例会，把握项目在一段时间内的进展情况，说明工作周期
内的按照时间表完成的建设内容与现有问题，并对一定时间内项目中发现的问题，在会
上逐一讨论，并及时制定风险规避方案及应对措施 
d. 在项目到达相应的里程碑时召开评审会议，保证阶段成果合格，项目顺利执行。 
e. 搭建业务部门与职能部门的协调机制，召开项目经理与产品经理与敏捷教练的
半月会议，用于协调需求方向。 
(3) 开发团队内部要进行项目的内容讨论 
开发项目经理与系统架构师和测试团队都要参加讨论，架构师能提出专业的技术意
见也能结合公司的架构设计给出相对具体的解决方案，方案内容包含但不限于，如通过
什么样的方式（实时，定时的Job 抓取或接收）数据转译成数据字段；存储到什么样的
表格里；表格怎么样设计；与其他表的关联关系是什么样的；信息处理过程是否要记录
在log 的日志中；如果出现问题没有成功存储或者接收数据，是否要提供报错预警或者
重试；如果需要提供数据给到下游，提供一个什么样的数据接口；API 协议怎么定义。
测试团队需要基于需求思考测试场景，其中包含了极限压力测试，如业务反复修改数据，
无效数据判断，特殊数据处理或系统承载的特殊场景测试，大量的数据同一时间传入等。 
6.2.2 调整组织架构 
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 50 - 
调整组织架构方便风险控制，如图6.1 所示。 
(1) 调整组织架构方便风险控制 
组织架构上，如图6.1 所示。将负责业务产品的IT 团队的组织架构规划到业务团队
中，增加业务适用方对于开发的把控能力与参与感。 
(2) 建立项目执行的考核机制 
明确项目主管部门和各职能部门职责，对每个关键岗位的工作内容及考核标准进行
明确，在便于年度考核的同时调动管理人员的积极性与责任心，对按计划完成工作的项
目负责人给予年度业绩考核加分鼓励。加强评估系统实施过程中可能发生的风险和内部
控制，建立适当的绩效机制，确保该模式可以正常实施。 
(3) 对于开发，业务分析师和测试人员的绩效考核，由项目经理与职能经理各占比
50%。给予项目经理一定权力，便于项目中的管理。 
图6.1 DF 银行外汇交易数据报表平台开发项目组织架构图 
Fig.6.1 Project O-chart of DF FX Data exchange platform project  
6.2.3 加强项目监管 
(1) 关注测试的工作结果 
通过监控测试工作的进度状态和工作量，及时调整测试工作，提高测试的有效性。
通过监视缺陷变化的模式，可以及时识别测试团队遇到的障碍，并及时响应。通过监视
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 51 - 
当前迭代和测试覆盖的当前版本(测试覆盖包括特性点覆盖、配置覆盖和测试环境覆盖)，
可以确定交叉测试团队和开发团队是否在适当地工作，以及测试计划是否满足持续开发
的需求。通过监视测试人员的个人仪表板帮助管理人员了解测试人员是否能够更有纪律
地完成他们的工作，以及团队和项目的整体进展。同时，监视个人仪表盘还可以帮助测
试人员了解自己的价值，有效地建立自我认同感。 
(2) 强化需求管理 
分类讨论产生需求变化的原因，如果是用户提出的需求变更，需求方要递交申请至
产品经理，产品经理要审批新需求是否影响整个产品的基调，开发人员工时估算人天报
价，如果产品经理，开发人员和用户能就此达成共识则递交人天估算至项目经理，重新
调整项目周期。再对团队内部进行协调，重排开发的优先级。敏捷管理下的需求不同于
传统项目，会有复用，修补和细化的可能，西南财经大学的毛利君在自己的项目中研究
了将需求分析，需求规格整合到需求组织中的适用性[49]。该方法在本项目中也适用。 
(3) 对需求内容的开发进行优先级排序 
DF 银行外汇交易数据报表平台开发项目目前完全基于需求方需求程度的优先级来
进行排序，没有系统的优先级排序的流程，主要靠沟通。在北京交通大学的张志祥在项
目实现的过程中使用了详细的需求变更流程，不仅有普通需求变更流程还有紧急需求变
更流程[50]。开发过程中遇到问题，随时写有邮件与驻场业务分析师确认，如能立即确认
清晰，则继续当前任务，如果驻场业务分析师需要去向业务方确认，则开发选择应先行
开发其他的用户故事以免影响进度。 
(4) 强化文件管理 
DF 银行外汇交易数据报表平台开发项目目前由于对进度赶工，完全忽视文件的管
理，需要将文件梳理流程固定为迭代代办，项目文件可以为新人提供指导，是重要且有
意义的工作。 
(5) 增加风险监督人 
风险监督人的职责为确保风险的状态可以被监控到位并推动风险可视化。 
6.2.4 强化员工培训 
(1) 组织集体的敏捷培训学习 
培训不仅面向IT 团队，也面向业务团队。这可以使业务团队能更好的理解优先级排
序的意义和灵活应对变化的新型合作方式。 
(2) 邀请金融领域合规专家针对行业相关法律法规、制度进行培训讲解 
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 52 - 
组织单位内部相关职能部门和技术研发部门学习，并对受训人员的学习效果进行结
业考试，并把培训成绩纳为评优考核项。尤其是技术研发部门是项目开发的具体实施部
门，只有开发人员有合规意识，系统中才能有清晰的反馈。 
(3) 组织外部专家来培训 
联系已在金融服务领域取得优秀成绩的其他企业或者是在管理规模上有优秀建设的
组织，来为专业岗位人员进行“外部培训”，了解其他公司在项目执行过程中学习到的
经验，搭建沟通平台，为潜在风险提供应对思路 
(4) 组织开发人员及技术人员编写业务办理流程规范及系统操作手册，项目执行人
员可以通过资料学习加深对项目了解程度，并有利于后续接触的专岗人员操作时有理可
依有据可循。 
6.2.5 修整技术细节 
(1) 增大在需求分析阶段的投入 
业务分析师在产品构思阶段初期就规划出业务需求与用户需求，并在理清业务需求
与用户需求的过程中明确流程的具体业务对象。 
(2) 提早规划非功能性需求 
开发人员在产品构思阶段就要梳理清楚功能性需求与非功能性需求，并在这些需求
中明确的区分出性能需求，环境需求，安全保密需求等。 
(3) 关键模块使用成熟的技术，尽量避免新技术的不适用性 
(4) 开发与测试节奏同步 
测试团队要与开发团队同一时间开始迭代工作，要基于用户故事进行测试用例的编
写，在测试用例编写结束后组织与业务分析师和开发团队进行讨论补充。 
(5) 分析师之间的信息要同步 
非驻场业务分析师将设计方案同步给驻场业务分析师，由驻场业务分析师与业务团
队讨论交付周期合理性。如四方均无任何异议，将定义详细的开发计划，并与接口上下
游同步上线周期，协同联调测试的时间。以上都没有问题，会将用户故事拿到下一个迭
代来进行开。 
(6) 遵循DF 项目开发的逻辑 
开发团队使用敏捷管理方法进行实践，针对初始代码的设计保持持续集成的理念、
不要用固定的框架去开发，在代码中留有可扩展性，开发人员内部在将代码递交给测试
人员之前提前进行代码的互相评审的、使用自动化的方式进行代码静态检查。 
东北大学硕士学位论文     
第6 章 DF 银行外汇交易数据报表平台开发项目风险的应对与控制 
 
- 53 - 
(7) 驻场业务分析师要参与验收 
每一次最小单位功能上线前，业务测试都要参与检验，以正式验收功能。 
6.3 小结 
本章为DF 银行外汇交易数据报表平台开发项目中遇到各个风险如何进行策略管理
提供了方法和建议，提高风险预测范围的覆盖面积，降低项目受风险影响的可能性，希
望不仅能够帮助当前项目有条不紊的进行也为其他类似项目的项目提供参考。 
东北大学硕士学位论文     
第7 章 结论与展望 
 
- 54 - 
第7 章 结论与展望 
7.1 结论 
本论文展开研究的原因是因为DF 公司第一次将集团级别的战略开发项目交给到中
国的开发中心来进行开发，无论是集团领导还是公司的管理层对项目重视程度高，需要
对项目风险进行系统的分析评估及提前控制。论文的主要工作成果如下： 
在项目预研阶段，成立了项目风险管理小组，牵头编制项目风险管理制度，其中包
含细致的风险管理流程、组织结构、个人职责。依照风险管理流程展开对项目风险的识
别评价与控制。 
风险识别使用的方法为文献调查法、头脑风暴法与德尔菲分析法分析项目风险。通
过阅读大量文献，对比和整理当前项目的特征，梳理出项目面临的普遍风险和独有风险，
并在此基础上总结出初始风险清单。而后使用此清单当作参照标的物，结合项目本身的
开发计划召集项目开发相关人员使用头脑风暴法邀请开发团队参与讨论，基于项目实施
计划按照产品构思、需求分析、需求管理、需求实现、需求验收、产品运营，六个阶段
来分别讨论风险，得到总计二十个风险因素。基于此初版风险清单，邀请外部专家问询
专家建议，再梳理项目风险删除低风险因素、补充其他潜在风险因素、对风险因素重新
分类后对风险提出建设性方案。得到结论一级风险有四项、二级风险有十五项。 
风险评价使用的方法为层次分析法与模糊评价法，使用层次分析法建立风险评价模
型与矩阵，从上至下的分解并采用逻辑递进的方式列举出每一个层次的影响因素，将风
险细分到最小颗粒度后对比风险因素间的重要度进行两两打分，计算最大特征值与特征
向量，检验一致后得出权重。基于权重结果再使用模糊评价法对风险进行评估，先选定
评语集再进行打分，确认隶属度。最后得到结论为项目风险程度适中。 
风险控制阶段借鉴德尔菲法时专家给出建议，遵循DF 银行对非重要风险不强制控
制的原则，对较小风险采用风险接受的策略，对较大或一般风险采取风险缓释的应对策
略。针对各风险因子，按照风险等级的不同对每一条风险都给出策略，给出总计15 条
应对策略。重新分析风险可能产生的原因，又针对当前项目提出了总计21 条的控制措
施，涵盖培育文化氛围、调整组织架构等方面。各策略总结形成文件落实为风险控制手
册，由项目风险控制小组管理。值得一提的时，因项目采用敏捷与瀑布式结合的方式联
合开发，在每次迭代的过程中，开发小组也会基于实时情况对项目风险进行分析、提供
合理建议并在下一迭代开发过程中使用方法进行控制。 
东北大学硕士学位论文     
第7 章 结论与展望 
 
- 55 - 
7.2 展望 
论文对DF 银行外汇交易数据报表平台开发项目进行了系统的风险识别风险评估与
风险控制管理，为DF 银行奠定了敏捷与瀑布式联合开发项目的风险管理体系。由于整
个数字化转型项目过于庞大，没能够对每一个子模块都使用本研究方法进行研究且在项
目预研初期，因疫情的不断反复没能让各国的业务需求方加入到项目风险识别的讨论范
围中，导致了有些功能不适用在当地的情况发生。未来随着新一轮需求分析的展开，可
以引入更多的同事参与讨论，争取囊括更多视角。 
东北大学硕士学位论文     
参考文献 
 
- 56 - 
参考文献 
 
[1] 陈振华. 基于大数据技术的银行智能风控体系构建探究[J], 时代金融, 2021, 795(05): 42-44. 
[2] Primadhika M, Teguh R, Bob H, Adi P. Agile project management challenge in handling scope 
and change:A systematic literature review[J], Procedia Computer Science, 2022, 197: 290-
300. 
[3] Rocío R R, Isabel O M, Javier R,Luis B S. Finding the Links between Risk Management and 
Project Success:Evidence from International Development Projects in Colombia[J], 
Sustainability, 2020, 12(21): 92-94. 
[4] Katarina B, Jana Š. Risk management in traditional and agile project management[J], 
Transportation Research Procedia, 2019, 40(1): 986-993. 
[5] Philipp A L, Andreas W. The agility-control-nexus:A levers of control approach on the 
consequences of agility in innovation projects[J], Technovation, 2021, 107: 3-4. 
[6] 陆怡舟, 赵韩婷, 张萌. 商业银行风险管理绩效评价研究——基于AHP-DEA 方法的评价框架
[J],金融监管研究, 2019, 09: 83-98. 
[7] 梅中鹤. 基于ANP 和FCE 方法的EPC 项目监理风险测度实证研究[J], 房地产世界, 2021, 24: 
22-25. 
[8] 蒋晨丽,林丽春,郑惠婷. 基于模糊层次分析法的养生旅游项目风险管理研究——以龙岩市永定
天子温泉为例[J], 山东农业工程学院学报, 2021, 38(10): 51-59. 
[9] 段爱玲,颜宇航,苑天文.基于SWOT 与AHP 融合分析法在风险管理中的应用[J],软件工
程,2020,23(01):40-44 
[10] 李俊蕾, 刘成程, 王超亮, 李庆阳. 不可预见风险因素对项目风险等级影响路径及应对措施研
究——以某热电厂迁建为例[J], 中国工程咨询,2021, 12: 45-49. 
[11] 李晓璇, 方逸文, 石婷, 熊玲. 基于德尔菲法与层次分析法的投资管控及应用实践——以W 银
行为例[J], 投资与创业, 2021,32(20): 67-69. 
[12] 蒋文婷, 路子威, 温颖, 宫成, 牛凯庆. 航天研发项目流程管理与组合风险分析研究[J], 航天工
业管理, 2022, 02: 60-66. 
[13] 卢子健,郭丽华,陈娟等.铁路投融资深化改革背景下的铁路项目评价体系研究[J],铁道经济研
究,2023, 171(01):7-17. 
[14] 杨家辉, 贺健.基于AHP 模型的大数据企业财务风险预警研究[J], 企业科技与发展, 2021(03): 
206-207+210. 
[15] 李杨, 王珊. 基于复杂网络理论的EPC 项目关键风险识别[J], 华北理工大学学报(自然科学版), 
2023, 45(01): 75-80. 
[16] 赵子怡.“一带一路”背景下国际工程项目风险管理研究[J], 砖瓦, 2022(04): 101-104. 
[17] 谭元戎, 冯丽娜, 樊博. 中小金融机构软件项目风险缓降量化方法研究[J], 中国金融电脑, 
2014, 296(03): 30-34. 
[18] 张亚莉, 杨朝君. 多组织研发项目风险管理的模式分析及知识复用研究[J], 工程管理学报, 
2015, 29(03): 100-104. 
[19] 王蔚. 基于熵权TOPSIS 的软件项目风险评估[J], 项目管理技术, 2022, 20(02): 102-107. 
[20] 卢士达, 张露维, 吴金龙等. 基于贝叶斯和大数据分析的业务连续性风险管理[J], 电力与能源, 
2021, 42(02): 159-163+181. 
[21] 潘亮亮. 国有企业境外EPC 项目风险管理研究——以孟加拉燃煤电站码头工程为例[J], 中国
设备工程, 2022, 499(11): 66-68. 
东北大学硕士学位论文     
参考文献 
 
- 57 - 
[22] 王京晶. 基层央行信息化项目立项风险分析与应对[J], 金融科技时代, 2021, 29(08): 51-53. 
[23] 袁世东. 建筑项目风险管理中存在的问题及解决方法[J], 大众标准化, 2022, 12: 58-60. 
[24] 赵子林, 王金亮. 集团型企业数据中台项目风险管控能力提升[J],项目管理技术,2022, 20(03): 
125-129. 
[25] 马健. 基于AHP 层次分析法的主数据管理项目风险因素估计[J], 科技资讯, 2022, 20(07): 49-
51. 
[26] 庞宏秋. T 公司软件项目实施风险管理研究[J], 电脑与电信, 2022, Z1: 58-62. 
[27] 刘馨远. 基于大数据金融的风险与挑战研究[J], 时代金融, 2021, 15: 5-7. 
[28] 朱紫涵. 高速公路海外项目风险分析及策略研究[J], 江西建材, 2022, 05: 286-287. 
[29] 蔡小路. 基于数据治理的信息化项目风险管理研究[J], 中小企业管理与科技(中旬刊), 2021, 09: 
13-15. 
[30] 冉孟超. D 银行IT 风险识别与防控研究[D], 重庆工商大学, 2021. 
[31] 张莉. H 公司软件开发项目进度风险管理研究[D], 山东大学, 2021. 
[32] 童泽华. G 银行数据中心迁移项目的风险管理研究[D], 电子科技大学, 2021. 
[33] 骆鉴. 论国外金融信息化风险管理与控制[D], 吉林大学, 2010. 
[34] Elmar K. The effect of intervening conditions on the management of project risk[J], 
International Journal of Managing Projects in Business, 2008, 1(4): 602-610. 
[35] 柳小军. 项目风险管理在信息系统集成项目中的应用[J],数字技术与应用, 2021, 39(11): 7-9. 
[36] Vuk V, Nebojša D, Vesna S, Mališa S, Jelena S, Yan C, Yasir A, Kittisak J, Hiep V L, Karzan 
W, Ivan R.  Project planning and risk management as a success factor for IT projects in 
agricultural schools in Serbia[J], Technology in Society, 2020, 63(2): 101371. 
[37] 黄梅香. 浅析信息系统项目的风险管理[J], 科技视界, 2021, 30: 171-172. 
[38] 唐张伟. 基于头脑风暴法与流程图法的航空制造企业某改装项目风险识别[J], 江苏科技信息, 
2014, 23: 125-126. 
[39] 谢亚妮. 基于改进层次分析法的企业金融风险分析模型[J], 计算机与数字工程, 2022, 50(04): 
721-725+761. 
[40] 赵彩, 许大炜. 基于层次分析法的网络涉密信息风险评估系统设计[J], 电子设计工程, 2022, 
30(07): 91-95. 
[41] 吕杏. 基于AHP 法的高校设备采购风险定量分析[J], 现代经济(现代物业下半月刊), 2009, 
8(10): 4-6+31. 
[42] 程满亚, 孔祥峰. 层次分析法下高校财务风险衡量与控制研究[J], 西部财会, 2023, 480(03): 
49-51. 
[43] 赵现胜. 基于AHP 模型的融资租赁业务风险评价及控制研究[J], 西部金融, 2022, 571(03): 52-
64. 
[44] Zadeh L A. Electrical engineering at the crossroads[J], IEEE Trans. Educ. 1965, 8(2): 30–33. 
[45] Shan F, Xu L D. Decision support for fuzzy comprehensive evaluation of urban development. 
Fuzzy Sets Syst. 1999, 105(1): 1–12. 
[46] 黄晓斌. 工程项目风险意识防范问题研究[J],湖南农机, 2011, 38(03): 230-231. 
[47] 刘婧. A 公司智慧图书馆软件产品敏捷开发风险管理研究[D], 浙江大学, 2021. 
[48] 邓大鹏. 金融市场风险敏捷分析系统的研究和实现[D], 上海交通大学, 2008. 
[49] 毛利君. Z 银行金融软件项目需求管理研究[D], 西南财经大学, 2020. 
[50] 张志祥. N 银行软件项目的混合开发模式研究[D], 北京交通大学, 2021. 
 
东北大学硕士学位论文     
致谢 
 
- 58 - 
致谢 
 
在论文即将结束之际,特向所有关心和帮助过我的老师、同学、同事、朋友和亲人们
致以诚挚的谢意。本论文在导师的悉心指导下按时完成。从论文的方向选择、研究思路、
论文架构等方面，导师都付出了大量的心血，一次次地给予启发和指导。在此对老师博
学的知识和宽厚待人的风范致以崇高的敬意，对老师的悉心指导致以衷心的感谢！我还
要感谢我所在企业的同事们，感谢你们在项目繁重的同时，耐心回答我的问题，并为我
的论文写作提供了大量素材。感谢我的家人，谢谢你们对我学业的支持。更重要的谢谢
东北大学2022 级MEM 二十班的同学们，虽然两年半的学业过程中，不断地被疫情阻
隔我们的线下见面时光，但疫情并没有阻断我们的沟通，谢谢你们对我在平时学习中的
帮助，论文期间对我论文进度的关心，此致敬意。 
 
