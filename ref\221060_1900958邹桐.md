 
 
 
 
NORTHEASTERN 
U N I V E R S I T Y  
 
 
 
硕士学位论文 
THESIS FOR MASTER'S DEGREE 
 
 
 
论文题目 
非完美信息下多智能体的合作式对抗博
弈算法的研究 
作    者 
邹桐 
学    号 
1900958 
学 院(部) 
信息科学与工程学院 
专    业 
控制科学与工程 
指导教师 
王骄 教授 
 
 
2022 年7 月 
 
 
 
 
 
 
 
分类号                     密级            
UDC                     
 
学  位  论  文 
 
非完美信息下多智能体的合作式对抗博弈算法的
研究 
 
 
 
作 者 姓 名： 邹桐 
作 者 学 号： 1900958 
指 导 教 师： 王骄 
学       院： 信息科学与工程学院 
申请学位级别： 硕士 
 
 
学科专业名称： 控制科学与工程 
论文提交日期： 2022 年6 月 论文答辩日期： 2022 年6 月 
学位授予日期： 2022 年7 月 答辩委员会主席： 薛定宇 教授 
评   阅   人： 薛定宇 教授  王亚杰 教授 
 
 
 
 
东北大学 
2022 年6 月 
 
 
 
 
 
 
 
A Thesis in Control Science and Engineering 
 
 
 
A Study of Cooperative Adversarial game 
Algorithm for Multi-agent under Imperfect 
Information 
 
 
 
By Zou Tong 
 
 
 
Supervisor: Professor <PERSON> Jiao 
 
 
 
 
 
 
 
 
 
 
 
 
Northeastern University 
June 2022 
 
 
 
 
 
 
 
 
 
I 
 
独创性声明 
本人声明，所呈交的学位论文是在导师的指导下完成的。论文中
取得的研究成果除加以标注和致谢的地方外，不包含其他人己经发表
或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。
与我一同工作的同志对本研究所做的任何贡献均己在论文中作了明
确的说明并表示谢意。 
                                学位论文作者签名：
 
日    期：2022.6.12 
学位论文版权使用授权书 
本学位论文作者和指导教师完全了解东北大学有关保留、使用学
位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的
复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学
位论文的全部或部分内容编入有关数据库进行检索、交流。 
 
 
作者和导师同意网上交流的时间为作者获得学位后： 
 
半年 □    一年□    一年半□       两年 
 
学位论文作者签名：           导师签名： 
签字日期：2022.6.12           签字日期：2022.6.12 
 
 
 
II 
 
 
 
 
 
 
东北大学硕士学位论文                                                       摘要 
 
 
III 
 
摘  要 
机器博弈是人工智能的关键领域之一。通过对机器博弈的研究，可以解决
很多实际问题，例如量化交易、智能推荐、无人驾驶和多轮对话等应用场景。
基于博弈参与者对博弈信息的掌握程度，可以将其划分成完美信息博弈和非完
美信息博弈。然而，日常生活和研究过程中的大多数博弈都被认为是非完美信
息博弈过程，研究非完美信息博弈显然更具现实意义。 
相较于完美信息博弈，非完美信息博弈通常具有较大的不确定性和较多的
参与者，其求解更具挑战性。一方面，由于隐藏信息存在，博弈中状态动作空
间大、复杂度高，传统的基于博弈树搜索方法效率低；另一方面，目前对非完
美信息博弈的研究大多只考虑对抗，不考虑合作，基于合作式对抗的非完美信
息多智能体博弈是亟待解决的问题和充满前景的研究领域。因此，本文提出了
一个全新的非完美信息多智能体博弈算法，主要工作如下： 
（1）针对非完美信息多智能体博弈中参与者之间的合作问题，提出了一种
基于深度队友建模的隐式合作算法。通过设计两个用于队友合作的TWMIP-Net
网络和TWMPP-Net 网络，对博弈中的队友信息和队友策略进行预测。两个网
络的输入是自身手牌、队友手牌、公共知识以及全局状态，TWMIP-Net 的输出
是预测的队友牌型，TWMPP-Net 的输出是预测的队友牌力。最后，通过对队友
信息的预测和对队友策略的预测训练更为智能的合作智能体，实验结果证明了
所提出算法的有效性。 
（2）针对非完美信息多智能体博弈中合作式对抗的求解问题，本文通过引
入深度神经网络改进了传统的蒙特卡洛算法。首先，对奖励值进行优化设计，采
用一种基于观察的策略来确定高价值智能体的轨迹，利用跟踪模式追踪其路径，
从而为智能体提供更高效的搜索信息，提高学习效率，解决了博弈中存在的稀疏
奖励问题。其次，设计了TWMDMC-Net，将TWMIP-Net 和TWMPP-Net 网络作
为TWMDMC-Net 网络输入的一部分，实现了深度强化学习中的队友建模。最后，
与DeltaDou、RHCP、Random 以及CQN 等流行方法进行对比实验，证明了所提
算法在非完美信息多智能体合作式对抗问题中优于传统的求解算法。 
关键词：非完美信息博弈；多智能体系统；深度蒙特卡洛算法；队友建模 
东北大学硕士学位论文                                                       摘要 
 
 
IV 
 
 
 
东北大学硕士学位论文                                                    Abstract 
 
 
V 
 
Abstract 
The Machine game is one of the key areas of artificial intelligence. Through the 
study of machine games, many practical problems can be solved, such as quantitative 
trading, intelligent recommendation, driverless, multi-round dialogue and other 
application scenarios. Based on the degree of game participants' mastery of game 
information, they can be classified into perfect information games and non-perfect 
information games. However, most of the games in daily life and research process are 
considered as non-perfect information game processes, and it is obviously more 
practical to study non-perfect information games. 
Compared with perfect information games, imperfect information games usually 
have larger uncertainty and more participants, and their solutions are more challenging. 
On the one hand, due to the existence of hidden information, the state action space in 
the game is large and complex, and the traditional game tree based search method is 
inefficient; on the other hand, most of the current research on non-perfect information 
games only consider confrontation, not cooperation, and the non-perfect information 
multi-intelligent game based on cooperative confrontation is an urgent problem and a 
promising research area. Therefore, in this paper, we propose a new algorithm for non-
perfect information multi-intelligent games, and the main work is as follows. 
(1) An implicit cooperation algorithm based on deep teammate modeling is 
proposed for the cooperation problem between participants in non-perfect information 
multi-intelligent games. By designing two TWMIP-Net networks and TWMPP-Net 
networks for teammate cooperation, the teammate information and teammate strategies 
in the game are predicted. The inputs of the two networks are own hand, teammate's 
hand, public knowledge and global state, the output of TWMIP-Net is the predicted 
teammate's hand, and the output of TWMPP-Net is the predicted teammate's hand 
strength. Finally, the experimental results demonstrate the effectiveness of the proposed 
algorithm by training more intelligent cooperative intelligences through the prediction 
of teammate information and the prediction of teammate strategies. 
东北大学硕士学位论文                                                    Abstract 
 
 
VI 
 
(2) To address the problem of low search efficiency in imperfect information 
games, this paper improves the traditional Monte Carlo tree search algorithm by 
introducing deep neural networks, and uses the deep Monte Carlo algorithm as a multi-
intelligent game decision algorithm. First, the reward value is optimally designed, an 
observation-based strategy is used to determine the trajectory of high-value 
intelligences, and their paths are tracked using tracking patterns, thus providing more 
efficient search information for the intelligences and improving the learning efficiency, 
which solves the sparse reward problem existing in the game. Secondly, TWMDMC-
Net is designed to implement teammate modeling in deep reinforcement learning by 
using TWMIP-Net and TWMPP-Net networks as part of the TWMDMC-Net network 
input. Finally, comparison experiments with popular methods such as DeltaDou, RHCP, 
Random, and CQN demonstrate that the proposed algorithm outperforms traditional 
solution algorithms in non-perfect information multi-intelligent cooperative adversarial 
problems. 
 
Key words: imperfect information game; multi-agent system; deep monte carlo 
algorithm; partner modeling;  
 
东北大学硕士学位论文                                                       目录 
 
 
VII 
 
目  录 
独创性声明 ......................................................................................... I 
摘  要 ............................................................................................... III 
Abstract ............................................................................................... V 
第1 章 绪论 ...................................................................................... 1 
1.1 研究背景及意义............................................................................................. 1 
 研究背景.............................................................................................. 1 
 研究意义.............................................................................................. 2 
1.2 国内外研究现状............................................................................................. 3 
1.3 本文主要研究内容及结构框架..................................................................... 6 
 本文主要研究内容.............................................................................. 6 
 结构框架.............................................................................................. 7 
第2 章 相关基础理论知识 .............................................................. 9 
2.1 引言................................................................................................................. 9 
2.2 博弈论相关基础理论..................................................................................... 9 
 机器博弈类型...................................................................................... 9 
 多智能体的关系类型........................................................................ 11 
 非完美信息博弈................................................................................ 13 
2.3 深度学习概述............................................................................................... 15 
 深度学习基础.................................................................................... 15 
 循环神经网络.................................................................................... 16 
2.4 多智能体博弈相关算法............................................................................... 22 
 强化学习算法.................................................................................... 22 
 深度强化学习算法............................................................................ 25 
2.5 本章小结....................................................................................................... 27 
东北大学硕士学位论文                                                       目录 
 
 
VIII
 
第3 章 扑克博弈分析与仿真平台搭建 ........................................ 29 
3.1 引言............................................................................................................... 29 
3.2 扑克博弈问题描述....................................................................................... 29 
3.3 扑克博弈仿真平台....................................................................................... 30 
 仿真平台选取.................................................................................... 31 
 博弈环境介绍.................................................................................... 32 
3.4 RLCard 仿真平台环境配置 .......................................................................... 35 
 博弈系统框架.................................................................................... 35 
 动作-状态表示 .................................................................................. 36 
3.5 本章小结....................................................................................................... 37 
第4 章 基于深度队友建模的隐式合作算法 ................................ 39 
4.1 引言............................................................................................................... 39 
4.2 非完美信息博弈中的队友合作问题........................................................... 39 
4.3 队友建模算法............................................................................................... 41 
4.4 隐式合作队友建模网络............................................................................... 43 
 博弈信息编码.................................................................................... 43 
 基于信息的队友预测网络................................................................ 45 
 基于策略的队友预测网络................................................................ 47 
4.5 实验结果及分析........................................................................................... 48 
 实验环境............................................................................................ 48 
 训练策略............................................................................................ 48 
 实验结果............................................................................................ 49 
4.6 本章小结....................................................................................................... 51 
第5 章 基于深度蒙特卡洛的博弈决策算法 ................................ 53 
5.1 引言............................................................................................................... 53 
5.2 深度蒙特卡洛算法....................................................................................... 53 
5.3 奖励值优化设计............................................................................................ 55 
5.3.1 奖励值稀疏问题................................................................................. 55 
东北大学硕士学位论文                                                       目录 
 
 
IX 
 
5.3.2 奖励函数设计..................................................................................... 56 
5.4 合作式博弈决策网络设计........................................................................... 57 
5.5 实验结果及分析........................................................................................... 59 
5.5.1 训练过程............................................................................................ 59 
 对比实验............................................................................................ 60 
 实验结果............................................................................................ 61 
5.6 本章小结........................................................................................................ 63 
第6 章 总结与展望 ........................................................................ 65 
6.1 本文总结....................................................................................................... 65 
6.2 工作展望....................................................................................................... 66 
附录 ................................................................................................... 67 
附录A 斗地主基本规则 .................................................................................... 67 
附录B 斗地主出牌规则 .................................................................................... 67 
参考文献 ........................................................................................... 69 
致谢 ................................................................................................... 77 
攻读硕士学位期间主要成果及获奖情况 ....................................... 79 
 
 
 
东北大学硕士学位论文                                                       目录 
 
 
X 
 
 
 
东北大学硕士学位论文                                                        第1 章 绪论 
-1- 
 
第1章 绪论 
1.1 研究背景及意义 
 研究背景 
计算机的发明到目前已经有很长时间，接着映入人们眼帘的便是人工智能
（Artificial Intelligence）。一直以来，大量的专家学者对人工智能进行探索，而多
智能体技术（multi-agent technology）自然也引来很多优秀人士的关注。自20 世
纪80 年代，诞生了对多智能体领域的研究，并于90 年代中期获得广泛的认可
[1]。现如今，多智能体已经成为人工智能领域的热点话题，其在感知、推理、学
习、决策等方面体现出强大的智能性。研究多智能体技术的作用是解决单一智能
体所不能达到的目标，如对大型、复杂的现实科学问题进行求解，需要综合考虑
到智能体与智能体直接的合作和对抗。目前，采用多智能体技术的领域越来越多，
例如交通控制、机器人编队、航迹规划、能源分配、智能电网、生产制造、军事
无人机控制等，多智能体技术对人类的现实生活具有极高的研究价值和意义。如
今对于多智能体的研究很大一部分集中在多智能体的博弈问题上。 
多智能体情况下的博弈理论作为人工智能领域的一个重要分支，在不断向前
发展，被许多学者进行研究和探索。依照博弈参与人拥有的信息程度，机器博弈
可以被分为完美和非完美两种类型的信息博弈。完美信息博弈是指博弈方掌握一
致的信息，其他参与人当下的状态、当前的行为动作、支付等都是被对方知晓的，
信息在博弈时是透明的，没有任何隐藏，全部且准确的信息都能被各个参与者获
悉，如在社会中很受欢迎的围棋游戏以及国际象棋游戏等。在非完美信息博弈中，
对手的信息是被隐藏的，无法观察出来，每个参与者没有办法得到所有的信息，
因此信息是不对称的，如桥牌、德州扑克等。和完美信息博弈相比较，在非完美
信息博弈中，隐藏的对手信息导致决策者对于对手策略和博弈态势的认识是非常
有限的，己方不能拥有敌方的所有信息，各个参与者获得的信息也是不对称的， 
因此己方拥有的信息和敌方拥有的信息是有区别的，并且非完美信息博弈中存在
着状态空间非常庞大和搜索空间巨大的问题，因此对它的深入研究是一个比较大
的挑战。然而，在现实世界中，大多数博弈都被认为是非完美信息博弈，例如交
通路线、电子商业、贸易竞争等，因此解决非完美信息博弈问题将会对人类有巨
东北大学硕士学位论文                                                        第1 章 绪论 
-2- 
 
大帮助。 
通过对多智能体博弈的深入研究，可以使其与人类智力更加贴近，从而有助
于帮人们处理现实生活中的很多问题。其中，计算机棋类游戏是人工智能的一个
热门研究课题，人工智能技术的快速发展使棋类游戏领域的计算机实现研究也日
趋成熟。特别是中国象棋、国际象棋和围棋的人工智能程序已经发展到可以直接
与人类高手对弈并获胜的程度。“深蓝”是这方面的最好证明。 
然而，在非完美信息博弈下，对纸牌游戏的研究还没有达到像棋类游戏那样
的智力水平。这主要是因为棋类游戏中的信息是清晰明确的，可以在一定的数据
结构中直接提供给计算机，以做出智能决策，而在纸牌游戏中，信息是不完整的，
也就是说，除了目前已经打出的牌，不知道其他玩家还有什么牌可以用，所以计
算机程序需要从一些手牌信息中推断可能的牌型，以决定下一步的玩法。 由于
信息的非完美性和不对称性，牌类游戏求解方法在搜索策略上并不完全等同于棋
类游戏方法，同时进一步增加了牌类游戏解题方法的计算开销。 这也是纸牌游
戏研究没有跟上棋类游戏步伐的主要原因。 
本文以斗地主作为研究对象，对非完美信息下多智能体的合作式对抗博弈算
法进行研究。现实中大部分环境是既充满合作也充满对抗的，如果能够做好这种
非完美信息下的多智能体博弈，那么我们就可以对很多实际生活中遇到的问题进
行建模了。本课题的研究是接近现实的抽象，通过这项研究，我们可以发掘相关
算法和积累相关技术，便于日后落地到实际应用，对人类日常生活创造出更广阔
的实际应用价值。 
 研究意义 
机器博弈之所以被称作“果蝇”，是因为人类可以从计算机游戏中获得许多
新颖的、有用的成果。这些成果可以用来解决更多的现实问题。非完美信息博弈
更接近现实生活，在日常生活中随处可见。与此同时，我们生活中遇到的博弈大
多都是即存在合作又存在对抗的，并且是多人对多人的，并不仅仅局限于双人。
因此本课题的研究具有极大现实意义，例如可以促进斗地主软件打牌技术的提高，
使其更加智能化，更好的服务于斗地主玩家；且人工智能研究的目的，不仅仅是
让计算机与人类对弈，而是通过对高智能和竞争性游戏的研究，检验某些人工智
能技术是否能模拟人类的智慧。模拟对局的过程也能进一步促进人工智能技术的
研究。 
东北大学硕士学位论文                                                        第1 章 绪论 
-3- 
 
科学研究应该永远先于工业应用，并面向未来。在人工智能的历史上，许多
精妙的算法和理论都是从研究游戏开始的，这使得学者们在研究过程中积累了大
量的知识和技能，并最终投入了实际应用。例如，博弈论研究可以从最简单的囚
徒困境游戏开始，许多机器博弈算法基本上是在游戏的基础上不断发展的。 虽
然现实世界的应用往往过于复杂，但游戏规则清晰且易于建模，使其成为科学研
究的良好课题，从游戏研究入手来探索新的理论和技术然后拓展到实际是一个自
然的过程。在AI 的发展历史上，游戏起到非常重要的作用，它是一项比较偏基
础性的研究，这些研究能够推动整个领域的发展。因此，研究游戏的解法是很有
意义的。国际象棋软件的棋力已经达到了很高的水平，国内外的棋手都在使用这
样的软件进行辅助训练，并且取得很好的效果。斗地主作为典型的非完美信息下
多智能体的合作式对抗博弈，更接近于实际生活，应用范围更加广泛，蕴藏在其
中的理论与方法可以帮助我们更好的应用到实际生活中，解决生活中的博弈问题，
比如解决社会中的商业竞争、军事战争、股票市场、国际关系等真实的博弈场景。
因此，本课题的研究具有重要的实际应用价值。 
1.2 国内外研究现状 
机器博弈有着悠久的发展历史，到如今，前辈们为我们创造了丰富的研究成
果和宝贵的研究经验。 
国外对机器博弈的研究起初是从完美信息博弈开始。在对完美信息博弈的研
究领域中，涌现了许多具有里程碑意义的成绩，尤其是在下棋领域取得了丰硕成
果。在20 世纪五十年代，塞缪尔发明了一种跳棋程序，这个编程打败了塞缪尔
本人，并且在六十年代打败了另一个州的棋手。1979 年，国际象棋双陆棋锦标赛
上，鲁吉·维拉曾的双陆棋大师。1979 年，西洋双陆棋世界冠军Luigi Villa 在一
项比赛中被Berliner 教授设计的西洋双陆棋计算机程序打败[2]，这是公认的智力
活动的世界冠军第一次被智能体击败；由美国科学家设计的计算机程序
Chinook[3]在1994 年成功击败了世界跳棋冠军Marion Tinsley，至此，在西洋跳棋
领域，计算机程序实现了首次突破；1997 年，美国IBM 公司研制的一款电脑国
际象棋“深蓝”[4]依靠强大的运算速度以及Alpha-Beta 搜索方法在6 局对弈中将
当时的人类国际象棋世界冠军Garry Kasparov 击败，再次创造了电脑程序战胜人
类的新纪元；2006 年，一种将树搜索与 Monte-Carlo 评估相结合的新框架被提
出[5]，该算法在一个9×9 围棋程序Crazy Stone 中赢得了第10 届KGS 计算机围
东北大学硕士学位论文                                                        第1 章 绪论 
-4- 
 
棋锦标赛。2016 年，AlphaGo 由DeepMind 公司研发。该方法利用价值网络对棋
局定位和策略网络进行筛选，并利用蒙特卡洛树进行搜索，以4-1 击败世界围棋
冠军、职业九段选手李世石[6]，同年10 月，新版程序AlphaGo Zero 的研究成果
通过国际学术期刊《Nature》揭开了面纱。AlphaGo Zero 这一程序可以从空白状
态开始, 在没有任何人类棋谱输入的状态下进行自我对弈,自学围棋。并且，
AlphaGo Zero 刚出世不久就以100：0 的胜率一举击败了AlphaGo，创下了惊人
的记录。至此，一直以来在完美信息博弈中较难解决的围棋博弈被成功解决，这
标志着人工智能在完美信息博弈问题上取得了前所未有的壮举。 
随着研究的深入，许多学者开始探索非完美信息博弈领域。由于非完美信息
博弈具有搜索空间大、信息不透明等特点，因此对其的研究难度更大、挑战更高，
但也正因如此，研究者们纷纷对其痴迷，想在该领域获得一些成果。事实证明，
前辈及学者们做到了，他们在非完美信息博弈领域取得了满意的成就。1995 年，
Billing 开始研究扑克游戏，打开了迈向非完美信息博弈的大门。2000 年，博弈
论中的一个及其重要的概念--遗憾值匹配算法[7]被Hart 和Mas-Colell 所提出，该
算法以虚拟遗憾最小化算法为标志，是一种迭代策略求解算法,在二人零和博弈
中可以收敛到纳什均衡点，并且由于该算法的提出，游戏的胜率被显著提高但其
也具有一定的局限性，只能应用于正则式博弈模型的求解问题中。2003 年，两人
有限德州扑克的博弈论最优策略被提出，提出这一方法的人是阿尔伯塔大学电脑
扑克研究小组(UACPRG，University of Alberta Computer Poker Reasearch Group)[8]，
基于这一策略创建的程序已经实现了较高水平的博弈。2006 年，阿尔伯塔大学
的Martin Zinkevich 和Michael Bowling 等人提出了基于DIVAT 的无偏估计法[9]，
将无偏估计器应用于游戏扑克，显著降低了方差并且能更快速的评估。2007 年，
一种为大型扩展式博弈寻找近似解的新技术[10]被提出，该技术基于遗憾最小化，
使用一种称为反事实遗憾的新概念，该方法充分利用了扩展博弈论中的不完备性，
它可以最小化整体遗憾，计算纳什均衡，解决了多达
12
10 个游戏状态的扑克抽象
问题，比以往的算法有将近四个数量级的提升，并且使扑克游戏程序的强度有所
提高。但是，在一些复杂程度较高的扑克博弈方面，该方法获得的纳什均衡解有
可能存在较大差异。基于该原因，通常将原问题分解为主体及多个子博弈分别进
行求解，所提出的典型解法包括Re-solving[11]、Endgame solving[12]、Max-margin 
Refinement[13]等，但所达到的效果并不理想。2008 年，由阿尔伯塔电脑扑克团队
东北大学硕士学位论文                                                        第1 章 绪论 
-5- 
 
研制的Polaris 智能体，可以根据玩家过去的游戏风格推测出下一步的打法，并
在拉斯维加斯人机德州扑中，接连打败6 位德州顶尖的德州职业扑克高手，这也
是第一次由智能电脑战胜人类职业玩家。这为机器博弈打开了新的篇章，也将人
工智能又进一步推向了热潮。2012 年，Richard Gibson 等人提出了一种将估计量
的方差与直接从估计量计算遗憾的算法的收敛速度联系起来的方法[14]，并在
Goofspiel、Bluff 和德州扑克等博弈中进行了验证了该算法的收敛速度。2015 年，
Michael Bowling 等人提出了CFR+[15]算法，该算法能够解决数量级大的扩展式博
弈。2017 年，Matej Moravč ík 等人提出了DeepStack 算法用于解决非完美信息博
弈问题，DeepStack 将递归推理技术用于处理信息的不对称性、分解从而将计算
重点放在相关的决策上，以及一种利用深度学习来自主学习自己下棋的直觉方法。 
作为第一款打败专业牌手的电脑程式，DeepStack 在一场无注德州牌游戏中，
无疑是人工智能的一个重大里程碑。DeepStack 将递归推理技术用于处理信息的
不对称性、分解从而将计算重点放在相关的决策上，以及一种利用深度学习来自
主学习自己下棋的直觉方法，在一种由44,000 种不同类型的手牌组成的单对单
无限扑克牌的研究中，打败了职业牌手，并且在复杂度高达
160
10
的无限注德州扑
克上达到职业玩家水准[16]。作为第一款在无限注德州扑克游戏中打败专业牌手
的电脑程序，DeepStack 无疑是人工智能的一个重大里程碑。与此同时，卡耐基
梅隆大学（CMU）的Noam Brown 和Tuomas Sandholm 设计出了Libratus 系统
[17]，提出了一种新的子博弈求解方法，博弈策略由于对搜索深度的限制得以被优
化，德州扑克智能体的性能也得到了极大提升，降低了开发智能体所需的硬件条
件及计算资源，且应用Nested Endgame Solving[17]和AS[18]以及OOS[19]技术，使
无限注德州扑克达到了高效及精确求解。至此，人工智能在非完美信息博弈领域
取得超前发展，成功突破了非完美信息博弈长期以来存在的挑战。2019 年，美国
卡内基梅隆大学的诺姆布朗和图马斯·桑德霍设计了一种新型的智能系统 
Pluribus。它改进并升级了Libratus，该成果被发表在Science 上的一篇期刊中。
Pluribus 系统策略的核心是自学习训练，采用了改进的蒙特卡洛虚拟遗憾最小化
算法，同时使用行动抽象（Action abstraction）和信息抽象（Information abstraction）
技术[20]，能够在6 人牌局的无限注德州扑克中击败最厉害的专业玩家，但它无法
调整其策略来利用对手的特殊弱点。 
与国外对机器博弈研究的快速发展相比，国内对机器博弈理论的研究脚步相
东北大学硕士学位论文                                                        第1 章 绪论 
-6- 
 
对缓慢，且起步较晚，近些年才在国内社会和大学高校传播开来。大部分国内对
其的研究是从典型的棋牌类游戏开始的。中山大学的陈志行教授是中国电脑围棋
博弈的先行者，是中国电脑围棋走向世界的见证人。1991 年陈教授从中山大学
退休后，潜心研究开发电脑围棋软件。陈教授的围棋程序“手谈”以其卓越的战
斗智能而闻名，在国际上远为先进，对弈过程中创新的走法不断出现，有时还能
惊现专家式的走法，令当时的电脑围棋界研究人员和围棋爱好者耳目一新。之后
的“手谈”软件在陈志行教授及研发团队的精心改造下，围棋水平不断提高，在
一些国际计算机围棋比赛中连续获胜，包揽了当时大部分的世界比赛的冠军。自
1993 年起，共9 次获得电脑围棋世界冠军。“手谈”软件的成功引发了中国围棋
人工智能研究的浪潮。自2003 年起，东北大学的徐心和教授开始从事机器博弈
的研究工作，对象棋展开了研究，先后提出了走步分析、博弈树搜索[21]等方法，
对象棋博弈进行了大量实验和探究，并于2004 年成立“棋天大圣”代表队。2006
年8 月，首届在北京举行的中国象棋计算机博弈锦标赛中[22]，“棋天大圣”一举
拿下了冠军。哈尔滨工业大学的王轩实验室在2013 年的世界计算机扑克大赛
（ACPC）中获得二人限制性德州扑克游戏第四名，且在2017 年的比赛中荣获第
三名[23]。2020 年，CARC 智能博弈决策组在第二届全国多智能体对抗博弈挑战
赛中斩获佳绩。本次比赛，由王轩教授带领的鹏城实验室人工智能研究中心与哈
尔滨工业大学（深圳）计算机应用研究中心联合构建的“九凤”获得异构赛事第
四名，荣获该赛事三等奖。本次参赛是团队在多智能体混合博弈领域的首次尝试，
两家单位在非完全信息博弈、大规模信息处理、强对抗博弈的深度学习和强化学
习、群体智能决策等理论与技术方面开展了多年的研究工作，取得了大量创新研
究成果。到目前，非完美信息下的多智能体博弈打开了新的篇章，人工智能也又
进一步被推向了热潮。 
1.3 本文主要研究内容及结构框架 
 本文主要研究内容 
本文以扑克游戏斗地主为研究对象，通过研究斗地主中存在的博弈行为，从
而对所属机器博弈的非完美信息博弈求解问题进行研究。针对现存的非完美信息
博弈算法相关知识，基于其存在的弊端，从具有大规模应用的博弈背景出发对算
法进行研究。本文的主要研究内容如下： 
东北大学硕士学位论文                                                        第1 章 绪论 
-7- 
 
（1）设计了队友信息预测网络——TWMIP-Net 网络。对队友手中的牌型进
行预测，从而更好地和队友进行合作，结合自身牌型和队友牌型，以获得更高的
收益。在非完美信息博弈中，由于博弈参与者都看不见其他博弈者的招法，因此
相对于完美信息博弈，参与者的决策更加依赖于他们对队友的牌型的估计，这充
分说明了建立准确的队友信息预测模型至关重要。本文在多人合作式对抗博弈中
建立了一个通用的队友建模方法使得智能体能够基于该模型得到自身的策略。该
模型会根据预测结果，指导自身的出牌。  
（2）设计了队友策略预测网络——TWMPP-Net 网络。对队友手中的牌力进
行预测，从而对队友的牌力情况有一个宏观的了解，以便在出牌过程中更好地配
合，更好地和队友进行合作，最终实现获得更高收益的目标。在非完美信息博弈
中，由于博弈参与者都看不见其他博弈者的招法，因此相对于完美信息博弈参与
者的决策除了依赖于他们对队友信息的预测，也依赖于对队友策略的预测，因此
建立准确的队友策略预测模型至关重要。本文在多人合作式对抗博弈中建立了一
个通用的队友建模方法使得智能体能够基于该模型得到自身的策略。该模型会根
据预测结果，指导自身的出牌。 
（3）将队友建模与深度蒙特卡洛搜索（DMC）算法的更新过程结合起来，
设计了TWMDMC-Net 网络结构。所设计的网络将TWMIP-Net 与TWMPP-Net
作为新的输入，即在强化学习中加入了队友建模的思想，同时设计了访问函数以
缓解稀疏奖励的问题。然后将信息集和历史行为作为输入，将Q 值作为输出。 
（4）最后针对以上方法，设计并实现了在斗地主博弈模型背景下的仿真实
验，对算法进行了验证，给出了具体的实验结果。 
 结构框架 
论文的整体结构共分为六章，具体安排如下： 
第1 章：绪论。首先对本论文的研究背景及意义进行介绍，然后按时间发展
顺序分别介绍了完美信息博弈和非完美信息博弈的国外研究现状以及机器博弈
的国内研究现状，最后对本文的主要研究内容以及各章节内容做出介绍。 
第2 章：相关基础理论知识。对机器博弈相关的理论基础进行重点介绍，从
博弈论的有关基础理论知识，到非完美信息博弈问题，还介绍了深度强化学习相
关概念以及多智能体的关系类型和对应求解方法。 
第3 章：扑克博弈分析与仿真平台。本章主要介绍了扑克博弈分析和本文的
东北大学硕士学位论文                                                        第1 章 绪论 
-8- 
 
实验仿真平台RLcard。首先简单介绍了扑克博弈问题描述，然后介绍了仿真平
台的选取缘由以及RLcard 的博弈环境，最后介绍了RLcard 仿真平台的环境配
置。本文在RLcard 平台上对算法和实验进行了验证。 
第4 章：基于深度队友建模的隐式合作方法。本章首先介绍了非完美信息博
弈中的队友合作问题，然后介绍了目前存在的队友建模方法，接着提出了本文的
的隐式合作队友建模方法，最后对该方法进行了实验并加以分析。  
第5 章：基于深度蒙特卡洛的博弈决策算法。本章首先针对解决稀疏奖励问
题提出了解决方案，然后介绍了监督训练的流程和结果，最后给出了不同算法对
比的实验结果。 
第6 章：总结与展望。本章对本文所研究的内容进行了总结，并说明了其中
存在的问题和不足之处。 
 
 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-9- 
 
第2章 相关基础理论知识 
2.1 引言 
本章将根据本课题所研究的内容，首先介绍博弈论的相关知识并给出博弈问
题的一般分类方法，然后介绍了多智能体之间的关系类型和非完美信息博弈；其
次对深度学习进行阐述；最后，对多智能体博弈相关算法进行介绍。 
2.2 博弈论相关基础理论 
博弈论是一种最优决策理论，同时又被称为对策论。通过对博弈论进行研究，
博弈方在合作、竞争以及矛盾等情形下，经过对博弈参与方信息的充分了解，能
够选出为己方赢得最大利益的策略。博弈论的基本组成元素共有四个，分别是参
与人、收益、策略和信息。参与人又名“博弈方”，是指在游戏中具有自主决定
力，能承受一定的结果的决策主体。收益是指参与者在博弈结束时获得的回报或
结果，每个参与者所采取行动的最终目的都是为了使己方获得最大收益。策略是
一组参与方用来处理游戏问题可以选择的行为或战略，博弈各方可用的策略通常
是不同的。信息是指在游戏中，参与者所掌握的有关决策的知识，尤其是对于其
它参与方的特点和行动。 
博弈发生时，各个参与方都努力争取其自身最大利益。即任何博弈方都必须
对他人行动有所反应，以便自身获利。博弈的结果不是单纯的由自身决定，它还
受到其他参与者的影响。因为在自身采取行动时，其他人也同样在采取行动，所
以最终的结果由大家共同决定。 
 机器博弈类型 
博弈有许多不同的形式，各种类型的博弈，其结果也不尽相同。按照不同的
方法，博弈可以分成下列类型：  
按博弈的结果来说，博弈可以划分成负和博弈、零和博弈以及正和博弈。负
和博弈是指双方存在对立的、无法解决的冲突 或者双方不团结，不能相互让步
的博弈。此时双方的策略只能使自己造成损失，而各自的策略所带来的利益远不
如这种损失，因此形成两败倶伤的局面。零和博弈是指参与游戏的所有人都被严
格限定在一个特定的环境中，一个人的利益就是另一个人的损失，双方的利益和
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-10- 
 
损失加在一起总是为零，不存在任何合作的可能性。正和博弈就是游戏中的每一
方的收益都在增长，或者至少有一个博弈方的利润增加，而不会损害其他参与方
的利益，但总体利润增加。 
根据参与人数的不同，可将其分为二人博弈和多人博弈
[24]。二人博弈是一种
只有自己和一名对手参与的游戏，二人博弈只需要留心对手的行为即可。多人博
弈是指两人或两人以上参加的博弈。多人博弈中，既要观察除自己外的参与者们
的动作行为，也要考虑多个参与者之间由于不同的策略组合所导致的极复杂的相
互作用；  
按照玩家的行为次序，博弈可以划分成静态和动态两种。静态博弈通常是一
个行为和一个决定在同一时间发生的游戏，两个人按照一定的次序操作，但是没
有一个人能够了解另一个人的决策。在博弈中，人们可以通过对游戏中的行为进
行观测，从而做出相应的选择，即所谓的动态博弈。 
依照博弈参与人拥有的信息程度，博弈可以分为完美的以及非完美的。完美
信息博弈是指参与者拥有一致的信息，信息在博弈时是透明的，没有任何隐藏，
全部且准确的信息都能被各个参与者获悉。非完美信息博弈指的是每个参与者没
有办法得到所有的信息，对于对手的了解不够精确，也就是说对手的某些信息是
被隐藏的，因此信息是不对称的。和完美信息博弈相比较，非完美信息博弈中存
在着状态空间非常庞大和信息不确定的问题，因此对它的深入研究一直是一个大
的挑战，但因其更接近现实生活，所以更具研究价值与意义。 
这些不同的博弈类型可以以各种方式组合，包含了信息完美静态博弈、信息
完美动态博弈、非完美信息静态博弈和非完美信息动态博弈。 在这四种组合中，
后两种组合更为常见，覆盖范围也更广。非完美信息静态博弈是指至少有一个玩
家不完全了解对手的特征，即不知道玩家的真实类型，但知道每种类型出现的概
率。 非完美信息动态博弈属于动态博弈，其中的行为是连续的，在不完美信息
条件下，参与者知道其他哪些参与者有哪种类型以及每种类型出现的概率，但并
不确切知道其他参与者属于哪种类型。 然而，通过观察先前行为者的行为，后
来的行为者可以获得有关先前行为者的信息，并确认或修改自己所采取的行动。
日常中的诸多博弈问题也都归于这两种非完美信息集下的博弈组合，是当今人们
研究的热门领域。本文也将针对非完美信息集的博弈模型展开研究。 
 
 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-11- 
 
 
图2.1 博弈的几种分类方法 
Fig.2.1 Several ways of classifying games 
 多智能体的关系类型 
根据实际需要，多智能体问题按照完成的任务不同可以分为三种类型：完全
对抗、完全合作和混合关系型，其中本文所研究的多智能体的合作式对抗就属于
混合关系式类型的范畴，下面将对它们逐个进行介绍。 
（1）智能体之间是完全对抗关系 
极大极小的思想经常被用于完全对抗任务中的两个智能体情境下，即通过对
其他智能体的意图、将采取何种行动使最大程度上降低自己的利润进行提取预判，
且尽可能使自己可以有一个最大化最小收益的策略。最为典型的研究方法是
minimax-Q 算法[25]。在计算阶段，该算法利用极大极小的原则优化博弈策略和价
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-12- 
 
值。最佳价值函数被定义为当另一个智能体采取的行动导致自己最差的报酬时，
智能体的最大预期收益，这个收益可以用公式（2.1）来表示： 
 
*
*
( )
max min
( , ,
) ( , )
a
a
V
s
Q s a a
s a


−
−
=

 
(2.1) 
在学习的进程中，根据Q-learning 的强化学习，minimax-Q 利用最大最小概
念中所确定的价值函数，利用迭代更新价值和线性规划选取行为，来求解相应的
纳什均衡问题。在考虑了竞争对手的策略的情况下，公式2.1 给出了目前智能体
采用的战略，该战略是贪心策略，从而使得智能体更易于实现纳什均衡。Minimax-
Q 方法作为一种非常经典的竞争性博弈的方法，衍生出了许多其他方法，包括
correlated Q-learning、Friend-or-Foe Q-learning（FFQ）和Nash Q-learning 方法等。 
（2）智能体之间是完全合作关系 
“合作”代表了多种智能需要共同完成一项任务，即目标任务的实现与各个
智能体的行动结合所产生的共同行动有关。其产生的动作空间被称作联合动作空
间。因此Q 学习方程为： 
1
1
1
1
Q
(
,
)
(
,
)
[
maxQ
(
, ')
Q (
,
)]
k
k
k
k
k
k
k
k
k
k
k
k
u
x u
Q x u
r
x
u
x u


+
+
+
+
=
+
+
−
    (2.2) 
在公式（2.2）中，kx 和
ku 分别代表k 状态下的环境状态和动作状态，智能体
在k+1 状态下的回报收益用
1
kr + 表示，最佳动作策略用'u 表示，Q (
,
)
k
k
k
x u
表示的 
是k 状态环境
kx 下采取的策略
ku 所得到的回报收益，其中为智能体学习率，
为回报衰减系数。 
如果全部智能体都使用贪婪策略以及独立决策方式时，有可能出现合作问题。
进一步，所有智能体全部使用一样的算法同步学习最优Q 函数得最佳特征。从
理论上来讲，是可以通过贪婪策略使共同利益最大化的，然而，智能体之间的协
调会被贪婪策略的行为选择机制所打破，造成终端的联合行动有可能并不是最优
行动组合，导致结果不理想。  
基于多智能体合作问题的角度出发，将该问题分为不需要协作机制的问题、
隐式的协作机制的问题、显式的协作机制的问题。这种分类方法以是否需要协调
机制来通过合作获得最佳回报为依据。这里主要介绍与本文研究内容有相关性的
隐式协作机制。 
隐性协作机制是一种潜在的协作机制。当智能体需要相互协商以达成最佳联
合行动时，个体的相互建模可以为智能体决策提供这种机制。联合行动学习（joint 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-13- 
 
action learner，JAL）[26]和频率最大Q 值（frequency maximum Q-value, FMQ）[27]
方法适合于隐性协作机制；在JAL 方法中，一个智能体根据观察到的其他智能
体的过去行为建立其策略模型；在FMQ 方法中，个体价值的定义是基于个体行
为所在的联合行动实现最佳奖励。者两种求解方法都仅能处理规模较小的多智能
体问题。在实际应用中，众多智能体存在相互联系和作用，这使得普通求解算法
计算效率不高，局限性大。在大型多智能体的研究中，研究团体合作的影响，例
如，目前的智能体效应以及在团体中的角色，可以帮助个体进行策略的研究。 
（3）智能体之间是混合关系 
由于前面提到的两种智能体之间的关系包括个体之间的竞争和个体之间的
相互合作，所以个体在学习策略时必须考虑对方的决策行为，以便有更好的应对
动作，当智能体之间存在合作和竞争时，个体的决策也会考虑其他智能体的决策
情况。混合任务虽然对智能体的具体功能没有特别的限制，但是却需要对其中存
在的对抗和合作中的平衡问题进行解决。Asymmetric Q-learning[28]、CE-Q[29]和
Nash Q-learning[30]都是典型解决该问题的算法，其中Nash Q-learning 方法最常见，
其目的是为每个状态找到纳什均衡点，并在学习过程中根据纳什均衡策略更新Q
值。就一个智能体i 而言，其Nash Q 值由公式（2.3）定义： 
 
1
'
'
'
'
( )
( )
( )
( )
Nash
i
n
i
t
t
Q s
s
s
Q s


=

 
(2.3) 
迭代更新Q 值时使用Nash Q 值来更新，如公式（2.4）所示： 
 
(
)
(
)
(
)
( )
1
1
1
,
,
,
1
,
,
,
i
n
i
n
i
i
t
t
t
t
t
t
Q
s a
a
Q
s a
a
r
NashQ
s




+



=
−

+
+

 
(2.4) 
Nash Q-learning 法可以对智能体获取的有关其它智能体的信息（包括行为、
奖励等）进行有力的假设，但现实中的复杂问题常常不符合这些苛刻的要求，因
此这种方法在某种程度上也是不适用的。 
 非完美信息博弈 
机器博弈是现代博弈进行的主要方式之一，其由原始的博弈论发展而来。在
机器博弈的发展历程中，有众多的科研工作者对完美和非完美信息博弈的研究做
出了巨大的贡献。现如今，围棋AI 接连击败人类顶尖高手，标志着完美信息博
弈的一次重大突破。然而，由于非完美信息博弈具有较大的不确定性和较多的参
加者，使得问题的解决变得更加困难。经典棋牌类游戏如跳棋、围棋、象棋等，
作为一种具有代表性的完美信息博弈对象，已引起众多学者的关注。这种游戏的
一个共同点是，玩家可以拥有全部的游戏状态。此类博弈的共同特征是玩家掌握
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-14- 
 
所有的游戏状态，即玩家能直接掌握所有信息或根据计算推理获得全部的信息。
而在非完美信息博弈中，玩家不能完全得知全部信息，他们只能了解到自身情况、
历史信息以及当前的状态。例如在斗地主进行过程中，各个参与人只知道自己持
有的手牌信息以及公共手牌，却无法真正得知对方的牌力信息。这就使得游戏者
无法像完美信息博弈那样依据清晰的信息来做出更明智的决定。非完美问题的规
模也随之增加，但是这也给了研究人员更多的选择余地，并且增加了对比赛的兴
趣。 
在早期机器博弈的研究中，完美信息博弈是一大重点研究领域，在二人博弈
中取得了很大成绩。伴随着计算水平的显著提高和博弈算法的不断创新，由多人
参与的非完美信息博弈已然替代完美信息博弈成为人们探索的重心。由于多人的
参与，非完美信息博弈中增加了合作或竞争的因素，即参与者可以按照自身策略
以及博弈状态和另外的玩家开展合作或竞争。非完美问题面临的难题主要有下面
几点： 
（1）信息的非完美性。与完美信息博弈中能够看到所有游戏状态不同，在
非完美信息博弈过程里只有公共信息和自身情况被玩家掌握，因此每位玩家了解
到的游戏局面是不一样的，故无法像完美信息博弈那样依照当前局面猜测其他参
与者的最优策略并做出行动，这使博弈多了很多未知性。但也给博弈各方提供了
采取更多策略的空间。 
（2）不确定性。不确定性指的是博弈的进展有可能受到外部随机因素的影
响，而这些因素是参与者无法完全确定的。例如，斗地主或掼蛋等游戏中的发牌
方式就是一个随机过程，它在一定程度上影响着游戏的最终结果。在不确定的游
戏中，博弈方以获胜或止损为目标，即在随机事件不利时将损失降到最低，在随
机事件有利时将利润最大化。 
（3）变化性。参与者掌握的信息在非完美博弈中会随着博弈的进行而更新，
是不断变化的，例如在斗地主博弈中，越靠后的时间段玩家可见的公共牌数量就
越多，了解到的信息也就也多，所以需要不停的对获得的信息进行重新整合和调
整，以便应对当前局势并做出正确的判断。 
 
 
 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-15- 
 
2.3 深度学习概述 
 深度学习基础 
深度学习是一种可以高效地训练有深层神经网络结构的方法，它由人工神经
网络发展而来。神经网络属于分层系统的一种，由大量非线性的神经元构成，网
络的深度通常将输入层排除在外，即不包括输入层层数。在20 世纪，最早期的
MP 模型[31]神经网络被建立，如图2.2 所示。该模型包含𝑛个输入𝑥1，𝑥2，…𝑥𝑛，
𝑛个权值𝑤1，𝑤2，…𝑤𝑛，1 个偏置𝑏以及一个输出𝑦。MP 模型有着进行逻辑运算
的能力，它的处理过程是把各个输入乘以对应的权值，然后进行求和，随后通过
激活函数𝑓对加权和进行处理，最后将处理好的值传递给下一层。MP 模型可以
被认为是对单个神经元进行的一种形式化数学描述，虽然它没有进行学习的能力，
却开启了人们研究人工神经网络的新篇章，在那之后，又出现了很多新的神经网
络模型[32-37]，但发展较为缓慢。 
 
图2.2 MP 神经元的数学模型 
Fig.2.2 Mathematical model of MP neurons 
一直到2006 年时，Hinton 等人提出了包含多个隐藏层的深度信念网络（Deep 
Belief Networks，DBNs）[38]，如图2.3 所示。此后，卷积神经网络（CNN）[39]、
循环神经网络（RNN）[40]和长短期记忆网络（LSTM）[41]等众多的深度学习模型
迅猛发展，深度神经网络开始重新吸引业界的关注。 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-16- 
 
 
图2.3 深度神经网络 
Fig.2.3 Deep Neural Networks 
 循环神经网络 
RNN 其主要用来处理序列数据，因为它具有时间轴的概念，同时拥有记忆
模块，对于不同顺序的相同输入也会产生不同的输出。通常情况下输入层和输出
层以及隐藏层形成了神经网络。因为它具有相互连接的隐藏层神经元所以其与之
前的CNN 神经网络不同，因此可以处理时序问题，恰恰是这种网络结果会使得
其计算能力更强一些。 
 
图2.4 RNN 网络结构 
Fig.2.4 RNN network structure 
在上图RNN 网络结构图中，X 表示输入样本其输入到RNN 网络结构中，
tS 表示样本在t 时刻的记忆，U 是输入权重矩阵，V 是输出权重矩阵，W 是上次
1
tS −作为输入的权重矩阵。由此可看出此刻的输出不仅与此刻的输入有关，还与
之前的记忆有关。 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-17- 
 
 
(
)
t
t
o
g Vs
=
 
(2.5) 
 
(
)
1
t
t
t
s
f Ux
Ws −
=
+
 
(2.6) 
上式中f 和g 都是激活函数，f 可以为tanh、relu、sigmoid 等激活函数，g 可
以是softmax 或其他。最后不断地计算，将前一刻的(2.6)带入(2.5)，一直往前延
伸最终可以得到下式： 
)
(
t
t
o
g Vs
=
 
1)
(
t
t
Vf Ux
Ws −
=
+
 
1
2
(
)
((
))
t
t
t
Vf
Ux
Wf Ux
Ws
−
−
=
+
+
                    (2.7) 
1
2
3
(
((
(
)
))
t
t
t
t
Vf Ux
Wf
Ux
Wf Ux
Ws
−
−
−
=
+
+
+
 
1
2
3
(
(
(
(
))))
t
t
t
t
Vf Ux
Wf Ux
Wf Ux
Wf Ux
−
−
−
=
+
+
+
+
 
由上式可以更为清晰明了的看出，某时刻输出值
to 不仅与当前时刻的输入值
tx 有关，还与之前所有时刻的值
1
2
3
t
t
t
x
x
x
−
−
−
、
、
有关，虽然这较之前的神经网络
来说是个极大的优点，但是实际上往往后面的值与前面值之间的联系已经微乎其
微，恰恰是这个所谓“优点”会造成对前后联系不大的值产生错误的判断。 
（1）RNN 缺点不足 
在RNN 中网络参数靠着反向传播来进行更新，一般情况下，采用交叉嫡损
失函数或者平方误差损失函数作为模型中的损失函数。在这个网络中因每一个当
前步的输出都要依靠当前以及前若干步的状态，这种改良的BP 算法使输出端的
误差反向传递，采用梯度下降法来更新， 即后一层对前一层求梯度，这将导致
如果当网络层数较多时，会发生导数式子会变得很长。而在RNN 中tanh 函数作
为激活函数比较常见，应用它时会导致出现很多小于1 项，因此会导致链式求导
公式的结果趋近于0，即所谓的梯度消失现象另外如果W 值过大且序列长度长
期依赖将会导致梯度爆炸，这是RNN 的一大弊端。所以1997 年学者提出了LSTM
算法。 
我们已经了解了前面提到的RNN 神经网络存在着诸如梯度消失和梯度爆发
等致命问题。这使得我们对RNN 的使用范围小之又小。但是RNN 的延伸网络
LSTM 却因为避免了梯度消失与梯度爆炸的问题而得到广泛的应用。 
（2）LSTM 网络结构 
LSTM 网络与常规RNN 网络最大的区别就是它的隐藏层，由“门控单元”构
成 LSTM 的隐藏层，从而能够对神经元进行调控。因此，LSTM 中的神经元可
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-18- 
 
以起到保存和舍弃的作用。门控单元主要包括一个乘法器和一个sigmoid 激活函
数层。在此基础上，激活函数层的输出为0 至1，用于控制消息的传递。1 代表
所有的信息都能被传送，0 代表任何信息无法传送。 
LSTM 的神经元一般包括门控单元和操作模块。这些操作单位经过反复的学
习，得到相应的权值。这些运算单元通过反复的学习，得到相应的权值。LSTM
神经元的构造如下。 
 
图2.5 LSTM 记忆单元内部结构 
Fig.2.5 Internal structure of LSTM memory cell 
由图2.5 可知，通常，一个LSTM 单元包括三部分。这三个模块是由遗忘计
算模块、输入计算模块和输出计算模块组成。者均执行着各自的功能。 
遗忘计算模块如图2.6 所示，该模块的主要作用是判定和计算LSTM 的神经
元缺失，该神经元会接收来自前一LSTM 神经元的隐含层状态
t 1
h −和当前时刻的
输入
tx ，然后将二者进行运算。由于我们在门控单元采用了sigmoid 函数，所以
我们会得到0 或1 的输出，同时会把它传给上一刻的LSTM 神经元状态
t-1
C
，综
上所述，此遗忘模块输出如下： 
 


(
)
1,
t
f
t
t
f
f
W
h
x
b

−
=
+
 
(2.8) 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-19- 
 
 
图2.6 LSTM 遗忘门结构 
Fig.2.6 LSTM forgetting gate structure 
 
图2.7 LSTM 输入门结构 
Fig.2.7 LSTM input gate structure 
输入计算模块如图2.7，该模块的主要功能为更新LSTM 的神经元状态，对
应的计算公式为： 
 


(
)
1,
t
i
t
t
i
i
W h
x
b

−
=
+
 
(2.9) 
                   


(
)
1
tanh
,
t
c
t
t
c
C
W h
x
b
−
=
+
                   (2.10) 
LSTM 网络最重要的便是LSTM 的神经元当前状态，结构如2.8 所示： 
 
图2.8 LSTM 细胞状态更新 
Fig.2.8 LSTM cell state update 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-20- 
 
当输入计算模块计算完成之后，神经元的状态就可以进行更新由原来的状态
t-1
C
更新到
t
C 。同时将遗忘计算模块中的
tf 乘上上一个时刻的
t-1
C
，通过此次计
算就可以过滤掉与网络无关的信息，随后添加t
t
i
C

 ，此时新值具体表达式为： 
 
1
t
t
t
t
t
C
f
C
i
C
−
=
+ 

 
(2.11)                
最后一个模块输出计算模块的具体结构如图2.9 所示，该模块主要实现该神
经网络的最后的输出功能。当更新完LSTM 的当前时刻状态之后，随后计算输
出，并传递给下一个神经元。公式如下： 
 


(
)
1,
t
o
t
t
o
o
W h
x
b

−
=
+
 
(2.12) 
                        
(
)
tanh
t
t
t
h
o
C
=

        
(2.13) 
 
图2.10 LSTM 输出门结构 
Fig.2.10 LSTM output gate structure 
总的来说，我们在三个计算模块中都采用了“门控单元”。在此基础上，利
用 Sigmoid 功能判定是否要存储，并利用 tanh 函数进行操作转换。最后，经过
两次计算，再加上三个门的作用。这种网络的结构可以让自身拥有储存过往的知
识的功能，具备了记忆能力。 
（3）LSTM 运行原理 
在理解LSTM 理论的基础上，我们将详细地阐述LSTM 网络的工作过程。
LSTM 的工作流程可分成正向传播和反向传播。在正向传播中，包含了从输入到
隐藏的传递再到输出层和神经元的传递。整个过程可以使得该网络对过去的信息
有着记忆功能。 
我们可以看到，在LSTM 中任何时间都存在着各种不同的输入，而且许多的
信息会被隐藏层有选择地保存下来。LSTM 是从输入级开始，经过隐藏级，再通
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-21- 
 
过输出级。从独特的构造可以看到，最初的时候，隐藏在隐含层的内部的讯息还
能保持到最终。即使是在即将完成的网路周期中，隐藏层依然可以有选择地筛选
信息。 
为了方便计算，做如下假设：
ij
W 表示神经元i 与神经元j 连接时的权重，
t
jb
代表神经元j 在t 时刻的激活函数，
t
cs 代表神经元c 在t 时刻的状态。, ,
,
I W c

分
别代表遗忘计算模块、输入计算模块、输出计算模块和一个神经元分别用。f 表
示激活函数，输入层激活函数与输出层的激活函数分别用g 与h 来进行表示。假
设有下述时间序列： 
                     
{ (1), (2),
, (
)}
X
x
x
x N
=

                  (2.14) 
这个序列长度为N，LSTM 存在一个时间t=1 到时间t=N 的前向传播与一个时间
从t=N 到t=1 的反向传播。其中，易得反向传播导数公式： 
t
j
t
j
L
a



=
                         (2.15) 
这里重点计算LSTM 的反向传播。某时刻t，神经元输出为： 
1
1
1
K
G
t
t
t
c
ck
k
cg
g
k
g
W
W


+
=
=
= 
+ 
                (2.16) 
某时刻t，输出计算模块的表达式为： 
(
)
( )
1
c
t
t
t
t
w
w
c
c
c
f
a
h s


=

=
                (2.17) 
某时刻t 神经网络的状态方程为： 
         
( )
1
1
1
1
t
t
t
t
t
t
t
t
t
s
w
c
c
s
ct
I
c
cW
W
b h s
b
W
W
W









+
+
+
+ +

=
+
+
+
         (2.18) 
其神经元权值导数为： 
     
( )
t
t
t
t
c
I
c
s
b g a


=

                     (2.19)  
遗忘计算模块
tf 的导数为： 
(
)
1
1
c
t
t
t
t
c
s
c
f
a
s




−
=

=
                    (2.20) 
输入计算模块i 的权值导数为： 
(
)
(
)
1
c
t
t
t
t
I
I
c
s
c
f
a
g a


=

=
                 (2.21)  
 
 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-22- 
 
2.4 多智能体博弈相关算法 
智能体间的博弈可以分为完美信息博弈和非完美信息博弈两种。实际应用中
的多数博弈问题都属于非完美信息的范畴。因此本文主要针对更具挑战性的非完
美信息多智能体博弈进行研究。普遍的强化学习方法在解决博弈问题时是对确定
的贪婪策略进行学习，这类求解方法在单智能体问题中可以获得很好的表现，但
在非完美信息下的多智能体博弈中却并不适用。这是因为在多智能体系统中存在
不确定性，智能体在相同的可观测的状态下采取相同的动作可能得到的收益不一
样，因此形成的标签也不唯一，这将造成这些算法在非完美信息多智能体博弈中
不能收敛，因为在非完美信息多智能体博弈问题求解中需要随机策略到达最优。
目前对非完美多智能体博弈的解决方案存在一些问题，如对学习抽象模式能力的
缺失，无法利用抽象模式对新的策略进行学习，而是需要依赖人类经验或其他启
发式的策略将问题复杂度降到可以解决的空间内，但获取人类经验并不是一件容
易的事情，需要耗费很多人力和物力，因此只对复杂程度低的博弈环境或者特定
场景才适用。因此，研究能够解决大规模搜索空间及高复杂度的多智能体博弈解
决方法很有必要性。 
 强化学习算法 
强化学习一词来自行为心理学，表明生物体会更频繁地实施对其有利的策略，
以避免伤害。在人工智能中，强化学习是一种特别的机器学习问题。增强式的学
习体系使决策者能够对周围的情况进行观测和行为。完成任务，可以得到相应的
报酬。强化学习的研究是学习怎样利用与周围的环境相互作用来获得最高的报酬。
举例来说，一台在一个迷宫里闲逛的机器人，会先看一下四周，然后再根据观测
到的结果发现来确定自己该怎么走。一旦出错，就会使机械人失去大量的时间和
能量；假如正确的话，那么这个机器人将会从这个迷阵中逃脱。在本例中，机器
人的动作是一种以观测为基础的动作，所耗费的精力和精力，以及从一个迷阵中
逃出来都是对其的一种奖赏（所耗费的精力和时间被视为负面回报）。强化学习
最大的特征就是没有正确的答案，而机器人则是利用反馈信号来完成学习任务。
当一个机器人穿过一个迷宫时，自身并不知道每一个动作是对是错，而是取决于
他花了多少时间、多少精力，还有他能否走出这个迷宫。在此基础上，本文提出
了一种基于激励的方法，即在没有正确的回答的情况下，加强式学习的最大特征
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-23- 
 
就是没有正确的回答。对于一个在穿过一个迷宫的机器人来说，他并不能确定所
有的行为都是对的，只有通过花费的时间、精力和能否离开这个复杂的区域来判
定行为的合理性。如图2.4 展示了强化学习系统示意图。 
 
图2.11 强化学习系统示意图 
Fig.2.11 Reinforcement learning system diagram 
从图2.11 我们可以看出一个强化学习系统包含以下几个重要对象： 
（1）智能体（Agent）。智能体可以被类比为人，是在强化学习环境中去探
索和学习并做出决策的主体。通过对环境进行观测，智能体做出相应动作且接收
来自环境的反馈。  
（2）环境（Environment）。所谓环境，就是智能体所处的环境，也就是客
观环境，智能体要对其进行试探，它就是智能体的应用场景，接收智能体的行为，
然后根据智能体的行为，更新环境信息，然后反馈给智能体。 
（3）观测（Observation）。观测是指智能体能够观测或者感知到的环境信
息。观测一般是有限的，智能体只能在非常有限的视野里观测周围的情况，其能
够感知的范围和内容都是有限的。 
（4）动作（Action）。动作是指由智能体发出的行为或动作，以及智能体和
环境之间发生的动作交互。也可以说是智能体根据当前观测做出的下一步决策，
该决策作用于环境并且用于改变环境。  
（5）奖励值（Reward）。奖励值也称回报值，是指智能体在做了一个动作
后得到的用于描述其行为好坏的值。如果一个行为很好，智能体或许能获得一个
超大的奖励值；如果一个行为不好，那么有很大几率得到很微小的奖励值。智能
体依赖奖励值选取最佳策略。  
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-24- 
 
强化学习的算法主要有动态规划[42]、蒙特卡罗法[43]、时间差分[44]以及Q 学
习算法[45]等。不同的算法有其各自的优点和局限性。动态规划算法在科学理论和
数学研究领域有着很强的理论支撑，然而它的应用条件十分严格；Q 学习评价策
略比较容易，使用数据量相对较少，但存在过高估计的问题。大多数强化学习问
题都能用马尔科夫决策过程（MDP）[46]来描述，这是强化学习的理论基石，通常
可以被描述为(𝑆,𝐴,𝑃𝑠𝑠′
𝑎,𝑅𝑠𝑠′
𝑎)，其中𝑆= {𝑠1, 𝑠2, … , 𝑠𝑛} 是智能可以达到的所有状态
的集合，𝐴= {𝑎1, 𝑎2, … , 𝑎𝑛}是行动空间集，代表智能体可以采取的所有行动，𝑃𝑠𝑠′
𝑎
是智能体在当前状态𝑠中采取行动𝑎后过渡到新状态𝑠′的状态转换概率集合，𝑅𝑠𝑠′
𝑎
是以概率𝑃𝑠𝑠′
𝑎从状态𝑠过渡到下一个状态𝑠′'后获得的奖励瞬时值，它代表奖励值的
集合。强化学习的一个主要目的是根据对智能体行为策略的优化，然后找到目前
状态下的最优动作映射，即状态集S 和行动集A 之间的映射，也即状态-行动价
值函数[47]。 
𝑣𝜋(𝑠)是策略π下的状态估值函数，表示状态𝑠下的期望回报，其表达式如公
式（2.22）所示： 
( )
v
s

 
2
1
2
3
]
[ t
r
t
t
E r
r
r
s
s



+
+
+
=
+
+
+
=
∣
 
 
       
1
2
0
[
]
k
t
t k
t
k
E
r
r
s
s




+
+ +
=
=
+
=

∣
 
(2.22)            
'
( , )
[
( )]
a
a
ss
ss
a
s
s a
P
R
v
s






=
+


 
表达式中的E代表智能体采取策略π时的期望回报，称作折扣系数或者折
扣因子，它的取值范围是

0,1 ，表示对长期回报的重视程度，显然，如果
0
=
，
则完全忽略在这之后状态的价值，而孤立地评估当前直接得到的回报，即只关心
当前时刻的奖励；如果
1
= ，则表示之后的所有时间步的奖励和当前时刻奖励的
重要性相同。越重视长期回报，的值就越靠近1；越不重视长期回报，的值
就越靠近0。
( , )
s a

表示在当前某个策略下状态𝑠采取动作𝑎的概率。 
与状态值函数类似，在策略的驱使下，可以通过以状态𝑠做动作𝑎的估值来
定义状态-动作值函数
( , )
q
s a

，其公式可以表示为如下公式（2.23）： 
( , )
q
s a

 
2
1
2
3
]
,
[ t
r
t
E r
r
r
s a



+
+
+
=
+
+
+ ∣
 
 
 
'
'
'
( )
a
a
s
ss
s
S
R
P v
s



=
+ 
 
(2.23) 
'
'
'
'
'
'
'
'
( )
(
|
)
( ,
)
a
a
s
ss
s
S
a
A
R
P v
s
a s q
s a






=
+ 

 
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-25- 
 
部分可观测马尔科夫决策过程（Partially Observable Markov Decision Process， 
POMDP）[48]是MDP 模型的扩展形式，在部分可观测环境中，智能体所观测到的
信息只是环境的部分信息，并不是环境的全部信息。比如在扑克博弈中，参与人
只能看到自身手牌和公共牌的信息，并不能看到其他玩家的牌。正是因为智能体
当前的观测不足以表示当前的状态，因此POMDP 不具备马尔科夫性。POMDP 
通常定义为一个7 元组( ,
,
,
,
,
, )
S A P R


，其中,
,
,
S A P R 与MDP 相同，即S 是
一组状态，A 是一组动作，P 是状态之间的一组条件转移概率，R 是奖励函数，
而是智能体对环境的观测，O 是一组条件观察概率，
[0,1]

是折扣因子。在
某一时刻，环境处于某种状态s
S

，智能体在A 中选取动作a
A

，这会导致环
境转换到状态
's 的环境概率为
'
( | , )
P s s a 。与此同时，智能体接收观测o，它
取决于环境的新状态，概率为
'
( | , )
O o s a 。最后，智能体接收到奖励
( , )
r
R s a
=
。
然后重复该过程。目标是让智能体在每个时间步骤选择最大化其预期未来折扣奖
励
0
[
]
t
t
t
E
r


=

的行动。折扣系数决定了对更远距离的奖励有多大的直接奖励。
当
0
=
时，智能体只关心往后一步的即时奖励；当
1
= 时，智能体关心未来奖励
的预期总和。 
 深度强化学习算法 
深度学习（Deep Learning, DL）和强化学习（ Reinforcement Learning，RL）
是近些年来机器学习的重要研究领域，取得了卓越的成果。在语音识别[49,50]、图
像分析[51,52]、自然语言处理[26,27]等众多范畴，深度学习都起到了不可替代的作用。
在游戏博弈[53,54]、仿真模拟[55]和优化与调度[56,57]等领域，强化学习占据着重要地
位。然而伴随着现代社会的迅猛发展，在众多复杂的实际应用任务中需要结合深
度学习与强化学习，从而解决问题。深度强化学习（Deep Reinforcement Learning，
DRL）方法最早由DeepMind 提出，其将深度学习与强化学习融合，使感知能力
与决策能力可以在一起发挥作用。从那时起，深度强化学习在人工智能的许多领
域取得了重大突破。大量的机器博弈研究者也运用此方法求解复杂的博弈问题，
极大程度上提高了求解效率。 
非完美信息博弈问题可以通过上面提到的部分可观测马尔可夫决策过程模
型进行建模，然后采用强化学习的方法进行学习和训练的环节。然而，由于非完
美信息博弈问题一般都有着高维度的状态空间或动作空间，因此状态-动作值函
数
( , )
Q
s a

所对应的矩阵就会非常巨大，这就造成了电脑没有足够的空间来容纳
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-26- 
 
这样一个庞大的矩阵，再加上没有充足的训练数据，也没有经过多次的试验，所
以当一个智能体出现新的状况时，它就不可能做出最好的反应。有些常规的算法
只能用于较小规模的博弈问题，而不能用于较大的博弈问题。 
基于以上问题，需要对传统的方法进行改进和创新。而深度强化学习是利用
深层神经网络与强化学习的方法来描述高维度的情况。其主要思路是：通过神经
网络来拟合强化学习中的价值和战略空间，把强化学习的功能的更新转换为一个
网络的参数寻优过程。 
DeepMind 在2013 率先推出了DQN（Deep Q Learning Network，DQN）[58]。
DQN 作为一种经典的增强学习方法，其应用于深入强化的研究中，可以很好地
克服传统的高维状态不能处理的难题，为深入强化学习的发展打下了良好的理论
基础。深度Q 学习在后续又经过了一些改进，改进后的DQN 在许多方面都有了
比较大的提升。改进版的DQN 有两个结构相同的网络并且都是将Q 值作为两个
网络的输出。该做法的目的是稳定训练过程中深度Q 学习的目标，并使其较容
易收敛。 
DQN 算法用深度神经网络取代了强化学习中状态动作的价值函数，即用函
数近似值估计价值函数，并且将网络的权重w 进行随机初始化，如公式（2.24）
所示： 
 
( , , )
( , )
Q s a w
Q s a


 
(2.24) 
把将要训练的数据输入到深度Q 学习网络中，然后通过卷积层和下采样层的
操作以及激活函数的处理，提取到更加抽象的特征，从而获得每个动作对应的Q
值，进而将输出对应的Q 值最大的动作
max
( , ; )
Q
a
Q s a w
=
选择出来。由于在训练
的初始阶段网络连接的权重是随机初始化的，因此一开始计算出的Q 值最高的
动作
max
( , ; )
Q
a
Q s a w
=
也是完全随机的，智能体展现的是随机的探索，伴随着深
度Q 学习网络的收敛，随机探索的情况也不断减少。 
DQN 算法利用均方差（mean-square error，MSE）来定义损失函数（loss  
function），也称之为目标函数（objective  function）。表示为公式（2.25）所示： 
 
'
2
 arg 
( )
[
max
( ,
, )
( , , )) ]
(
a
T
et
L
E
Q s a w
Q
a w
r
s



=
−
+
w
 
(2.25) 
从公式中可以看出，
'
max
( ,
, )
( , , )
a
r
Q s a w
Q s a w



+
−
便是Q 学习状态-动作值
函数更新中计算出的Q 值与旧矩阵中的Q 值的差值，此差值在Q 学习中以一定
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-27- 
 
的学习率来进行学习，而在深度Q 学习领域是利用梯度下降法更新网络权重。参
数 w 关于损失函数的梯度表示如公式（2.26）所示： 
 
( )
( , , )
[(
max
( ,
, )
( , , ))
]
L w
Q s a w
E r
Q s a w
Q s a w
w
w





=
+
−


 
(2.26) 
该梯度可以通过公式（2.26）来计算，然后可以在该梯度降低的方向上进行
迭代，这可以通过采用随机梯度下降算法（SGD）实现。在梯度降低期间，根据
公式（2.27），对深度强化学习网络的参数进行了更新： 
( )
w
w
w
L w

=
−
                      (2.27) 
 
图2.12 深度强化学习网络 
Fig.2.12 Deep reinforcement learning networks 
这个式子是一个关于学习速率的函数，它表示了一个梯度的递减。深度强化
学习网络的框架如图2.12 所示。 
深度强化学习是一种克服了常规的强化学习算法不能有效地处理高维状态
空间的问题，将深度学习与增强式学习相融合，以达到从感知（Perception）到行
为（Action）的端到端的学习，从而克服了常规的基于特征抽取的繁琐步骤。在
学习过程中，智能体能够在个性特征中找到最根本的关联。深度强化学习是解决
大规模不完善的信息对策问题的有效途径，并为将算法扩展到现实世界环境提供
了可能。 
2.5 本章小结 
本章主要介绍了与本文研究有关的相关理论知识，首先介绍了博弈论的基础
知识，主要包括三部分：机器博弈类型、多智能体的关系类型、非完美信息博弈，
揭示出本文研究内容的意义，接下来，对深度学习的相关知识进行了介绍，并从
东北大学硕士学位论文                                          第2 章 相关基础理论知识 
-28- 
 
深度学习基础与循环神经网络两个方面进行了详细的阐述，最后介绍了多智能体
博弈相关算法，其中包括强化学习基础知识和深度强化学习算法。 
 
 
 
 
 
 
 
 
 
 
 
 
 
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-29- 
 
第3章 扑克博弈分析与仿真平台搭建 
3.1 引言 
本文将斗地主作为非完美信息多智能体博弈研究的实际应用场景。斗地主作
为非完美信息博弈的一个典型代表，近年来在国内很受欢迎，拥有大量玩家，是
广大扑克迷们的休闲娱乐活动。斗地主的牌局不确定性很大，参与者只能掌握自
己手里的牌，无法得知别人的牌。在实际操作时，参与者可以通过持续积累打牌
经验、游戏策略以及记牌能力来获胜。如：参与者可以通过观察其余参与人的出
牌掌握其出牌规律或者对整体牌局进行预判，从而选择自身的最优决策来赢得博
弈。由于斗地主的突出特点，因此非常适用于非完美信息博弈的研究与实验验证。
经过搜集资料以及分析对比，本文决定在RLcard 平台上对算法和实验进行验证，
为了体现实验的科学性以及对本文提出的算法有整体了解和把握，因此对该平台
做简要介绍。 
3.2 扑克博弈问题描述 
非完美信息多智能体扑克博弈正在逐渐发展，但其面临着诸多挑战： 
（1）在多智能体博弈中，存在着合作和对抗的关系。每个智能体必须学会
相互合作或对抗。比如，在“斗地主”游戏中，两个农夫必须联手对付地主，才能
赢得比赛。 
（2） 扑克博弈有着很大的状态空间。比如UNO 游戏中的状态空间为10163。
每个人的牌都是被别人看不着的，每个人不但要思考自己的卡牌，还要根据对方
的行动来判断对方下一步的行动。 
（3）扑克博弈的动作空间具有很大规模。例如，随着纸牌组合的爆炸式增
长，斗地主可能的动作数量可以达到104。 
（4）在牌类博弈中存在着稀疏奖励的情况。比如，在麻将中难以取胜，只
有在500 轮的随机游戏结束后，方能决定胜负。 
在解决以上扑克博弈的过程中，强化学习（RL）是一种具有广阔应用前景的
方法。强化学习智能体通过与周围环境相互作用，在不同的情况下，以尝试错误
的方式作出决定。由于神经网络被作为一个函数逼近工具，深度强化学习已经在
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-30- 
 
许多方面取得了突破性的进展：Atari 游戏[59]、连续控制[60]和神经结构搜索[61]等。 
本文以斗地主扑克博弈作为研究对象，其具有典型性的多人非完美信息博弈
特征，斗地主是由三位玩家所构成的，其奖励系数和行为空间巨大，所以对其进
行训练需要大量的数据和更为高效的训练方式。此外，由于斗地主中有一位玩家
是地主，另外两位玩家是农民，玩家的这种身份差异和训练算法的不同导致对智
能体的训练需要多种不同的方式。本文简要介绍了几种常见的训练方式： 
（1）基于相互博弈的训练。三名参与者都可以被设定成被训练的对象，在
游戏初期，每个智能体的游戏意识都很低，他们的游戏策略也是随机的。如果三
个智能体在一起，互相学习，互相提升，那么博弈能力就会越来越大。但这种方
法也有一个弊端，那就是倘若没有人为的干扰，它可能会“跑偏”，这三种方法可
能会偏离正常的训练。 
（2）自博弈训练方式。AlphaZero 采用的是自博弈训练的方式。 
与前一种算法相似，自博弈同样基于大量的智能体进行交互博弈收集信息，但这
种自博弈通过对收集到的信息进行一定的筛选和过滤，只留下对训练有帮助的信
息，以便更有效地进行智能体的培训。 
（3）引导式训练方法。采用预先设定的模型或人工介入与待训练的策略进
行博弈，但这一策略的获取难度较高，且难以获得海量的数据。 
（4）让人类玩家与智能体对弈。人类则是在游戏过程中不断地改变自己的
游戏战略，让智能体学会与人的游戏能力相匹配。然而，由于斗地主具有大量的
状态，且游戏的过程非常复杂，所以它的可能性不大。 
基于以上训练方式中存在的不足，本文提出了一种新的训练斗地主智能体的
方法，将随机智能体与深度强化学习的智能体进行混合培训。这种训练方式可以
将农民作为被训练对象，而地主是一个任意的智能体。训练初始阶段三位玩家的
获胜概率几乎是一样的，随着农民智能体的持续训练，智能体的博弈能力会变强，
农民获胜的概率将有所增加。 
3.3 扑克博弈仿真平台 
大部分已有的强化学习资源都是面向单一的智能系统，比如OpenAI Gym。
而有些平台虽然支持像星际争霸这样的多智能体系统，却不支持像斗地主这样的
牌类游戏。为了克服上述问题，并为解决人工智能系统的研发与试验带来的困难， 
Daochen Zha 和Kwei-Herng Lai 等人在2019 年推出了一个以纸牌博弈为基础的
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-31- 
 
RLCard 游戏环境。RLCard 平台对游戏进行模拟，并将状态信息反馈给智能体，
然后在玩家做出行动之后，RLCard 便进行仿真，直至游戏最后一刻。通过这种
方法，可以获得许多智能体的运动轨迹，并以此来对智能体进行训练，使得它们
具有更强的博弈功能。 
 仿真平台选取 
本文选取RLcard 作为游戏环境。RLCard 平台对以下几个方面进行了抽象和
界定：选手、游戏、对战、发牌者、裁判员。基于类的概念，可以让玩家在一定
程度上达到一定的游戏体验，而同一类的设计方式也能让用户更好地了解和掌握
游戏的逻辑。值得注意的是，RLCard 是专为牌类游戏所设计的一个平台，不单
单是多种牌类游戏首次在强化学习库中的实现，并且由于提供了简单直观的接口
使得用户对强化学习研究更省时省力。其结构框架如图3.1 所示。 
 
图3.1 RLcard 框架 
Fig.3.1 RLcard framework 
RLCard 致力于扑克博弈的研究，且开放源码，它将国内和国外的一些热门
卡牌博弈结合起来，支持的牌类博弈包括：Blackjack，Texas Hold'em，UNO，
Doudizhu 以及Majiang 等。RLCard 工具包遵循以下设计原则：  
（1）可再现性。可以再现有关环境的结果。RLCard 支持多种类型的扑克牌
游戏，在不同的游戏环境下，使用相同的随机数据可以得到相同的效果。 
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-32- 
 
（2）易使用性。在每次比赛之后，玩家都会在一个简洁的界面中，获得大
量的经验。可以方便快捷的对状态表示和动作编码以及奖励设计、游戏规则等进
行配置。 
（3）可扩充性。根据以上的设计原理，可以轻松地将新的游戏环境加入到
工具箱中。在工具箱中的依赖关系应该尽量减少，尽量减少，这样以后就可以方
便地进行代码的维护。 
RLCard 可以为强化学习和非完美信息多智能体博弈提供一个良好的平台，
从而在多智能体、高维状态动作空间、稀疏奖励等多个方面有重要的突破和进展。 
 博弈环境介绍 
RLCard 包含多种游戏，其中有 UNO，斗地主，德州牌等，其色彩斑斓的游
戏深受大众喜爱。在RLcard 中，可以看到在表格3.1 中显示的关于游戏的概要。 
表3.1 RLcard 中的游戏摘要 
Table 3.1 Summary of the game in RLcard 
环境 
信息集数量 
信息集平均大小 
动作空间大小 
21 点 
103 
101 
100 
德州扑克 
102 
102 
100 
有限制德州扑克 
1014 
103 
100 
斗地主 
1053~1083 
1023 
104 
麻将 
无限制德州扑克 
UNO 
10121 
10162 
10163 
1048 
103 
1010 
102 
104 
101 
表3.1 概括了RLCard 所含的扑克牌类别，并估算了各个扑克牌的复杂性，
其中包括信息集数、单个信息集中的平均状态数、操作空间的尺寸（不具有抽象
性）。需要说明的是，在某些比赛中，仅有一个范围内的复杂性估算。比如，在
斗地主中，可以使用很多种不同组合的牌，这就给状态空间尺寸的估计带来了一
些困难。数据集就是从玩家视角观测到的一种状况，它可以用来度量游戏的规模，
将每个数据集合内的游戏状况数目作为数据集合的平均值来确定。在这个资料集
合里，任何一种可能的打法都与一种牌局相匹配。由于大量的活动区域使得游戏
的困难程度大大增加，所以RLCard 也给用户更多的活动范围。下面对RLcard 平
台中的高级设计进行介绍： 
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-33- 
 
第一个是针对环境的高级设计。所有的牌类游戏都有一个Env 界面，一个完
全的游戏Env 的例子必须包括详细的对策和决策，获得决策之后，将决策进行计
算，一直到博弈结束，并且生成每个智能体的游戏轨迹。具体来说，为了开发RL
算法，RLcard 运用了如下的方法：  
（1）1set_agent：设置智能体。该功能是将在游戏中采取动作并作出决策的
智能体告知给Env。不同类型的游戏，一般都会有数量不等的智能体，比如，在
斗地主中，有3 个智能个体。输入是此功能的代理类别清单。 
（2）run：在执行一轮游戏的期间，智能体的当前牌局和在此状态下的合理
决定会由 env 依次传送到智能体，而在此期间，会根据是否进行训练来获得目前
的智能体行为，并依次向下一名参与者发送，直到游戏完成为止。比如：
Random_doudizhu 可以通过设置博弈环境，设置智能体，并给出博弈的数据，从
而实现一场博弈。Random_doudizhu 采用三个任意的斗地主智能体的实例如下： 
表3.2 Random_doudizhu 函数 
Table 3.2 Random_doudizhu function 
算法1：Random_doudizhu 函数 
1 
import rlcard 
2 
import RandomAgent 
3 
env = rlcard.make(‘doudizhu’)   #  初始化环境 
4 
agent = RandomAgent()    #  初始化随机智能体 
5 
epioode_num = ?    #  设置游戏回合数 
6 
env.set_agents([agent,agent,agent]) 
7 
while  游戏回合  do: 
8 
#  循环内执行一轮游戏并从环境中生成这三个智能体的游戏数据 
9 
trajectories,payoffs = env.run() 
有些以取样为基础的算法，不必在博弈树上回溯，而更倾向于使用基础界面，
而不必考虑具体的遍历细节。另外，RLCard 还为在特定的动作和内部调用中的
使用提供了先进的处理方式。 
（3）step：一般都是在run 周期中被调用的，并且可以在特定的环境中自动
被调用。当前的游戏者会把输入的动作当作一个决定动作，然后执行，环境会根
据当前的情况继续进行，下一个人的状态和id 会被返回。比如在斗地主中，如
果一开始就使用env.step （1 个3）的方式来执行3，那么地主就会拿出一张3 并
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-34- 
 
改变地主的状态，然后将当前的玩家转移到地主下家。 
（4）step_back：与上面提到的step 不同，step_back 是一个在运行之后向后
移动的外部调用。环境将恢复到当前状态的上一个步骤。因为step_back 要求在
保留以前的保护和恢复状态，所以一般都是在缺省情况下关掉的。要使用，需要
将allow_step_back= True 在环境被建立时设定为True。 
（5）get_payoffs：收获玩家回报。一般在游戏结束时调用该函数获取各个玩
家的最终回报。 
第二个是针对游戏的高级设计。通常来说，纸牌的构造都是相似的。将一些
抽象的概念放在游戏中，并采用同样的方法，可以使使用者和开发者更好地进行
代码扩充和进行调查。大多数的扑克游戏包括下列类别： 
（1）Player：玩家类。玩家是按照策略进行扑克博弈的角色，每款游戏通常
都由多个玩家来玩。  
（2）Game：一局游戏是从一个非终端状态开始到一个终端状态的完整序列。
在游戏结束时，每个玩家都将获得一笔回报。 
（3）Round：一轮是一轮比赛的次序。大部分的扑克牌可以很自然的分成几
轮。比如在斗地主中，每一轮都是三个人轮番上阵。 
（4）Dealer：扑克游戏一般都要进行一次洗牌，然后给牌手一组。庄家会按
照牌局的规定和数目来分配扑克牌。 
（5）Judger：在一轮或者整个游戏的最后，由裁判员来作出裁决。不同的游
戏都有自己的胜利法则，而每一局都会按照自己的规则来决定胜负。 
在一场比赛中，发牌人会先把牌洗干净，然后给每个人发牌，然后在每个回
合或者游戏的末尾，由裁判员决定选手的收益。 
第三个是针对智能体的高级设计。智能系统提供了几种关键的方式，step:根
据所述状态所提供的包括当前玩家的游戏状况的条件，以及所述条件下的正当的
行动决定（legal_actions)返回行为决策，从而方便了所述的算法。Eval_step：在
试验中对试验进行确认时，使用它来恢复决策和每次决策的可能性。RLCard 中
包含了几个典型的算法例子，这些例子被封装成Agent，并且显示了怎样与工具
箱相关联。以RL 为例，DQN 是一个经典的RL 演算法。另外，RLCard 还支持
诸如 NFSP、CFR 和DeepCFR 等其他类型的计算方法。 
 
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-35- 
 
3.4 RLCard 仿真平台环境配置 
这一部分详细阐述了RLCard 中的“斗地主”游戏的详细结构，包括整个体
系结构和操作过程，然后是状态代码的编写，并简要说明了RLCard 平台上用于
深度强化学习的训练方法。 
 博弈系统框架 
 
图3.2 基于RLcard 的斗地主模型框架图 
Figure 3.2 Framework diagram of RLcard-based doudizhu model 
基于上文提到的RLCard 中智能体的通用训练流程，这个部分用图3.2 来表
达了一个斗地主的训练架构。因为是三人的非完美信息游戏，使得游戏体系的设
计更加复杂。具体实施该训练架构的程序主要分为三步执行。第一步，创建游戏
环境，env=rlcard.make('doudizhu')。第二步，设置智能体，创建三个智能体
[agt1,agt2,agt3]，以供等待收集数据和训练。第三步，循环运行游戏：（1）对游戏
进行初始化。把游戏中的全部角色，例如发牌者（Dealer)，玩家（Player）、裁判
（Judger）在类别中创建出来，并将比赛的规则和编码载入。（2）游戏回合。在
Game 类别的初始化之后，玩家需要出一张卡来继续比赛，最后在一个有限的循
环中决定谁是赢家。
（3）获取下一步行为。游戏玩家依据当下的合法可执行动作，
随机进行选择。（4）获取收益（Reward）。在一轮比赛结束后，赢的一队将会获
得比赛的奖励，而失败者将不会有任何奖励。（5）获取输出牌局。获得当前牌局
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-36- 
 
中每个智能体的游戏轨迹。（6）把游戏规则传递给要被训练的智能体，以便它们
接受训练。 
 动作-状态表示  
斗地主是一种流行的扑克牌，有着数亿玩家和爱好者。它由三人来玩，一共
54 张扑克牌，包括红色大王一张、黑色小王一张。叫地主过程结束之后，“地主”
被确定下来，然后它将获得额外三张牌，另外两位玩家被称为“农民”，两个农民
之间是合作关系，他们与地主之间形成对抗关系。在每一轮游戏中，起始玩家一
定要出牌，出牌类型没有限制，单张或对子等都可以，另外两位玩家可以选择管
上或者不出牌（被称为“过”）。如果两名玩家都“过”，那么本轮出牌结束，此时下
一轮第一个出牌的人是当前轮出牌最大的玩家。游戏的最终目的是先把手上的牌
出完。  
在RLCard 中，实施了一个标准的斗地主。在争夺地主的过程中，采用了一
种试探性的方式，将一位参加者作为“地主”。如果三个人都选择了弃权，那就看
他们手中的卡牌来确定，谁的牌面越大，谁就是“地主”。下面简要介绍RLCard
中的动作-状态表示。  
（1）状态表示。在每个决定点或者某个特定时刻，玩家都能看到目前的状
况（或者说，非完美的游戏的信息）。此状态包括玩家在自己的视角所看到的所
有情况。RLCard 为状态编写了一个可读取的Python 字典，每一个状态都是一个
含有两个数值的字典，这两个数值分别代表了一个合法的行为和一个观测的状态。 
（2）状态编码。在斗地主游戏中，该策略的输入量是由六张牌的矩阵组成
的：当前手牌、另外两玩家手牌的合并、最后三步以及已打出的牌的合并。每一
平面大小都是5x15 的尺寸。这个平面上的每一项都可能是1 或0。5 行分别代表
0,1,2,3,4 个对应的卡片。15 列表示从“3”至“RJ”（大王和小王）。比如，当游戏者
手里有“3”时，项（1、0）是1，其余栏0 是0。如果游戏者拥有一对“4”，那么项
（2、1）就是1，而列1 中的其他元素是0。在表格3.3 中显示了“斗地主”的状态
编码。 
 
 
 
 
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-37- 
 
表3.3 斗地主状态编码 
Table 3.3 The states code of doudizhu 
平面 
特征 
0 
当前手牌 
1 
其余两个玩家手牌并集 
2-4 
最近的三个行为 
5 
所有玩家打出牌的并集 
（3）斗地主动作抽象。斗地主行动数目大约是3 × 104，存在着组合爆炸
的问题，其中任意的3 张相同的牌、飞机或炸弹都能带任何单张牌或对子，这
空间对于学习算法来说是相当巨大的。为了达到减小行动空间的目的，RLCard
通过抽象初始行动空间，只对一个主要部分进行了编码，并利用规则来确定所
要携带的牌。如此，可使斗地主动作空间缩小至309。而抽象化的基本想法就
是把重点放在了只关注组合牌型的主要部分上。比如，“99955”被抽象为
“999**”。如果智能主体的行为不符合规定，那么智能主体就会做出“过”的
动作。所以现在的情况很简单，智能体只要掌握了正确的行为，就可以战胜任
何一个随机智能体。 
（4）收益。如果在游戏的最后，地主先出完了所有的牌，那么它就赢了，
并获得奖励值1。两位农民失败，得到0 的奖励值。同样，当两个农民中的一个
把手里的牌都出完了，那么两个农民就会赢，得到1 的奖励值，而地主输了，获
得奖励值0。 
3.5 本章小结  
本章首先对扑克博弈问题进行了简单描述，介绍了非完美信息扑克博弈面临
的挑战和常用训练方式。接着介绍了扑克博弈的仿真平台，包括本文仿真平台的
选取以及博弈环境介绍。最后介绍了RLcard 平台的配置，分别从博弈系统框架、
动作-状态表示进行阐述。 
 
 
 
 
 
东北大学硕士学位论文                              第3 章 扑克博弈分析与仿真平台搭建 
-38- 
 
 
 
 
 
 
 
 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-39- 
 
第4章 基于深度队友建模的隐式合作算法 
4.1 引言 
本章基于深度队友建模的隐士合作方法，主要论述了斗地主游戏的具体实施，
并在RLcard 平台上进行了实验。斗地主又称二打一，是目前国内最流行的一种
扑克牌游戏，于2013 年获得了国家体育总局的正式比赛资格。斗地主具有信息
非完美、多智能体属性和合作特征。此外，因为游戏规则中包含着许多复杂的牌
局和牌型组合，其拥有很大的策略空间。本章以斗地主扑克为实验对象，提出的
TWMIP-Net 和TWMPP-Net 隐式合作队友建模网络，实现相应的队友信息预测
和队友策略预测，获得更加智能的农民合作智能体。因此，在多人非完美信息机
器博弈中能够通过合作来提升农民智能体的合作博弈水平。
 
4.2 非完美信息博弈中的队友合作问题 
 
图4.1 斗地主合作案例 
Fig.4.1 A case example about cooperation in DouDizhu  
获得对队友的抽象描述将使玩家在博弈中获得明显的优势，特别是针对非完
美信息博弈，如在本文研究的斗地主中，通过对农民的建模可以更好地进行合作，
从而打败地主。图4.1 是一个斗地主案例，它显示了一个典型的情况，即如果农
10
♠
J
♥
J
♣
K
♥
A
♣
6
♣
5
♥
5
♥
6
♥
6
♠
7
♣
7
♦
8
♦
8
♣
地
主
农
民           
B
农
民           
A
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-40- 
 
民学会了相互合作，则当前的农民A 应该出一张小的单牌来帮助它的队友B 赢
得比赛。这一特性使得扑克博弈的流行算法，如反事实遗憾最小化(CFR)[62]及其
变体，不适用于如此复杂的三玩家设置。 
与其他纸牌游戏相比，由于纸牌的结合和复杂的规则，斗地主具有较大而复
杂的状态和动作空间。有成千上万种可能的卡片组合，其中这些组合的不同子集
对不同的手牌是合法的。图4.2 展示了一个有391 个合法动作的手牌的例子，包
括单张、对子、三带一、单张连牌（顺子）等等。与德州扑克不同的是，斗地主
的动作不容易抽象 ，这使得搜索计算成本昂贵，常用的强化学习(RL)算法效率
较低。在第二章中提到的深度Q-学习(DQN)的性能将由于大动作空间中的高估
问题而受到很大的影响，而A3C[63]等策略梯度方法未能利用动作特征，限制了
对看不见动作的泛化能力。 
 
图4.2 一组手牌的合法行动组合 
Fig.4.2 A hand and its corresponding legal moves 
因此，本文提出一种新的队友建模方法，这在非完美信息多智能体博弈中具
有重要意义。本文提出了一种基于队友建模的隐式合作方法，即智能体间无显式
交流和通讯，而是通过隐式的队友间信息进行合作，将此方法运用在像斗地主这
样的多智能体非完美信息博弈中取得了不错的效果。 
 
3
♣
4
♥
5
♥
6
♣
7
♥
8
♥
9
♣
9
♦
9
♥
10
♣
10
♦
10
♥
10
♠
J
♣
J
♥
J
♦
J
♠
Q
♣
K
♥
A
♣
4
♥
9
♣
9
♦
3
♣
4
♥
5
♥
6
♣
7
♥
5
♥
6
♣
10
♣
10
♦
10
♥
10
♠
…
…
…
391种合法组合
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-41- 
 
4.3 队友建模算法 
队友建模是对博弈过程中智能体进行训练以应对特定队友的过程。在一些博
弈中，存在队友之间的合作，由于博弈空间过大，或者由于部分状态信息无法获
得，没有一个通用的策略可以应对所有的情况。在桥牌、斗地主、掼蛋等博弈中，
部分博弈信息是无法观察到的，传统的博弈树搜索算法不能用于非完美信息博弈。
针对非完美信息博弈搜索算法需要依赖于未知信息的预测。当传统的博弈树搜索
算法不能很好地解决信息不完美、搜索空间过大等问题时，队友建模成为一种可
行的方法。 
队友模型的最大目标就是通过对其进行观察与研究，从而构建出一种高效
的队友合作模式。队友建模是一种对博弈中的队友或队友的行动的一种抽象表
达，它包括了玩家在游戏中的选择偏向、行为策略、暴露的弱点以及玩家的实
力评估等，只要是博弈过程中队友可被利用的信息都可以出现在队友模型中。
在完美信息博弈中，我们可以借由学习游戏者的策略来降低结点数目，缩小搜
寻范围，进而达到更好地完成游戏的目的；在非完美信息博弈中，采用了基于
队友策略的策略，可以根据对方的行动来进行未完成的信息的预测。当前的合
作队友建模技术没有保留游戏参与者在游戏中的所有数据，因此在游戏的真实
建模中，会根据特定的游戏形态构建出不同的合作队友模式。 
在本研究中，如何建立正确的队友模型是我们研究的重点问题。在博弈中，
可能的队友策略有无数种，我们如果能采用有效的方法来对队友行为进行分析建
模，就能更高效的进行合作。目前常用的建立队友模型的方法有三种，一种是基
于策略偏向的队友建模，一种是基于决策树的队友建模，一种是基于神经网络的
队友建模。 
（1）基于策略偏向的队友建模。由于在非完美信息博弈中，游戏中的状态
是未知的，所以很难给游戏者一个正确的评价，因此利用游戏者的价值模型去研
究对方的价值是不可取的。通过对合作对手策略的学习，可以将其视为一种有效
的合作模型，它通过在不同的游戏环境中，通过学习对方的战略偏好来进行预测。
在这样的情况下，虽然无法正确评估每一场游戏的状态，但根据游戏者的行为序
列，可以将其分类为多个游戏的状态。比如在斗地主游戏中，可以根据队友的行
动顺序和游戏的历史数据，预测出对方的手牌。 
大部分游戏玩家在游戏中都具有自己的选择倾向，比如一些玩家倾向于防御，
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-42- 
 
而另一些玩家倾向于攻击；有些人喜欢用极端的方式来获得更多的利益，有些人
则是相对保守，所以他们更倾向于采取保守的战术，除非是有十足的胜算。大部
分玩家在之后的战略中都会遵循先前的战略[64]。在游戏中，我们可以从特定的行
为中，看出玩家的选择倾向，并由此改变游戏的内在信息，从而得出游戏者的决
策倾向。就拿即时战略类的游戏来说，可以根据玩家制作的装备类型和数量来进
行分类，攻击型的装备越多，就会被归入攻击型，而拥有更多的防御性装备的，
就可以被称为防御者。 
 
图4.3 实时策略类游戏玩家类型的划分 
Fig.4.3 The division of real-time strategy game player type 
根据游戏的战略偏好，模型的建立取决于游戏的理解水平，而在游戏中所选
的特性和针对不同的游戏参与者所采用的对策，都是这方面的专业技能。比如在
斗地主里，面对有攻击性的玩家就会更多选择跟牌，而对于保守性的玩家，就会
采取更多的战术，如虚张声势。 
（2）基于决策树的队友建模。决策树也是一种较好的方法[65]，它从根结点
出发，在各个结点上判定相应的情况，并依据判定的结论继续向下一个结点移动，
直至达到树叶结点。在一个特定的训练样本集合中，可以构造出一颗基于特定的
规则的决策树，并从一个特定的结点出发，将该结点中的数据按特定的特性进行
归类，从而使该结点的信息得到最大程度的提高。 
决策树可以精确地估计出玩家所作的各种选项的概率，比如，当你了解到牌
局中的牌力的概率分配时，可以用决策树来估计该分布的大致范围。不过，与下
一节所述的ANN 相比，决策树在抗噪能力上稍有欠缺。 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-43- 
 
（3）使用神经网络进行建模。对于每个参加斗地主游戏的人而言，在不同
的信息集合下，会有着不同的决策。在构建一个完美的合作队友模式时，必须从
所有的可能因素中筛选出能够影响到对方做出决定的要素。ANN 能很好地在有
噪音的数据中进行模型辨识[66]，并利用ANN 判断最后会对游戏者的决定产生何
种影响，进而对其今后的行动作出判断[67]。 
利用ANN 技术进行游戏参与者的行为分析时，必须先确定游戏中的输入点，
然后选取任何能够对游戏者产生影响的因子进行输入，再利用游戏者的过去的游
戏纪录，进行神经网络的学习，以达到对游戏的预期效果。然后再基于这些预测，
作出最适合自己的决定。 
4.4 隐式合作队友建模网络 
在人类的实践中，获得对队友的抽象描述将使玩家在游戏中获得明显的优势，
特别是非完美的信息游戏。因此，队友建模在游戏人工智能中引起了大量的关注。 
队友建模的目的是确定队友隐藏牌的可能概率分布，这一动机是人类玩家将试图
预测队友的牌，以帮助双方更好的合作。由于斗地主博弈的复杂性，在做决策时，
很多行动可能是合适的。在这种情况下，分析队友的手牌将是非常重要的，因为
掌握这些信息有助于智能体选择最佳的动作。 
通过对队友模型的建立，可以使队友之间配合得更好。在斗地主博弈中，农
民与农民之间是合作关系，作为队友一起对抗地主。本设计的目标是把农民做强，
主要任务是集中在农民的合作问题上，实现更为智能的农民合作智能体。换句话
说，是想得到更强的农民，在斗地主中，对手的模型不需要额外预测，在信息层
面，预测队友，那么对手的也已经可知；在策略上，不需要考虑对手的策略，而
是考虑队友的策略，本质上是在解一个合作关系。因此，对农民之间的合作进行
队友建模至关重要，也是本文的重点研究内容。本文分别为2 个农民设计了
TWMIP-Net 网络和TWMPP-Net 网络来建模队友的信息和策略，通过队友建模
大大提高了对抗对手的能力。 
 博弈信息编码 
由于本文中网络设计的输出是softmax 层，而它的输出实际上是一个概率
分布，从而要求输入的标签也以概率分布的形式出现，进而方便后面的交叉熵
计算，因此本设计对博弈信息的编码采用独热编码（one-hot）的形式。One-Hot 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-44- 
 
编码，又称一位有效编码。其方法是使用N 位状态寄存器来对N 个状态 进行编
码，每个状态都有它独立的寄存器位，并且在任意时候，其中只有一位有效。 
One-Hot 编码是分类变量作为二进制向量的表示：将分类值映射到整值，然
后，每个整数值被表示为二进制向量。 
本文用一个one-hot 的4×15 矩阵对每张牌的组合进行编码，如图4.4 所示。
由于在斗地主中不区分花色，所以我们用每一行来表示一张特定的牌的数量。一
共有4 行，代表一副扑克牌中每种类型的牌都有4 张（除了大小王），每一列对
应13 个等级的牌和大小王，即第一列对应卡牌“3”，第二列对应卡牌“4”，依
此类推，第十二列对应卡牌“A”,第十四和十五列分别对应卡牌“大王”和“小
王”。 
 
图4.4 博弈信息编码 
Fig.4.4 Game information encoding 
在图4.4 中，信息编码的第一列有两个1，则代表牌中有两张3，再如编码
的第十四列有一个1，则代表牌中有一张小王。对于其他牌张也是用类似的方法。
更多示例如下图所示。 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-45- 
 
 
图4.5 卡牌表示的其他示例 
Fig.4.5 Additional Examples of Card Representations 
 基于信息的队友预测网络 
本文将“信息”定义为“牌型”，对于对队友信息的预测，即是预测队友的
牌型分布。本文设计的用于队友信息预测的TWMIP-Net 网络框架如图4.6 所示。 
 
图4.6 TWMIP-Net 网络示意图 
Fig.4.6 TWMIP-Net network diagram 
本网络用于农民间的互相预测手牌信息，从而达到隐式合作的目的。具体的网络
结构描述为：对于每个15×4 卡矩阵，首先将该矩阵平化为一个大小为60 的一
维向量。然后删除6 个总是为零的条目，因为只有一个黑色或红色的大王或小
王。换句话说，每个卡片矩阵被转换为一个大小为54 的单热向量。除了卡片矩
阵，我们进一步使用一个热向量来表示其他两个玩家当前的手牌。例如，对于农
民，使用一个大小为17 的向量，其中每个条目对应于当前状态下的手卡的数量。
Historical moves
h
……
……
…………
Input layer
Hidden layer
Output layer
.
.
.
.
.
.
.
.
State
15个
15个
2
♣
10
♣
10
♦
3
♣
4
♥
5
♥
6
♣
7
♥
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-46- 
 
对于地主来说，向量的大小是20，因为地主手上最多可以有20 张牌。类似地，
使用一个15 维向量来表示当前状态下的炸弹数量。对于历史移动，考虑最近的
15 个移动，并将每三个连续动作的表示连接起来；也就是说，历史动作被编码到
一个5×162 矩阵中。历史动作被输入到一个LSTM 中，使用最后一个神经单元
中的隐藏表示来表示历史动作。如果历史动作少于15 步，我们使用零矩阵表示
缺失的动作。网络的输入分为两个模块，第一个模块是目前牌局中所能观测到的
状态信息，其中具体包括11 种，分别是自己的手牌信息、其他两位玩家的卡牌
信息、卡牌的最近行动、地主最近采取的行动、另一位农民最近采取的行动、地
主出过的牌、另一位农民出过的牌、地主剩余牌、队友剩余牌数、当前状态下炸
弹的数量和最近15 步的行动，共计430 维。第二个模块为历史的出牌信息，LSTM
网络的输出为128 维的数据，将其与第一个模块的牌局状态信息进行拼接，送入
4 个全连接层，输出512 维数据，再将512 维数据分别输入到15 个预测每种牌
型个数的模块中，其中预测A-K 的网络由一个（512，256）的全连接、ReLu 层、
（256，5）的全连接组成，预测大小王的网络结构直接由一个（512,5）全连接层
组成。图4.6 的网络示意图可以具体表示为图4.7。其中输出部分的牌型用5 维
one-hot 矩阵表示。网络的具体参数表如表4.1 所示。 
 
 
图4.7 TWMIP-Net 网络结构图 
Fig.4.7 TWMIP-Net network structure diagram 
 
 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-47- 
 
表4.1 TWMIP-Net 网络参数表 
Table 4.1 TWMIP-Net network parameters table 
 
输入特征 
尺寸大小 
 
 
 
 
状态 
自己的手牌 
54 
另外两位玩家的联合手牌 
54 
最近行动 
54 
地主最近的行动 
54 
另一位农民最近的行动 
54 
地主出过的牌 
54 
另一位农民出过的牌 
54 
地主剩余的牌 
20 
另一位农民剩余的牌 
17 
当前状态炸弹的数量 
15 
历史信息 
最近15 步的行动 
5*162 
 基于策略的队友预测网络 
 
图4.8 TWMPP-Net 网络示意图 
Fig.4.8 TWMPP-Net network diagram 
本文将“策略”定义为“牌力”，对于对队友策略的预测，即是预测队友的
手牌强度如何。本文设计的用于队友策略预测的TWMPP-Net 网络框架如图4.8
所示。TWMPP-Net 网络的输入信息与TWMIP-Net 的输入完全相同，不同的是
Historical moves
h
……
……
…………
Input layer
Hidden layer
Output layer
State
2
♣
10
♣
10
♦
3
♣
4
♥
5
♥
6
♣
7
♥
.
.
.
.
7个
256*1
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-48- 
 
输出部分。TWMPP-Net 网络将输入送入二个全连接层，输出256 维数据，再将
256 维数据分别输入到7 种关键牌型个数预测网络中，每个预测网络由一个（256，
5）的全连接组成。根据斗地主中卡牌的不同组合，本文选取了对获胜影响较大
的7 种牌力进行预测，它们分别是：是否有飞机、炸弹的数量（最多四个炸弹）、
有无王炸、有无大王、有无小王、“2”的数量、是否有大顺子（7 张以上）。 
4.5 实验结果及分析 
 实验环境 
本实验的系统环境如表4.2 所示。 
表4.2 实验硬件和软件配置 
Table 4.2 Experimental hardware and software configuration 
 
硬件设备 
处理器 
Intel(R) Xeon(R) Silver 4110 CPU @ 2.10GHz 
显卡 
NVIDIA GeForce RTX 2080*4 
内存 
128G 
硬盘 
SSD 3T(500GB*6) 
 
操作系统 
Ubuntu 16.04 LTS 
软件设备 
框架 
PyTorch1.2.0 
 
编程语言 
Python3.6 
 
开发环境 
PyCharm Community Edition 2020.3 
 
 训练策略 
为了对本文所提出的TWMIP-Net 和TWMPP-Net 网络进行验证，首先通过
监督学习训练了一个斗地主智能体。本文从一个流行的斗地主游戏手机应用程序
内部收集用户数据，并过滤掉原始数据，只保留高段位玩家生成的数据，以确保
数据质量。经过过滤，得到226230 个人类专家数据，使用监督损失来训练网络。 
由于本文的预测模型本质上是一个分类问题，因此目标是根据给定的状态预
测行动，总共是27242 个类。然而，在实践中发现，大多数操作都是非法的，并
且迭代所有类的代价是昂贵的。受Q-network 设计的启发，本文将问题转化为二
进制分类任务。使用与TWMIP-Net 和TWMPP-Net 相同的网络结构，并在输出
中添加一个Sigmoid 函数。然后，通过二进制交叉熵损失来训练网络。随机抽取
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-49- 
 
10%的数据进行验证，其余数据用于训练。将用户数据转换为正实例，并根据未
选择的合法移动生成负实例。最终，训练数据由49990075 个实例组成。通过研
究，进一步发现数据是不平衡的，其中负实例的数量远远大于正实例的数量。因
此，采用了基于正负实例分布的加权交叉熵损失改善了性能。本文将批量大小
batch-size 设置为8096，并训练20 个epochs。通过选择得分最高的动作进行预
测，将对验证数据具有最高精度的模型进行输出。 
 实验结果 
（1）训练结果图 
首先是预训练，通过监督学习，得到了三个位置的精确率实验结果图，分别
是地主的精确率，地主上家的精确率，地主下家的精确率，均达到了约83%的精
度。 
 
  （a）                            （b） 
 
      （c）  
图4.9 三个玩家的精确率：（a）地主的精确率;（b）地主上家的精确率;（c）地主下家的精
确率. 
Fig.4.9 Accuracy of the three players: (a) accuracy of the landlord; (b) accuracy of the 
LandlordUp; (c) accuracy of the LandlordDown. 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-50- 
 
（2）队友信息预测结果图 
 
（a）                                 （b） 
 
         （c） 
图4.10 训练及测试的Loss 曲线图和预测精确率：（a）训练的loss 曲线图;（b）测试的loss
曲线图;（c）预测准确率. 
Fig.4.10 Loss graph of training and test and the prediction accuracy: (a) Loss graph of 
training; (b) Loss graph of the test; (c) Prediction Accuracy. 
TWMIP-Net 网络的实验结果如上图所示：图（a）和图（b）分别为训练和测
试的Loss 曲线图，图（c）为TWMIP-Net 网络的预测结果图。从实验结果图中
可以看出，队友信息预测模型的准确率为52%。当epoch 为30k 时，训练的损失
函数收敛到0.15。测试的损失函数在7k 时收敛到0.3 以下。 
（3）队友策略预测结果图 
TWMIP-Net 网络的实验结果如下所示：图4.15 和图4.16 分别为训练和测试
的Loss 曲线图，图4.17 为TWMIP-Net 网络的预测结果图。 
 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-51- 
 
 
（a）                                   （b） 
 
（c） 
图4.11 训练及测试的Loss 曲线图和预测精确率：（a）训练的loss 曲线图;（b）测试的loss
曲线图;（c）预测准确率. 
Fig.4.11 Loss graph of training and test and the prediction accuracy: (a) Loss graph of 
training; (b) Loss graph of the test; (c) Prediction Accuracy. 
从实验结果图中可以看出，队友策略预测模型的准确率达到67.7%。当epoch
为11k 时，训练的损失函数收敛到0.11。测试的损失函数在2.5k 后时收敛到0.135。 
4.6 本章小结 
本章首先介绍了非完美信息博弈中的队友合作问题，然后介绍了队友建模算
法，主要介绍了基于策略偏向、决策树、神经网络这三种建模方法。接着提出了
本文所设计的TWMIP-Net 和TWMPP-Net 隐式队友建模网络，分别是基于信息
的队友建模和基于策略的队友建模。最后分别给出了队友信息预测的实验结果和
队友策略预测的实验结果。 
 
 
东北大学硕士学位论文                         第4 章 基于深度队友建模的隐式合作算法 
-52- 
 
 
 
 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-53- 
 
第5章 基于深度蒙特卡洛的博弈决策算法 
5.1 引言 
现代强化学习算法主要关注简单和小的动作空间，而在斗地主中没有取得令
人满意的进展。在本章工作中，我们提出了用TWM-DMCNet 深度强化学习解决
斗地主人工智能的方法，它增强了传统的蒙特卡洛方法与深度神经网络、动作编
码和合作行为。并通过与DeltaDou、RHCP、Random、CQN 等经典算法进行对
比实验得到实验结果，证明了经典的蒙特卡洛方法可以在具有复杂作用空间的领
域中提供强大的结果。 
5.2 深度蒙特卡洛算法 
近年来，强化学习在一些复杂的非完美信息博弈中得到了成功的应用。例如，
有相当多的关于扑克游戏[68-70]的强化学习的工作。与依赖于游戏树遍历的反事实
遗憾最小化(CFR)[62]不同，RL 是基于采样的，因此它可以很容易地推广到大规模
游戏中。通过这种方式，OpepAI、DeepMind 和腾讯利用这种技术分别在DOTA[71]、
星际争霸[72]和国王的荣誉[73]中构建了他们的游戏AI，并取得了惊人的成就，证
明了在非完美信息游戏中强化学习的有效性。最近，有一些研究将强化学习与搜
索结合起来，并在扑克游戏中显示了其有效性，如无限制德州扑克和
DouDizhu[74,75]。 
然而，由于斗地主的复杂性，传统的强化学习方法如DQN[59]和A3C[63]在这
个游戏中有着较差的表现。即使是一种改进的方法，即组合Q 网络，也不能达到
令人满意的性能。更重要的是，DeltaDou[[75]可以推断隐藏的信息，并使用MCTS
将RL 与搜索结合起来，但其计算成本昂贵，并依赖于人类的专业知识，限制了
其实用性和性能。为此，DouZero[76]利用蒙特卡罗（Monte Carlo，MC）方法[77]，
并设法击败了所有的AI 程序。这种方法也被应用于其他一些博弈中，如现代棋
盘游戏Kingdomino，和一种新型国际象棋Tibetan Jiuqi[78,79]。但与这些环境不同
的是，斗地主博弈是一个复杂的非完美信息博弈，需要在大的状态和行动空间上
进行竞争和合作。DouZero 的惊人性能揭示了蒙特卡罗方法在大型复杂卡牌游戏
中的良好效果，为未来处理复杂动作空间和非完美信息的研究提供了新的视角。 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-54- 
 
 
图5.1 蒙特卡洛分类 
Fig.5.1 Monte Carlo Classification 
蒙特卡罗法是一种无模型的强化学习算法，它可以分为如图5.1 所示的first- 
visit MC 和every-visit MC。first-visit MC 只统计每个episode 中第一次出现的状
态s 到terminal 状态的reward 之和，而every-visit MC 则统计每个episode 中每
次出现状态s 到terminal 状态的reward 之和。本文中是在every-visit MC 上进行
改进的。 
本文应用了深度蒙特卡罗(DMC)方法，它推广了基于深度神经网络的蒙特卡
罗(MC)方法进行函数逼近。上面提到的蒙特卡罗(MC)方法本质上是一种随机采
样方法，它采用随机采样的样本值去估计真实值，其原理是基于中心极限定理，
样本数量越多，其平均就越趋近于真实值。与以往常规的依赖大量先验数据的方
法相比，蒙特卡罗的优势是明显的。它不需要任何的经验以及专业知识，但却可
以根据大量的随机对弈来估计出下一步行动的价值。蒙特卡罗(MC)方法是强化
学习的关键技术，从经验中学习价值函数和最优策略，即从与环境[80]的实际交互
或者模拟交互中采样状态、行为以及奖励序列。这种技术是为情景任务设计的，
经验可以分为最终终止的情节，只有在一个事件完成时才更新价值估计和策略。
具体来说，在每个事件之后，观察到的回报被用于策略评估，然后策略可以在事
件中访问的状态进行改进。为了使用MC 方法优化策略π，该过程直观地描述为：
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-55- 
 
第一步，使用生成一个片段。第二步，对于在片段中访问的每个状态动作对
( , )
s a ，计算并更新
( , )
Q s a 的平均回报。第三步，对于该片段中的每个状态，更新
策略：
( )s

←argmax
( , )
a A Q s a

。 
在MC 方法的实践中，我们可以在步骤1 中利用探索和开发之间的平衡。此
外，上述过程可以自然地与深度神经网络相结合，从而产生深度蒙特卡罗神经网
络(DMC)。通过这种方法，Q 表可以被神经网络所取代，而神经网络可以在步骤
2 中通过均方误差(MSE)损失进行优化。 
由于斗地主是一个典型的情景性任务，MC 自然就适合用于这个问题。更重
要的是，DMC 需要大量的训练经验，同时也很容易有效地并行生成数据，这也
可以缓解高方差的问题。此外，与其他强化学习算法和深度Q 学习相比，有一些
明显的优势。如：首先，DMC 可以很自然地利用动作特性，通过将动作特性作
为输入来概括不可见的动作。虽然如果操作大小很大，可能会有很高的执行复杂
度，但在斗地主的大多数状态下，只有一个操作的子集是合法的，因此我们不需
要遍历所有的操作。因此，DMC 是一种总体上有效的针对斗地主的算法。由于
DMC 在斗地主中的优势，因此本文采用了该算法，取得了优异的性能。 
5.3 奖励值优化设计 
5.3.1 奖励值稀疏问题 
在多智能体增强式的学习中，奖励价值是一种可以被计算的准则，智能体的
行为决策过程由其提供标准，因此它可以被看作是一种对智能体行为进行优化的
反馈。稀疏奖励作为常见的奖励之一，其与密集奖励相对应。密集奖励通常是一
种持续性的奖励，智能体每做一步动作都会得到相应的正向奖励或者负向奖励，
而稀疏奖励是离散型的奖励，并不是每一步动作都可以得到及时的回报，它的公
式表述如公式（5.1）所示： 
, 
0, 
t
r success
R
fail

= 

                      (5.1)  
通常在以目标为导向的多智能体任务中都使用稀疏奖励的形式。然而稀疏奖
励问题有着很大的难度，比如人们利用强化学习训练围棋博弈的时候，只有在一
局对弈结束后才会产生奖励值。对于即时战略而言，动作复杂度高，环境变化多
样，智能体拥有复合目标的特点，使得奖励值获取难度更高。在将奖励值作为衡
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-56- 
 
量标准的强化学习业界中，稀疏奖励无法为学习智能体提供高质量的反馈，以至
于给智能体学习策略造成了很大的困难。具体的挑战主要有以下两点： 
（1）稀疏奖励使样本数据的效率较低。玩斗地主的过程中，每一轮结束不
会产生即时奖励，而只是在一整局结束后才会有一个奖励值，这种方式造成了在
智能体探索时所收获的奖励为0。因此，就算在利用经验回放方式的训练算法里
（如本文在2.4 节提到的DQN），样本效率仍然很低。我们知道，经验回放机制
以储存历史经验训练当前时刻的策略为原理，可是在存在稀疏奖励的情形下，经
验池里储存的历史经验大多数奖励值为0，换句话说，所储存的样本对训练策略
起不到很大作用，几乎对提升策略无效。而能为策略训练带来很高价值的历史经
验太少，样本的利用率很低，智能体要花费很多的时间去摸索，造成学习速度很
慢，这将会使得智能体在很大程度上没有办法学到有效的策略。 
（2）探索效率低是稀疏奖励带来的另一个问题。目前，由于存在稀疏奖励，
导致了资源开发的低效益。长久以来，加强研究中所关心的问题就是探索与使用
之间的均衡问题，其中一个最直观的影响就是当稀疏奖励存在时，个体可能会利
用历史经验而减少对环境的探索，这会导致智能体学到的策略是次优的。在稀疏
奖励设定中，会出现大量的无效样本，也就是说，仅有少数的经验是有效的。因
为很久没有得到来自外界的信息，所以智能体对探索失去了兴趣，产生了惰性，
反而更喜欢从现有的信息中获取信息。当智能体在随机搜索的时候，他就会意识
到一个有可能创造出高价值的状态或动作，因此会对自己的行为产生依赖，进而
引发次优行为的出现。 
上述关于稀缺性奖励的问题在强化学习中是一个主要的问题。在多智能体的
强化学习中，关于稀疏奖励的应用还很少见，这与此问题的复杂性有着紧密的联
系。由于受信用分配问题、环境不稳定、维度灾难等因素的制约，进一步扩大了
稀疏奖励的负面影响，使得智能体的学习变得更为艰难和缓慢。在多智能体强化
学习中，如何有效地求解稀疏奖励问题显得尤为关键。 
5.3.2 奖励函数设计 
本文以斗地主博弈作为实验对象，针对其在多智能体场景中的稀疏奖励问题，
本文通过学习一个评估行动探索价值的函数来解耦探索和开发。方法的关键思想
是设计一个访问值函数，该函数称为W 函数，用
( , )
W
s a

表示，根据探索次数近
似于基于访问计数的内在奖励
W

的累积总和，即 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-57- 
 
           
(
)
(
) (
)
1
1
1
,
,
:
t
t
i
i
i
i t
H
i t
W
t
t
w
i
a
s
s
s a
i t
W
s a
r



+
+
+
=
−

=


=





∣
∣
               (5.2) 
其中，
W

为探索次数折扣因子。折扣越高，未来的探索次数就会越多。使用下
式进行更新： 
(
)
(
)
(
)
1
1
,
,
,
,
W
i
t
t
i
t
t
t
t
t
W
s a
W
s a
s a s



+
+
=
+
             (5.3) 
随后，根据上置信界(UCB)算法，该行为策略是贪婪的q 函数和上置信界的和： 


(
)
arg max
( , )
( , )
W
a
a s
Q
s a
U
s a



=
+
∣
              (5.4) 
其中，
( , )
W
U
s a 是基于
( , )
W
s a

的置信上界，是一个比例系数。基于奖励
W
r
和
上限
( , )
W
U
s a ，将W 函数作为长期UCB。探视奖励为： 
       
(
)
(
)
(
)
(
)
2log
,
               if s  is non-terminal 
,
2log
,
1
     otherwise.    
1
,
j
j
t
j
a
t
t
t
t
j
a
w
t
W
t
tr
n s a
n s a
n s a
n s a





= 





−
         (5.5) 
此时，W 函数代表折扣加权平均UCB，即 
        
(
)
(
)
(
)
UCB
2log
,
,
,
j
i
j
i t
a
i t
t
t
w
H
i
i
n s a
W
s a
n s a


=
−

= 
                      (5.6) 
对于区分公式（5.5）中奖励的终端状态和非终端状态来说，终端状态的访问
值实际上等于单独的探索奖励。因此，终端状态的访问值可能明显小于其他状态
之一，并且智能体在第一次访问后不会再访问它们。然而，这可能对学习q 函数
有害，特别是当终端状态产生高奖励时。因此，我们假设智能体永远停留在终端
状态，在每个时间步长都获得相同的探索奖励。折扣奖励之和的极限是公式（5.5）
中的奖励。 
5.4 合作式博弈决策网络设计 
将上一章队友建模的部分称为“预测模型”，将本章所设计的做出决策的部
分称为“决策模型”。该决策模型采用深度蒙特卡洛算法进行训练。将关于上一
章种队友合作网络的预测结果与状态特征以及动作特征连接起来，并将所有这些
信息输入到决策模型中，以决定采取哪种行动。 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-58- 
 
 
图5.2 TWMDMC-Net 网络框架 
Fig.5.2 TWMDMC-Net Network Framework 
对图5.2 进行描述如下：对于斗地主游戏中的两个农民玩家建立相同架构的
两个选取动作的网络，网络的输入由状态特征和动作特征组成。状态特征表示玩
家已知的信息，而动作特征描述与当前状态对应的合法移动。具体来说，网络输
入为合法动作信息、可观测的状态信息以及三个玩家五轮的历史动作信息（即15
个历史动作），经过整个网络输出各个合法动作的q 值，玩家根据贪婪算法来选
取动作。根据Act 和Learner 两个部分来具体描述整个网络架构，在act 选取动
作生数据部分，玩家根据获取的可观测信息进行合法动作筛选，将状态信息和历
史动作信息输入手牌预测和牌力预测网络，得到预测结果，将状态信息，历史动
作信息，预测结果根据合法动作的数量进行复制，使每个合法动作都匹配上状态
信息、历史动作信息和预测结果，再将这四种信息输入6 层全连接，输出各个动
作q 值。在learner 部分，每次输入3200 个样本（一个动作，一个状态信息，一
个历史动作信息，两个预测信息）进行不断地自博强化。其结构图如下所示。 
 
图5.3 TWMDMC-Net 网络结构图 
Fig.5.3 TWMDMC-Net network structure diagram 
Encode
Action
State
……
……
…………
Prediction model1
Prediction model2
Historical moves
h
2
♣
10
♣
10
♦
3
♣
4
♥
5
♥
6
♣
7
♥
Encode
Encode
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-59- 
 
5.5 实验结果及分析 
5.5.1 训练过程 
称地主为L，在地主之前移动的农民为U，在地主之后移动的农民为D。本
实验将多个参与者过程和一个学习过程并行化DMC，伪代码如下： 
表5.1 行为者进程伪代码 
Table 5.1 Actor process pseudo-code 
算法 ：行为者进程  
1： 
输入:有B 个条目的共享缓冲区
U
B 和
D
B ，每个条目的大小为S，探
索超参数为ϵ，折扣系数为γ。 
2： 
初始化本地Q 网络
U
Q 和
D
Q 以及本地缓冲区
U
D and
D
D  
3： 
for 迭代=1, 2, ... do 
4： 
将Q𝑈, 
D
Q 与学习者进程同步化 
5： 
for t =1, 2, ... T do       生成一个片段 
6： 
Q←基于位置的
U
Q , 
D
Q 中的一个 
7： 
at←{
arg maxa
( , )
t
Q s a ，当概率为(1 − ϵ )时，
随机行动，当概率为ϵ 时。
 
8： 
QU:执行
ta ，观察
1
ts + 和奖励tr  
9: 
QD:执行
'
ta ，观察
'
1
ts + 和奖励tr  
10: 
存储: { ,
, }
t
t
t
s a r
 for
L
D  
11: 
存储: 
'
'
{ ,
, }
t
t
t
s a r
 for
D
D  
12: 
end for 
13: 
for t = T-1, T-2, ... 1 do 
14: 
tr ←
1
t
t
r
r

+
+
，更新tr in 
U
D ,
D
D  
 
表5.2 学习者进程伪代码 
Table 5.2 Learner process pseudo-code 
算法 ：学习者进程 
1： 
输入:有B 个条目的共享缓冲区
U
B 和
D
B ，每个条目的大小为S，批
量大小为M ，学习率为 
2： 
初始化本地Q 网络
g
U
Q 和
g
D
Q  
3： 
for 迭代=1, 2, ... 直到收敛do 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-60- 
 
4： 
    for
{ ,
}
p
U D

 do       通过多线程进行优化 
5： 
      if 
p
B 中所有的条目
M

then 
6： 
        从
p
B 中的
×
M S 个实例中采样一批{ ,
, }
t
t
t
s a r
并释放条目 
7： 
        用MSE 损失和学习率更新
p
g
Q  
8： 
      end if 
9: 
    end for 
10: 
end for 
学习者为这三个位置维护三个全局Q 网络，并根据MSE 损失提供的数据来
近似目标值。每个参与者维护三个本地Q 网络，它们定期与全局网络同步。参与
者将从游戏引擎中重复采样轨迹，并计算每个状态-动作对的累积奖励。学习者
和参与者之间的交流是通过三个共享的缓冲区来实现的。每个缓冲区被分为几个
条目，其中每个条目由几个数据实例组成。训练的结果将在5.5.3 节给出。 
 对比实验 
本实验的系统环境与4.5.1 节相同。为了评估TWMDMC-Net 模型的性能，
在RLcard 平台上进行了包括地主和农民双方对立的比赛。为了方便描述，对于
两种算法A 和B，本文将所设计算法称为A，将对比算法统称为B，然后让本文
算法作用在两个农民智能体上，让地主智能体应用B 算法，进行对弈。通过与现
如今流行的几种算法进行对比，测试出了本文算法在队友合作方面的性能。 
第一个对比实验是与DeltaDou 进行对比。DeltaDou 一个强大的人工智能程
序，它使用贝叶斯方法推断隐藏的信息，并使用MCTS 搜索移动，本文使用了作
者提供的代码和预训练过的模型。该模型经过了两个月的训练，显示出与顶级人
类选手相当。 
第二个对比实验是与基于规则的程序进行对比。分别将基于开放的启发式程
序RHCP 以及Random 随机程序用作地主的算法进行了对弈。 
第三个对比实验是CQN。组合Q 学习是一个基于卡牌分解和深度Q 学习的
程序。本文使用了其开源代码和作者提供的预训练模型。 
对于评价指标，本文全部以获胜百分率（WP）作为评价指标。WP 表示为本
算法所赢得的游戏数除以游戏总数。 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-61- 
 
 实验结果 
（1）训练结果 
图5.4 为农民和地主的Loss 曲线图，图5.5 为农民和地主的收益曲线图。从
结果中可以看出，农民的收益为正，地主的收益为负，表明了农民获胜的次数多
于地主，揭示了本算法在对弈中能够帮助农民提高获胜的概率，在队友合作问题
中有一定的有效性。 
 
（a）                                （b） 
图5.4 农民和地主的Loss 曲线图：（a）农民的Loss 曲线图;（b）地主的Loss 的曲线图. 
Fig.5.4 Loss Curve Graph of the Farmer and the Landlord: (a) Loss Curve Graph of the 
Farmer; (b) Loss Curve Graph of the Landlord. 
 
（a）                                  （b） 
图5.5 农民和地主的收益曲线图：（a）农民的收益曲线图;（b）地主的收益曲线图. 
Fig.5.5 Revenue Curve Graph of the Farmer and the Landlord: (a) Revenue Curve Graph          
    of the Farmer; (b) Revenue Curve Graph of the Landlord. 
（2）对比实验结果 
TWMDMC-Net 分别与DeltaDou、RHCP、CQN、Random 进行了100000 次对局，
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-62- 
 
下表为具体实验结果。获胜百分比的柱状图表示如图5.6 所示。 
表5.3 实验对比结果 
Table.5.3 Experimental comparison results 
算法名称 
对局总数 
获胜次数 
获胜百分比 
TWMDMC-Net 
100000 
56582 
56.59% 
DeltaDou 
100000 
43418 
43.41% 
TWMDMC-Net 
100000 
72562 
72.56% 
RHCP 
100000 
27438 
27.42% 
TWMDMC-Net 
100000 
83213 
83.21% 
CQN 
100000 
16787 
16.79% 
TWMDMC-Net 
100000 
88125 
88.13% 
Random 
100000 
11975 
11.98% 
 
图5.6 不同算法获胜百分比 
Fig.5.6 Percentage of winning by different algorithms 
结果表明，本文所提出的算法提高了农民模型的性能，其中，在实验过程中
发现模型一开始的表现比DeltaDou 差，这是因为网络必须接受更多的特征作为
输入，并且有更多的神经元，这将使学习速度减慢，但经过足够的训练后，它们
能够掌握更多的知识，并取得比DeltaDou 更好的性能。以上研究表明，将队友
建模与深度强化学习相结合，有利于在多智能体不完全信息博弈中实现性能增益。 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-63- 
 
5.6 本章小结 
本章提出了基于合作的深度强化学习算法解决斗地主问题的新方法，并将该
方法命名为TWM-DMCNet。将第四章的TWMIP-Net 网络和TWMPP-Net 网络
的输出作为TWM-DMCNet 的输入。首先对深度蒙特卡洛算法进行了介绍，接着
对奖励值进行了优化设计，缓解奖励稀疏的问题，然后介绍了合作式博弈决策网
络的结构，并对训练过程和对比试验加以说明，最后给出了农民和地主的收益曲
线图以及每种算法的获胜百分比，并对结果进行了分析说明。 
 
 
东北大学硕士学位论文                         第5 章 基于深度蒙特卡洛的博弈决策算法 
-64- 
 
 
 
 
东北大学硕士学位论文                                          第6 章 总结与展望 
-65- 
 
第6章 总结与展望 
6.1 本文总结 
随着人工智能技术的日趋成熟，无论是像围棋、象棋那样的完美信息多智
能体博弈，还是像扑克牌那样的非完美信息多智能体博弈，都得到了很大的发
展，而非完备信息多智能体博弈由于其复杂性高，发展缓慢，但其与真实生活
的关系更加紧密，因此，对于非完美信息博弈进行探讨显然是很有必要的，越
来越多的学者对其进行研究，使其成为了目前人工智能领域的热点。但目前研
究人员对非完美信息博弈的研究，很多都是集中在智能体间的纯对抗，或者是
纯合作，而对于既竞争又合作的博弈研究甚少，因此基于合作式对抗的非完美
信息多智能体博弈是亟待解决的问题，针对该研究领域，本文设计了全新的非
完美信息多智能体博弈算法，主要工作如下： 
（1）针对斗地主扑克博弈中的队友信息预测问题，本文提出了TWMIP-Net
算法。TWMIP-Net 网络的输入分为两个模块，一个模块为当前牌局所能观测到
的信息，其中包括自己的手牌信息、地主剩余牌数、队友剩余牌数等，另一部分
为历史的出牌信息，将这两个模块的信息进行拼接，输入到四个全连接层中，再
经过预测15 种牌型个数的模块，最后输出每种牌型的数量的概率，以此来达到
预测队友牌型的目的。 
（2）针对斗地主扑克博弈中的队友策略预测问题，也即队友牌力预测，本
文提出了TWMPP-Net 算法。TWMPP-Net 网络的输入与TWMIP-Net 是一致的，
包括自己的手牌信息、地主剩余牌数、队友剩余牌数、历史出牌信息等，输出是
七种对获胜具有较大影响的牌型组合，包括是否有飞机、炸弹的数量（最多四个
炸弹）、有无王炸、有无大王、有无小王、“2”的数量、是否有大顺子（7 张以
上）。通过对牌力预测对队友的手牌有一个更好的掌握，从而更好的合作。 
（3）本文将TWMIP-Net 和TWMPP-Net 与深度蒙特卡洛方法结合，提出了
TWM-DMC-Net 网络，并针对稀疏奖励的问题设计了一个访问值函数。强了传统
的蒙特卡方法与深度神经网络、动作编码和合作行为。最后，与A3C、RL-based
以及SL 等流行方法进行对比实验，证明了所提算法在非完美信息多智能体合作
式对抗问题中优于其他求解算法。 
东北大学硕士学位论文                                          第6 章 总结与展望 
-66- 
 
6.2 工作展望 
本文对非完美信息多智能体博弈及相关算法进行了研宄，针对多智能体非完
美信息机器博弈下队友建模方面，提出了TWMIP-Net 和TWMPP-Net 算法，并
将算法应用在斗地主扑克上，实现了具有较高博弈水平的二打一扑克智能体。但
是，本文的工作仍存在很大的改进空间，具体可以从以下几个方面作进一步研究： 
（1）对于非完美信息多智能体合作博弈，可以考虑使用显式的多智能体合作
机制，通过智能体间的通讯和交流机制实现隐藏信息下的合作任务。 
（2）可以将对斗地主中叫牌阶段的研究融合进来，后续可以考虑设计循环叫
牌的强化学习算法。 
（3）本文所设计的网络结构比较复杂，而且网络是根据斗地主扑克的特点进
行设计的，网络的输入包含了许多斗地主扑克特有的信息，因此网络难以适用于
其它扑克游戏，可对扑克游戏进行研宄，设计出通用的扑克网络模型。
 
 
 
 
 
东北大学硕士学位论文                                                       附录 
-67- 
 
附录 
附录A 斗地主基本规则 
斗地主博弈中的出牌种类的描述如表A.1 所示。 
 
表A.1 斗地主中的所有可能出牌情况 
 
Table A.1 All possible card situations in Landlord 
牌型 
说明 
数量 
单张 
A、K、9 
15 
对子 
AA、22、33 
13 
三张 
444、555 
13 
三带一 
6665、7773 
182 
三带二 
66655、88833 
156 
顺子 
34567、5678910J 
36 
连对 
667788、JJQQKKAA 
52 
三顺 
444555、666777888 
45 
飞机带单张翅膀 
77788835、JJJQQQ45 
21822 
飞机带对牌翅膀 
6667773355、8889994455 
2939 
四带二单 
333356、555589 
1326 
四带二对 
444455、999966 
858 
炸弹 
9999、AAAA 
13 
火箭/王炸 
大小王同时出 
1 
过牌（pass） 
不出牌 
1 
总计 
 
27472 
附录B 斗地主出牌规则 
斗地主游戏一般包括三个人，一个是“地主”，两个是“农民”。两名“农民”
玩家中，在“地主”出牌后紧挨着出牌的“农民”被称为“农民1”，而另一个“农
民”则被称为“农民2”。“斗地主”游戏的全过程包括：发牌、叫牌、出牌、判
断胜负四个阶段： 
首先是发牌阶段。将一副牌（54 张牌，包括3，4，5，6，7，8，9，T,，J，
东北大学硕士学位论文                                                       附录 
-68- 
 
Q，K，大王，小王，其中大小王各一张，其余牌各四张）其中51 张牌被打散，
然后平均分配给三名玩家，剩下的3 张牌则是底牌。在确认"地主"之前，三个人
都无法查看。在叫牌阶段，当"地主"被确认时，底牌被翻开，并给"地主"。 
其次是叫牌阶段。3 名选手按照第一轮的顺序，开始叫牌，每名选手可以叫
牌一次，叫出的分数是“一分”，“二分”，“三分”，甚至是“零分”。当有人叫出
分数的时候，另外两个人可以叫“三分”，也可以不叫。如果一名选手喊出"三分
"，则该选手被认定为"地主"。另外，如果三个人都喊完了，并且没有一个人喊出
的分数超过"3 分"，那么谁的点数最高，谁就是“地主”。当“地主”被确认之后，
就会把剩下的三个牌都拿出来，分发给“地主”。如果三个人同时不喊，那么该
游戏就被认为是无效的，或者被认为已经结束了，三个人将不会在这一轮中得到
任何的分数。 
然后是出牌阶段。游戏开始时，首先是扮演"地主"的玩家开始出牌,然后按照
逆时针顺序，其余两位玩家依次出牌或者选择过牌（pass），如果选择出牌，那么
牌的类型应与上家一致（炸弹除外）。后续牌手必须比前一牌手出的牌大，或者
不出牌。如果连续两个玩家都没有选择出牌，那么后续玩家可以选择任何合法的
牌。在出牌阶段，一轮游戏表示玩家开始玩牌到下一次该玩家出牌的阶段。 
最后是判定胜负阶段。一场游戏包括三名选手。由叫牌决定一个人是“地主”，
其余两个人就是“农民”。两个“农民”是合作的，“地主”和“农民”是对立
的。如果“农民”或“地主”中的任何一个人先把手上的牌都用光了，那么这场
比赛就结束了。如果“地主”玩家先出了所有的牌，那么这场游戏的“地主”就
赢了，两个“农民”选手输掉了游戏。否则，将会被判定为其他两个“农民”玩
家，将获得这场游戏的胜利，而“地主”将会失去这一场游戏。 
在斗地主中，牌型的大小为:双王是最大的，能够管住任意其他类型的牌张；  
第二大的牌张类型是炸弹，它仅次于双王，比其他任何一种牌都大，当玩家打出
的牌都是炸弹时，则按照牌的分值来比较大小；除了双张之王和炸弹外，其他的
牌张必须保证牌型相同并且出牌的张数一样才可以比较大小；单张牌的分值按照
从小到大的排序为:3<4<5<6<7<8<9<T<J<Q<K<A<2<小王<大王，其中每张牌不
对花色进行区分；单顺从5 张牌起连，双顺从3 个起连，三顺则从2 个起连；飞
机带翅膀牌型以及四带二牌型的大小比较则按照其中的飞机和四张部分进行对
比，带的牌张并不影响大小。 
 
 
东北大学硕士学位论文                                                   参考文献 
-69- 
 
参考文献 
[1] 李杨, 徐峰, 谢光强, et al.多智能体技术发展及其应用综述[J].计算机工程与
应用,2018, 54 (09): 13-21. 
[2] Berliner H J.Backgammon computer program beats world champion[J].Artificial 
Intelligence,1980, 14 (2): 205-220. 
[3] Schaeffer J, Lake R, Lu P, et al.Chinook: The World Man-Machine Checkers 
Champion[J].Ai Magazine,1996, 17 (1): 25-27. 
[4] Holmes R J, Dandrade B W, Forrest S R, et al.Efficient, deep-blue organic 
electrophosphorescence by guest charge trapping[J].Applied Physics Letters,2003, 
83 (18): 3818-3820. 
[5] Coulom R. Efficient Selectivity and Backup Operators in Monte-Carlo Tree 
Search[C].Proc of the International Conference on Computer & Games,2006: 72-
83. 
[6] Babbar S.Review - Mastering the game of Go with deep neural networks and tree 
search[J], Nature 2016, 529: 484–489. 
[7] Hart S, Mas-Colell A.A Simple Adaptive Procedure Leading to Correlated 
Equilibrium[J].Game Theory and Information,1997: 1127-1150. 
[8] Billings D, Burch N, Davidson A, et al. Approximating game-theoretic optimal 
strategies for full-scale poker[C].IJCAI,2003: 661. 
[9] Zinkevich M, Bowling M, Bard N, et al. Optimal unbiased estimators for 
evaluating agent performance[C].AAAI,2006: 573-579. 
[10] Zinkevich M, Johanson M, Bowling M, et al.Regret minimization in games with 
incomplete 
information[J].Advances 
in 
neural 
information 
processing 
systems,2007, 20. 
[11] Burch N, Johanson M, Bowling M. Solving imperfect information games using 
decomposition[C].Twenty-eighth AAAI conference on artificial intelligence,2014: 
602-608. 
[12] Ganzfried S, Sandholm T. Endgame solving in large imperfect-information 
games[C].Workshops at the Twenty-Ninth AAAI Conference on Artificial 
东北大学硕士学位论文                                                   参考文献 
-70- 
 
Intelligence,2015: 455-462. 
[13] Moravcik M, Schmid M, Ha K, et al. Refining subgames in large imperfect 
information games[C].Proceedings of the AAAI Conference on Artificial 
Intelligence,2016: 572-578. 
[14] Gibson R, Lanctot M, Burch N, et al. Generalized sampling and variance in 
counterfactual regret minimization[C].Proceedings of the AAAI Conference on 
Artificial Intelligence,2012: 1355-1361. 
[15] Bowling M, Burch N, Johanson M, et al.Heads-up limit hold’em poker is 
solved[J].Science,2015, 347 (6218): 145-149. 
[16] Moravcık M, Schmid M, Burch N, et al. Deepstack: expert-level artificial 
intelligence in no-limit poker. CoRR abs/1701.01724 (2017). 
[17] Brown N, Sandholm T.Safe and nested subgame solving for imperfect-information 
games[J].Advances in neural information processing systems,2017, 30. 
[18] Burch N, Lanctot M, Szafron D, et al.Efficient Monte Carlo counterfactual regret 
minimization in games with many player actions[J].Advances in neural 
information processing systems,2012, 25. 
[19] Lisý V, Lanctot M, Bowling M H. Online Monte Carlo Counterfactual Regret 
Minimization for Search in Imperfect Information Games[C].AAMAS,2015: 27-
36. 
[20] Brown N. Equilibrium Finding for Large Adversarial Imperfect-Information 
Games[D]. US army,2020. 
[21] 魏钦刚, 王骄, 徐心和, et al.中国象棋计算机博弈开局库研究与设计[J].智能
系统学报,2007 (01): 85-89. 
[22] 徐心和, 王骄.中国象棋计算机博弈关键技术分析[J].小型微型计算机系
统,2006 (06): 961-969. 
[23] 胡书豪. 基于虚拟自我对局的非完备信息博弈策略研究[D]. 哈尔滨工业大
学,2020. 
[24] 弗登博格. 博弈论(经济科学译丛)[M].  博弈论(经济科学译丛),2006. 
[25] Singh S, Trivedi A, Garg N.Collaborative anti-jamming in cognitive radio networks 
using Minimax-Q learning[J].International Journal of Modern Education and 
东北大学硕士学位论文                                                   参考文献 
-71- 
 
Computer Science,2013, 5 (9): 11. 
[26] Cho K, Van Merriënboer B, Gulcehre C, et al.Learning phrase representations 
using RNN encoder-decoder for statistical machine translation[J].arXiv preprint 
arXiv:1406.1078,2014. 
[27] Zhao Y, Tao D-P, Zhang S-Y, et al.Similar handwritten Chinese character 
recognition based on deep neural networks with big data[J].Journal on 
Communications,2014, 35 (9): 184. 
[28] Du J, Jiang C, Benslimane A, et al. Stackelberg differential game based resource 
sharing 
in 
hierarchical 
fog-cloud 
computing[C].2019 
IEEE 
Global 
Communications Conference (GLOBECOM),2019: 1-6. 
[29] Alshehri A, Badawy A-H A, Huang H.FQ-AGO: fuzzy logic Q-learning based 
asymmetric link aware and geographic opportunistic routing scheme for 
MANETs[J].Electronics,2020, 9 (4): 576. 
[30] Hu J, Wellman M P.Nash Q-learning for general-sum stochastic games[J].Journal 
of machine learning research,2003, 4 (2): 1039-1069. 
[31] Mcculloch W S, Pitts W.A Logical Calculus of the Ideas Immanent in Nervous 
Activity[J].biol math biophys,1943. 
[32] Ackley D H, Hinton G E, Sejnowski T J.A learning algorithm for Boltzmann 
machines[J].Cognitive science,1985, 9 (1): 147-169. 
[33] Grossberg S.Some networks that can learn, remember, and reproduce any number 
of 
complicated 
space-time 
patterns. 
II[J].Journal 
of 
Mathematics 
& 
Mechanics,1969, 19 (1): 135–166. 
[34] Hopfield J J.Neural networks and physical systems with emergent collective 
computational abilities[J].Proceedings of the national academy of sciences,1982, 
79 (8): 2554-2558. 
[35] Kohonen T.Correlation Matrix Memories[J].MIT Press,1972. 
[36] Palm G.On associative memory[J].Biological Cybernetics,1980, 36 (1): 19. 
[37] Rumelhart D E, Hinton G E, Williams R J.Learning Representations by Back 
Propagating Errors[J].Nature,1986, 323 (6088): 533-536. 
[38] Hinton G E, Salakhutdinov R R.Reducing the dimensionality of data with neural 
东北大学硕士学位论文                                                   参考文献 
-72- 
 
networks[J].science,2006, 313 (5786): 504-507. 
[39] Lecun Y, Bottou L, Bengio Y, et al.Gradient-based learning applied to document 
recognition[J].Proceedings of the IEEE,1998, 86 (11): 2278-2324. 
[40] Hochreiter S, Bengio Y, Frasconi P, et al. Gradient flow in recurrent nets: the 
difficulty of learning long-term dependencies: A field guide to dynamical recurrent 
neural networks. IEEE Press In,2001. 
[41] Hochreiter 
S, 
Schmidhuber 
J.Long 
Short-Term 
Memory[J].Neural 
Computation,1997, 9 (8): 1735-1780. 
[42] Bellman R.Dynamic programming[J].Science,1966, 153 (3731): 34-37. 
[43] Metropolis N, Ulam S.The monte carlo method[J].Journal of the American 
statistical association,1949, 44 (247): 335-341. 
[44] Sutton R S.Learning to predict by the methods of temporal differences[J].Machine 
learning,1988, 3 (1): 9-44. 
[45] Watkins C J, Dayan P.Q-learning[J].Machine learning,1992, 8 (3): 279-292. 
[46] 侯振挺. 马尔可夫决策过程[M].  马尔可夫决策过程,1998. 
[47] Bowling M, Veloso M.Multiagent learning using a variable learning 
rate[J].Artificial Intelligence,2002, 136 (2): 215-250. 
[48] Spaan M, Spaan N. A point-based POMDP algorithm for robot planning[C].IEEE 
International Conference on Robotics & Automation,2004. 
[49] Graves A, Mohamed A-R, Hinton G. Speech recognition with deep recurrent neural 
networks[C].2013 IEEE international conference on acoustics, speech and signal 
processing,2013: 6645-6649. 
[50] Li Y, Zhang J, Pan D, et al.A study of speech recognition based on RNN-RBM 
language model[J].Journal of Computer Research and Development,2014, 51 (9): 
1936. 
[51] Krizhevsky A, Sutskever I, Hinton G E.Imagenet classification with deep 
convolutional neural networks[J].Advances in neural information processing 
systems,2012, 25. 
[52] Russakovsky O, Deng J, Su H, et al.Imagenet large scale visual recognition 
challenge[J].International journal of computer vision,2015, 115 (3): 211-252. 
东北大学硕士学位论文                                                   参考文献 
-73- 
 
[53] Kocsis L, Szepesvári C. Bandit based monte-carlo planning[C].European 
conference on machine learning,2006: 282-293. 
[54] Tesauro G.TD-Gammon, a self-teaching backgammon program, achieves master-
level play[J].Neural computation,1994, 6 (2): 215-219. 
[55] Qi-Ming F, Quan L, Hui W, et al.A novel off policy Q (λ) algorithm based on linear 
function approximation[J].Chinese Journal of Computers,2014, 37 (3): 677-686. 
[56] Ipek E, Mutlu O, Martínez J F, et al.Self-optimizing memory controllers: A 
reinforcement learning approach[J].ACM SIGARCH Computer Architecture 
News,2008, 36 (3): 39-50. 
[57] Wei Y, Zhao M.A reinforcement learning-based approach to dynamic job-shop 
scheduling[J].Acta Automatica Sinica,2005, 31 (5): 765. 
[58] Mnih V, Kavukcuoglu K, Silver D, et al.Playing atari with deep reinforcement 
learning[J].arXiv preprint arXiv:1312.5602,2013. 
[59] Mnih V, Kavukcuoglu K, Silver D, et al.Human-level control through deep 
reinforcement learning[J].nature,2015, 518 (7540): 529-533. 
[60] Lillicrap T P, Hunt J J, Pritzel A, et al.Continuous control with deep reinforcement 
learning[J].arXiv preprint arXiv:1509.02971,2015. 
[61] Zoph B, Le Q V.Neural architecture search with reinforcement learning[J].arXiv 
preprint arXiv:1611.01578,2016. 
[62] Neller T W, 
Lanctot 
M. An 
introduction 
to 
counterfactual 
regret 
minimization[C].Proceedings of Model AI Assignments, The Fourth Symposium 
on Educational Advances in Artificial Intelligence (EAAI-2013),2013. 
[63] Mnih V, Badia A P, Mirza M, et al. Asynchronous methods for deep reinforcement 
learning[C].International conference on machine learning,2016: 1928-1937. 
[64] Gilpin A, Sandholm T. Better automated abstraction techniques for imperfect 
information games, with application to Texas Hold'em poker[C].Proceedings of the 
6th international joint conference on Autonomous agents and multiagent 
systems,2007: 1-8. 
[65] Magerman D M.Statistical decision-tree models for parsing[J].arXiv preprint cmp-
lg/9504030,1995. 
东北大学硕士学位论文                                                   参考文献 
-74- 
 
[66] Kubat M.Neural networks: a comprehensive foundation by Simon Haykin, 
Macmillan, 
1994, 
ISBN 
0-02-352781-7[J].The 
Knowledge 
Engineering 
Review,1999, 13 (4): 409-412. 
[67] Nicolai G, Hilderman R J. No-limit texas hold'em poker agents created with 
evolutionary neural networks[C].2009 IEEE Symposium on Computational 
Intelligence and Games,2009: 125-131. 
[68] Heinrich J, Silver D.Deep reinforcement learning from self-play in imperfect-
information games[J].arXiv preprint arXiv:1603.01121,2016. 
[69] Lanctot M, Zambaldi V, Gruslys A, et al.A unified game-theoretic approach to 
multiagent reinforcement learning[J].Advances in neural information processing 
systems,2017, 30. 
[70] Sweeney N, Sinclair D. Applying Reinforcement Learning to Poker[C].Computer 
Poker Symposium,2012. 
[71] Berner C, Brockman G, Chan B, et al.Dota 2 with large scale deep reinforcement 
learning[J].arXiv preprint arXiv:1912.06680,2019. 
[72] Vinyals O, Babuschkin I, Czarnecki W M, et al.Grandmaster level in StarCraft II 
using multi-agent reinforcement learning[J].Nature,2019, 575 (7782): 350-354. 
[73] Ye D, Liu Z, Sun M, et al. Mastering complex control in moba games with deep 
reinforcement learning[C].Proceedings of the AAAI Conference on Artificial 
Intelligence,2020: 6672-6679. 
[74] Brown N, Bakhtin A, Lerer A, et al.Combining deep reinforcement learning and 
search for imperfect-information games[J].Advances in Neural Information 
Processing Systems,2020, 33: 17057-17069. 
[75] Jiang Q, Li K, Du B, et al. DeltaDou: Expert-level Doudizhu AI through Self-
play[C].IJCAI,2019: 1265-1271. 
[76] Zha D, Xie J, Ma W, et al. Douzero: Mastering doudizhu with self-play deep 
reinforcement learning[C].International Conference on Machine Learning,2021: 
12333-12344. 
[77] Sutton R S, Barto A G.Reinforcement learning: an introduction MIT 
Press[J].Cambridge, MA,1998, 22447. 
东北大学硕士学位论文                                                   参考文献 
-75- 
 
[78] Gedda M, Lagerkvist M Z, Butler M. Monte Carlo methods for the game 
Kingdomino[C].2018 IEEE Conference on Computational Intelligence and Games 
(CIG),2018: 1-8. 
[79] Zhou J. Design and Application of Tibetan Long Chess Using Monte Carlo 
Algorithm and Artificial Intelligence[C].Journal of Physics: Conference 
Series,2021: 42-104. 
[80] Sutton R S, Barto A G. Reinforcement learning: An introduction[M]. MIT 
press,2018. 
 
 
 
东北大学硕士学位论文                                                   参考文献 
-76- 
 
 
 
东北大学硕士学位论文                                                       致谢 
-77- 
 
致谢 
此时此刻，我百感交集，美好的研究生时光如同恍惚一场梦，纵有千般不舍，
也即将奔赴新的征程。所有的经历都是学习，所有经历，于我都是礼物，我要向
所有曾经帮助、鼓励、陪伴我的人表以最诚挚的感谢！ 
第一，我要感谢的人是我的导师王骄教授。您既是我们的老师，也是我们在
东北大学这个大家庭里的大家长。您孜孜不倦的科研态度时时刻刻都在激励着我
们前行。没有您的支持和指导，我很难写好这篇论文，感谢您的不厌其烦和苦口
婆心。饮其流者怀其源，学其成时念吾师，我深刻体会到遇良师不易，师恩深重，
将终生难忘，在此表示最赤诚的感激之情。愿王老师工作顺利，桃李满园。在此
也感谢李贞妮老师对我的指导和帮助，愿李老师合家欢乐，生活顺利。 
第二，我要感谢学长学姐、学弟学妹和同届的同学们。是于起学长、明红兵
学长、高源学长、黄星辉、张乐民、何彧衡、付龙悦博士、王诗佳博士、李想学
妹、史泽计学弟、原明睿学弟、冯彦磊学弟、刘源学弟等在我科研过程中帮助我
解决一些问题；在日常生活中，是李云博士师姐、吕柳杉学姐、牛珊珊学姐、张
宇鑫、张新淼、张雪静、陈铭学妹、赵星学妹等给出建议，帮助我处理生活中的
事情。在此感谢实验室的所有成员，愿大家平安喜乐，笑口常开。 
第三，我要感谢我的研究生室友朱灿、郑珊珊、陈红汝。我们从全国各地相
聚于605B，相互包容，相互帮助。是你们在雨天给我送伞，在我生病时帮我带
饭，在我难过时陪伴在我身边。我不会忘记我们在宿舍一起聊天、哈哈大笑的时
刻，不会忘记我们一起出去玩，一起聚餐，一起划船的情景，你们带给了我很多
快乐，让我觉得即使一个人深处陌生的城市也不会孤单。愿大家前程似锦，都可
以实现自己的理想生活。 
第四，我要感谢我的家人。你们永远尊重我、倾听我、指引我，这始终是让
我觉得心安的事情。在我学业不顺利或者心情不好的时候，我会给家里打电话，
唠唠家常，听听你们的声音，每次打完电话我都会心情转好，你们是我最大的精
神支撑。我描述不出我有多爱你们，我只知道你们开心是我最大的幸福。愿家人
身体健康，平平安安。 
此外，由衷感谢论文评阅老师们的辛苦工作以及答辩老师们的指导与教诲。 
最后，祝愿所有真心爱我的人平平安安，一帆风顺！ 
东北大学硕士学位论文                                                       致谢 
-78- 
 
 
 
东北大学硕士学位论文                         攻读硕士学位期间主要成果及获奖情况 
-79- 
 
攻读硕士学位期间主要成果及获奖情况 
主要成果： 
参与科研项目：国家自然科学基金重点项目：“复杂环境下的机器博弈算法
与应用”（在其中担任了子课题的主要研究员。） 
获奖情况： 
1、 一等奖学金，2019.09 
2、 一等奖学金，2020.09 
3、 二等奖学金，2021.09 
4、 “志愿服务先锋”，2020.12 
 
