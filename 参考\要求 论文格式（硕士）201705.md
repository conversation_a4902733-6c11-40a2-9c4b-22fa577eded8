## 东北大学硕士学位论文排版打印格式

1\. 引言

依据中华人民共和国《科学技术报告、学位论文和学术论文的编写格式》和东北大学学位论文格式改编，专为我校申请硕士、博士学位人员撰写打印论文时使用。本格式自发布日起实行。

2\. 学位论文主要部分

学位论文主要部分由前头部分、主体部分和结尾部分（只限必要时采用）组成。

2.1 前头部分

（1）封面

（2）扉页------题名页（中、英两种）

（4）声明（独创性声明）

（3）摘要（中、英两种文字）

（5）目录

（6）插图和附表清单（只限必要时）

（7）缩略字、缩写词、符号、单位表（只限必要时）

（8）名词术语注释表（只限必要时）

2.2 主体部分

（1）绪论（前言、引言、绪言）

（2）正文

（3）讨论、结论和建议

2.3 结尾部分（只限必要时采用）

（1）参考文献

（2）致谢

（3）攻读学位期间发表的论著、获奖情况及发明专利等项。

（4）作者从事科学研究和学习经历的简历

（5）可供参考的文献题录（只限必要时采用）

（6）索引（只限必要时采用）

3\. 版式

纸张大小：纸的尺寸为标准A4复印纸（210mm×297mm）。

版芯（打印尺寸）：160mm×247mm（不包括页眉行、页码行）。

正文字体字号：小4号宋体，全文统一。

每页30～35行，每行35～38字。

装订：双面打印印刷，沿长边装订。

页码：页码用阿拉伯数字连续编页，字号与正文字体相同，页底居中，数字两侧用圆点或一字横线修饰，如·3·或－3－。

页眉：自摘要页起加页眉，眉体可用单线或双线（二等线、文武线），页眉说明5号楷体，左端"东北大学硕士、博士学位论文"，右端"章号章题"。

封面：东北大学研究生（博士或硕士）学位论文标准封面（双A4）。

4\. 体例

4.1 标题

论文正文按章、条、款、项分级，在不同级的章、条、款、项阿拉伯数字编号之间用点"."（半角实心下圆点）相隔，最末级编号之后不加点。排版格式见表4.1。

此分级编号法只分至第四级。再分可用（1）、（2）......；（a）、（b）......等。

表4.1 标题排版格式

  --------------- -------------- -------------- --------------------------
  标题            字号字体       格式           举例

  第一级(章)      二号黑体       居中，占3行    第1章 XXX

  第二级(条)      三号黑体       居左，占2行    1.1 XXXXXX

  第三级(款)      四号黑体       居左，占2行    1.1.1 XXXXXX

  第四级(项)      小四号黑体     居左，占1行    1.1.1.1 XXXXXX
  --------------- -------------- -------------- --------------------------

摘要、目录、参考文献、致谢、个人简历等标题作为第一级标题排版。

4.2 正文

汉字字体字号：正文字体小4号宋体。

外文、数字字号与同行汉字字号相同，字体用WORD系统中的Time New
Roman体或相近字体。

4.2.1 插图

插图包括图解、示意图、构造图、曲线图、框图、流程图、布置图、地图、照片、图版等。插图注明项有图号、图题、图例。图号编码用章序号。如"图2.1"表示第2章第1图。图号与图题文字间置一字空格，置于图的正下方，图题用5号字，字体可用宋体，须全文统一。图中标注符号文字字号不大于图题的字号。

4.2.2 表

表的一般格式是数据依序竖排，内容和项目由左至右横读，通版排版。表号也用章序号编码，如：表2.1是第2章中的第1表。表应有表题，与表号之间空1～2字，置于表的上方居中，用5号宋体，须全文统一。表中的内容和项目字号不大于图题的字号。

4.2.3 公式

公式包括数学、物理和化学公式。正文中引用的公式、算式或方程式等可以按章序号用阿拉伯数字编号（式号），如：式（2.1）表示第2章第1式，公式一般单行居中排版与上下文分开，式号与公式同行居右排版。

4.3 附录

附录中的图、表、公式、参考文献等另行编排序号，与正文分开，也一律用阿拉伯数字编号，但在数码前冠以附录序码。例如：图A.1，式（B.3）等。

4.4 计量单位

学位论文一律采用1984年2月27日国务院发布的《中华人民共和国法定计量单位》，并遵照《中华人民共和国法定计量单位使用方法》执行。论文中命名用各种量、单位和符号，必须遵循国家标准GB3100-82，GB3101-82，GB3102/1-13-82等的规定。

单位名称和符号的书写方式，可以采用国际通用符号，也可以用中文名称，但统一采用一种，不要混用。

4.5 参考文献

参考文献采用顺序号编号体系。

专著格式：

\[序号\] 编著者. 书名\[M\]. 出版地：出版社，年代，起止页码.

期刊论文格式：

\[序号\] 作者. 论文名称\[J\]. 期刊名称，年度，卷（期）：起止页码.

学位论文格式：

\[序号\] 作者. 学位论文名称\[D\]. 发表地：学位授予单位，年度.

**参考文献举例：**

\[1\] 张毅. 铸造工艺CAD及其应用\[M\]. 北京：机械工业出版社，1994，14-15.

\[2\] Huang S C, Huang Y M, Shieh S M. Vibration and stability of a
rotating shaft containing a transerse crack \[J\]. J Sound and
Vibration, 1993, 162(3): 387-401.

\[3\] 周丽. 机械式挖掘机工作装置的优化与仿真\[D\]. 沈阳：东北大学，2000.

**表格举例：**

表2.1 设计钢的化学成分（质量百分比，%）

Table 2.1 Chemical compositions of designed steels (mass%)

  -------------- --------- ----------- ----------- ------------ -----------
                 C         Mn          Si          N            V

  合金I          **0.1**   1.0         \<0.2       **0.01**     **0.1**

  合金II         **0.1**   1.0         \<0.2       **0.02**     **0.1**

  合金III        **0.1**   1.0         \<0.2       **0.01**     **0.05**
  -------------- --------- ----------- ----------- ------------ -----------

**公式举例：**

![](media/image1.wmf) (6.1)

![](media/image2.wmf) (6.2)

**图举例：**

![](media/image3.wmf)

图3.3硬度和表面粗糙度随CSR形变量的变化

Fig. 3.3 Variation of hardness and surface roughness

of the samples with CSR reduction

分类号 密级

UDC

学 位 论 文

基于GA的ABC支持型QoS切换管理机制

的研究与仿真实现

  ---------------- -------------------------------- ------------------ -----------------------
  作者姓名：       王某                                                

  作者学号：                                                           

  指导教师：       王某某 教授                                         

                   东北大学信息科学与工程学院                          

  申请学位级别：   硕士                             学科类别：         

  学科专业名称：   计算机软件与理论                                    

  论文提交日期：   2008年1月                        论文答辩日期：     2008年2月

  学位授予日期：   2008年3月                        答辩委员会主席：   高某某

  评阅人：         蒋某某、刘某某、张某某、黄某某                      
  ---------------- -------------------------------- ------------------ -----------------------

东 北 大 学

2008年1月

##### A Thesis in Computer Software and Theory

**Research and Simulated Implementation of QoS Handoff Mechanisms Based
on GA with ABC Supported**

By Wang Mou

Supervisor: Professor Wang Moumou

**Northeastern University**

**January 2008**

独创性声明

本人声明，所呈交的学位论文是在导师的指导下完成的。论文中取得的研究成果除加以标注和致谢的地方外，不包含其他人己经发表或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。与我一同工作的同志对本研究所做的任何贡献均己在论文中作了明确的说明并表示谢意。

学位论文作者签名：

日 期：

学位论文版权使用授权书

本学位论文作者和指导教师完全了解东北大学有关保留、使用学位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学位论文的全部或部分内容编入有关数据库进行检索、交流。

作者和导师同意网上交流的时间为作者获得学位后：

半年 □ 一年□ 一年半□ 两年□

学位论文作者签名： 导师签名：

签字日期： 签字日期：

摘 要

网络与通信技术的迅速发展给人们的工作和生活方式带来了巨大的改变。在更大、更快、更安全、更方便且融合有线传输和无线传输于一体的下一代互联网NGI（Next
Generation
Internet）中，随时随地享受高质量网络服务已经成为人们的迫切要求，这在客观上要求NGI提供可靠的QoS（Quality
of
Service）保证，并且可以在通信开始和运行期间为用户提供总最佳连接ABC（Always
Best Connected），进而使用户总是以最优方式接入网络并享受服务。

.................................................................................................................................................................................................................................................................................。

..........................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................................。

关键词：下一代互联网；总最佳连接；切换；服务质量；效用；遗传算法

### Abstract

The rapid development of Internet and communication technology has
brought tremendous changes to the way people work and live. In a larger,
faster, more secure, more conventient NGI(Next Generation Internet) that
integrates wired and wireless access technologies, enjoying high-quality
network service anywhere and at any time has become people's necessary
requirement, which indicates that NGI must provide QoS(Quality of
Service) guarantee and support ABC(Always Best Connected). Therefore,
people can choose the "best" way to access to network and enjoy the
service.

..................................................................................................................................................................................................................................................................................

.....................................................................................................................................................................................................................................................................................................................................

**Key words:** next generation internet; always best connected; handoff;
quality of service; utility; genetic algorithm

目 录

声明············································································································Ⅰ

中文摘要···································································································Ⅱ

Abstract······································································································Ⅳ

第1章 绪
论·····························································································1

1.1
下一代互联网NGI···············································································1

1.1.1
NGI主要特点············································································1

1.1.2
NGI关键技术············································································4

1.2
总最佳连接ABC················································································7

1.3
QoS··································································································13

第2章
ABC支持型QoS切换机制特点·············································14

2.1
不确定性信息···················································································14

2.1.1
信息的模糊性·········································································14

2.1.2
信息的随机性·········································································21

2.2
公平性决策······················································································25

2.2.1
博弈论····················································································26

2.2.2
Nash均衡················································································27

.................................................................................

.................................................................................

第1章 绪 论

1.1 NGI

下一代互联网NGI（Next Generation
Internet）^\[1\]^始于1996年美国克林顿政府的"NGI计划"。1996年10月，美国政府宣布启动下一代互联网NGI研究计划，并建立了相应的告诉网络试验床。1998年，先进Internet开发大学组织成立，开始Internet2研究计划，并建立了高速网络试验床Abilene。1998年亚太地区先进网络组织APAN（Asia
Pacific Advanced
Network）成立，建立了APAN主干网。2002年，各国发起全球告诉互连网GTRN（Global
Terabit Research Network）计划，积极推动NGI技术的研究和开发。

........................................................................................................................。

1.1.1 NGI主要特点

NGI的主要特点有^\[2-4\]^：

（1）混合无线接入技术：..........................................。

................................................................................................。
