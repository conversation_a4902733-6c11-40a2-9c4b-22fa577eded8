 
 
分类号                     密级            
UDC                     
 
学  位  论  文 
 
L 公司运维管理系统项目风险管理研究 
 
 
 
 
作者姓名： 贾长林 
作者学号： 2080394 
指导教师： 卢震 副教授 
 
东北大学工商管理学院 
申请学位级别： 硕士 
学科类别： 管理学 
学科专业名称： 工商管理（专业学位） 
论文提交日期： 2023 年5 月23 日 论文答辩日期： 2023 年6 月3 日 
学位授予日期： 2023 年7 月 
答辩委员会主席： 郭伏 教授 
评
阅
人
： 黄训江 副教授  秦丽娜 教授 
 
 
 
 
 
东  北  大  学 
2023 年6 月 
 
 
 
A Thesis for the Degree of Master of Business Administration 
 
 
 
Research on Project Risk Management of 
Operation and Maintenance Management 
System of L Company 
 
 
 
By Jia Changlin 
 
 
 
Supervisor: Associate professor Lu Zhen 
  
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
Northeastern University 
June 2023 
I 
 
独创性声明 
本人声明，所呈交的学位论文是在导师的指导下完成的。论文中
取得的研究成果除加以标注和致谢的地方外，不包含其他人己经发表
或撰写过的研究成果，也不包括本人为获得其他学位而使用过的材料。
与我一同工作的同志对本研究所做的任何贡献均己在论文中作了明确
的说明并表示谢意。 
                                         学位论文作者签名：
 
日    期：2023.6.30 
学位论文版权使用授权书 
本学位论文作者和指导教师完全了解东北大学有关保留、使用学
位论文的规定：即学校有权保留并向国家有关部门或机构送交论文的
复印件和磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学
位论文的全部或部分内容编入有关数据库进行检索、交流。 
 
 
作者和导师同意网上交流的时间为作者获得学位后： 
 
半年 □    一年□    一年半□       两年√ 
 
学位论文作者签名：  
          导师签名：
 
签字日期：2023.6.30                      签字日期：2023.6.30 
东北大学硕士学位论文                                                         摘要 
II 
 
摘  要 
随着我国信息技术的飞速发展，信息系统在企业中得到广泛而深入的应用。
企业信息系统项目的建设，为传统企业的发展提供动力，使其能够更快更好的转
型与升级。一般的，信息系统项目较复杂，且技术含量较高。因此，风险管理在
信息系统项目建设中，尤为重要。对企业信息系统项目风险管理进行研究，具有
重要意义。 
本文以L 公司运维管理系统项目为研究对象，对该项目风险管理进行专题研
究。首先，介绍了项目背景、研究目的和意义、研究内容及方法，项目风险相关
理论。然后，介绍了L 公司运维管理系统项目的具体情况。在此基础上，对L 公
司运维管理系统项目风险管理进行研究。 
首先，采用文献研究法、头脑风暴法和定性分析法，对项目风险进行识别。
主要包括需求风险、技术风险、管理风险、质量风险、安全风险，共5 类16 项风
险因素。接下来，采用层次分析法对风险识别结果进行分析，得到各风险因素的
权重。采用模糊综合评价法，得到项目整体风险模糊评价为较高风险。采用帕累
托分析法，根据各风险因素权重，分析出主要风险、次要风险、一般风险。其中，
项目主要风险，包括需求调研风险、沟通风险、进度风险、技术选择风险、人员
能力风险、需求变更风险、质量风险、系统集成风险，应重点关注和警惕。最后，
根据帕累托分析法对风险因素划分的结果，结合项目的实际情况，制定完善的风
险应对策略和保障措施。该研究将有效的降低项目风险，为L 公司运维管理系统
项目建设提供保障。同时，也将为相关企业的信息系统项目风险管理，提供参考
和借鉴。 
 
关键词：信息系统；风险管理；风险分析；风险应对 
东北大学硕士学位论文                                                      Abstract 
III 
 
Abstract 
With the rapid development of information technology in China, information 
systems have been widely and deeply applied in enterprises. The construction of 
enterprise information system project provides power for the development of 
traditional enterprises and enables them to transform and upgrade faster and better. In 
general, the information system project is more complex and has a high technical 
content. Therefore, risk management is particularly important in the construction of 
information system projects. It is of great significance to study the risk management of 
enterprise information system projects. 
This thesis takes the operation and maintenance management system project of L 
Company as the research object, and conducts a special study on the project risk 
management. Firstly, it introduces the project background, research purpose and 
significance, research content and methods, and project risk related theories. Then, it 
introduces the specific situation of the operation and maintenance management system 
project of L Company. On this basis, the project risk management of L Company's 
operation and maintenance management system is studied. 
Firstly, the project risk is identified by literature research, brainstorming and 
qualitative analysis. It mainly includes demand risk, technical risk, management risk, 
quality risk and safety risk, with a total of 5 categories and 16 risk factors. Next, the 
analytic hierarchy process is used to analyze the risk identification results and obtain 
the weight of each risk factor. By using the fuzzy comprehensive evaluation method, 
the overall risk of the project is fuzzy evaluated as higher risk. Pareto analysis method 
is used to analyze the main risk, secondary risk and general risk according to the 
weight of each risk factor. Among them, the main risks of the project, including 
demand research risk, communication risk, schedule risk, technology selection risk, 
personnel ability risk, demand change risk, quality risk, and system integration risk, 
should be focused and vigilant. Finally, according to the results of Pareto analysis on 
the division of risk factors, combined with the actual situation of the project, develop a 
sound risk response strategy and safeguard measures. This research will effectively 
东北大学硕士学位论文                                                      Abstract 
IV 
 
reduce the project risk and provide guarantee for the construction of L Company's 
operation and maintenance management system project. At the same time, it will also 
provide reference for the risk management of information system projects of relevant 
enterprises. 
 
Key words: Information system; Risk management; Risk analysis; Risk response
东北大学硕士学位论文                                                         目录 
 
 
目  录 
独创性声明 .................................................................................................................................. I 
中文摘要 ....................................................................................................................................... II 
Abstract ........................................................................................................................................ III 
第1 章 绪 论 ........................................................................................................................... 1 
1.1 选题背景 .......................................................................................................................... 1 
1.2 研究目的及意义 ............................................................................................................ 3 
1.2.1 研究目的 .............................................................................................................. 3 
1.2.2 研究意义 .............................................................................................................. 3 
1.3 国内外研究现状 ............................................................................................................ 4 
1.3.1 国内研究现状 .................................................................................................... 4 
1.3.2 国外研究现状 .................................................................................................... 5 
1.4 研究内容及研究方法 .................................................................................................... 6 
1.4.1 研究内容 .............................................................................................................. 6 
1.4.2 研究方法 .............................................................................................................. 7 
第2 章 项目风险管理相关理论 ................................................................................. 9 
2.1 项目风险管理概念 ........................................................................................................ 9 
2.2 项目风险管理过程 ...................................................................................................... 10 
2.2.1 风险管理规划 .................................................................................................... 10 
2.2.2 风险识别 ............................................................................................................. 10 
2.2.3 风险分析 ............................................................................................................. 11 
2.2.4 风险应对 ............................................................................................................. 15 
2.2.5 风险监控 ............................................................................................................. 16 
2.3 信息系统项目概念 ...................................................................................................... 16 
2.4 信息系统项目风险 ...................................................................................................... 18 
第3 章 L 公司运维管理系统项目介绍及特点分析 .................................. 21 
东北大学硕士学位论文                                                         目录 
 
 
3.1 项目介绍 ........................................................................................................................ 21 
3.1.1 项目建设背景 .................................................................................................. 21 
3.1.2 项目建设目标 .................................................................................................. 21 
3.1.3 项目主要功能 .................................................................................................. 22 
3.1.4 项目组织机构 .................................................................................................. 25 
3.1.5 项目进度安排 .................................................................................................. 27 
3.2 项目特点分析 ............................................................................................................... 29 
第4 章 L 公司运维管理系统项目风险识别 ................................................... 32 
4.1 风险识别思路 ............................................................................................................... 32 
4.1.1 风险识别原则 .................................................................................................. 32 
4.1.2 风险识别方法 .................................................................................................. 33 
4.1.3 风险识别步骤 .................................................................................................. 33 
4.2 风险识别过程 ............................................................................................................... 34 
4.3 风险识别结果 ............................................................................................................... 40 
第5 章 L 公司运维管理系统项目风险评价 ................................................... 44 
5.1 评价指标体系 ............................................................................................................... 44 
5.2 基于层次分析法（AHP）评价项目风险权重 .................................................... 44 
5.2.1 建立结构模型 .................................................................................................. 44 
5.2.2 构建判断矩阵 .................................................................................................. 45 
5.2.3 评估结果权重排序 ......................................................................................... 49 
5.3 基于模糊综合评价法评估风险等级 ...................................................................... 50 
5.3.1 指标等级划分 .................................................................................................. 50 
5.3.2 模糊综合评价 .................................................................................................. 50 
5.3.3 项目整体风险评价 ......................................................................................... 53 
5.4 评价结果分析 ............................................................................................................... 53 
第6 章 L 公司运维管理系统项目风险应对策略及保障措施 ........... 56 
6.1 项目风险应对策略 ...................................................................................................... 56 
6.1.1 主要风险应对策略 ......................................................................................... 56 
东北大学硕士学位论文                                                         目录 
 
 
6.1.2 次要风险应对策略 ......................................................................................... 63 
6.1.3 一般风险应对策略 ......................................................................................... 65 
6.2 项目风险管理保障措施 ............................................................................................. 67 
第7 章 结论与展望 ........................................................................................................... 71 
7.1 结论 ................................................................................................................................. 71 
7.2 展望 ................................................................................................................................. 72 
参考文献 ..................................................................................................................................... 73 
附录A 风险因素赋值打分表 ..................................................................................... 75 
附录B 模糊评价风险等级打分表 ......................................................................... 77 
致谢 ................................................................................................................................................ 78 
东北大学硕士学位论文                                                  第1 章 绪论 
-1- 
 
第1章  绪 论 
1.1 选题背景 
进入21 世纪，随着社会经济的不断发展，科学技术的突飞猛进，信息技术得
到了前所未有的发展。信息技术具有发展速度快、影响力大、渗透力强等特点，
已成为推动经济发展和社会进步的关键因素。信息技术的飞速发展，深刻地改变
了社会经济结构、生产力、生产方式以及人们的生活方式。 
A 公司是一家中央大型国有独资企业，是L 公司的总公司。党的十八大以来，
新时期的A 公司提出建设世界一流邮政企业的愿景。在经营发展、信息化引领、
人才培养方面，提出了“一体两翼”、科技兴邮和人才强邮三大战略。其中，信息
化引领的科技兴邮战略，体现了科学技术是第一生产力，邮政的发展离不开信息
化。未来企业的发展，一定是科技赋能、IT 赋能、数据赋能。A 公司未来的发展，
应牢牢把握信息化引领，这一科技兴邮战略主线。不断推进企业信息化建设和数
字化建设，使信息技术与企业业务发展深度融合。围绕着拓展业务范围，提升服
务质量和效率，实现降本增效，以科学技术助力企业发展。 
A 公司先后经历了邮电分营，政企分离，企业内部改革重组，确立了邮务、
速递物流、金融三大业务板块的经营格局。上个世纪末，A 公司自邮电分营以后，
就开始了邮政企业的信息化建设。A 公司信息化是一个逐步发展的过程，基本上
分为大规模基础网络平台建设、应用软件建设、逻辑和系统整合建设、数字化建
设四个阶段。 
（1）基础网络平台建设阶段，建成了遍布城乡，通达全国的超大型邮政信息
网，使各省、市、区县、邮政网点通过信息网络，连接起来。 
（2）应用软件建设阶段，开发包括邮务类、金融、速递物流、电子商务、生
产经营管理五大类应用系统，提升邮政的服务质量和效率，开拓了新的业务领域，
增强了企业的核心竞争力。 
（3）逻辑和系统整合建设阶段，主要是数据上收到集团，进行逻辑大集中，
建立大数据中心，整合公司各业务系统，打通信息孤岛和数据堡垒。整合公司各
板块数据资源，建立统一的平台系统，为企业数字化转型发展，打下坚实的基础。 
东北大学硕士学位论文                                                  第1 章 绪论 
-2- 
 
（4）邮政数字化建设阶段，是一个正在进行的过程，是一项以新技术为驱动
的战略性举措。通过将互联网、大数据、物联网、云计算、人工智能等现代信息
技术与邮政服务深度融合，提升邮政服务的效率、质量和范围，推动企业转型升
级和创新发展，促进高质量经济发展，最终将建设成为世界一流邮政企业。 
L 公司是A 公司下属的分支机构。L 公司信息化，紧跟集团公司的步伐。在
集团公司领导下，已建成覆盖全省的信息化网络。在此基础上实现省内各业务、
生产各环节的信息化全覆盖。并在省内生产经营，运营管理，业务拓展等方面，
在集团统版系统的基础上，积极创新，开发了诸多信息化系统，进一步强化科技
赋能，信息化引领。 
在代理金融业务方面，自主开发了金融客户管理系统，强化“数字+精准营
销”增强金融专业客户营销能力。在运营管理方面，开发了网点资源管理系统，
强化“数字+精细管理”，推动精细化管理，实现对基层经营单元的科学核算与考
核。在生产作业方面，建设自动化分拣辅助系统，强化“数字+智能化生产”，提
高分拣效率、降低错件率，促进流程优化，推动降本增效。L 公司的信息化建设，
有力地推动了企业管理模式的改革，提高了企业生产经营能力，提升了企业综合
服务水平，增强了企业核心竞争力。当然，企业在取得成绩的同时，我们也清楚
的看到了，随着公司业务的不断发展，邮政信息网规模不断扩大，各业务系统不
断增加，这对L 公司信息技术中心IT 服务质量和效率提出了更高的要求。 
为提高L 公司运维管理服务质量和效率，L 公司信息技术中心计划于2023
年5 月，建设L 公司运维管理系统。该项目的建设，旨在解决省分公司信息技术
中心对各市信息技术工作的指导，省分公司信息中心对全省信息网运维的服务支
撑，各市分公司信息技术中心对各业务部门以及所负责的自有网点的服务支撑工
作。解决网点故障上报靠电话，处理过程缺少电子记录，工作缺少量化统计的现
状；解决增加台席等计划性服务缺少直接联动机制，工作准备缺少提前性的问题；
增加了基层网点对服务处理结果的反馈，以提高运维服务满意度。L 公司运维管
理系统是一个全省性的重要项目，该项目的建设将规范全省运维管理流程，提升
运维服务质量和水平，节约运维通讯成本，实现降本增效。 
L 公司信息技术中心在过去的信息系统项目建设过程中，很少注重项目的风
险管理，在项目建设中，不可避免的出现各种风险问题，影响项目的范围、成本、
质量、进度，以及后期的运行维护。本文以正在建设的L 公司运维管理系统为研
东北大学硕士学位论文                                                  第1 章 绪论 
-3- 
 
究对象，通过风险管理的相关理论和方法，科学识别和分析L 公司运维管理系统
项目建设中所面临的风险，并在此基础上建立该项目风险评估体系结构。通过对
L 公司运维管理系统项目风险的分析，提出建设性的风险应对策略和保障措施，
可有效地保证L 公司运维管理系统项目的建设。该课题的研究，可使L 公司信息
技术中心加强信息系统项目的风险管理，并为以后相关信息系统项目的建设，提
供参考和借鉴。 
1.2 研究目的及意义 
1.2.1 研究目的 
L 公司运维管理系统项目风险管理研究，这一课题研究的目的主要有以下三
点。 
首先，该课题研究是为了保证L 公司运维管理系统项目建设的顺利完成； 
其次，该课题研究是为了将项目风险管理理论和方法，应用于L 公司信息系
统项目建设的一次实践研究； 
最后，该课题研究是为了将本次的研究成果，积累到L 公司信息技术中心的
企业知识库中去，填补L 公司信息技术中心在信息系统项目建设过程中，项目风
险管理应用的空白。同时，也将为L 公司信息技术中心在以后的信息系统项目建
设中，对加强项目风险管理方面，提供有效的参考。 
1.2.2 研究意义 
L 公司运维管理系统项目是L 公司重点建设的大型科技项目，是L 公司信息
化重点组成部分，更是科技兴邮的重要实践。目前，L 公司信息技术中心在省级
层面，于2017 年建设了新一代机房，并建设了全省运维监控平台，采购了新的设
备系统，可实现重要设备的自动化监控。但是，在全省信息技术部门对营业网点
及重要部门的技术支撑方面，仍缺少信息系统支持。 
L 公司运维管理系统项目的建设，需要选择合适的项目技术框架，以及利用
信息、网络、安全等相关技术来进行系统建设。项目在需求、范围、成本、质量、
进度、人力资源、沟通等方面的管理较为复杂。在项目干系人方面，所涉及的干
系人较多。在项目需求方面，前期项目需求不太详细，还需要深入调研。在项目
东北大学硕士学位论文                                                  第1 章 绪论 
-4- 
 
范围方面，项目范围还未确定，且随时存在变更可能。在项目成本和质量方面，
L 公司领导层要求节约成本，但对项目质量提出较高要求。在项目进度方面，L
公司信息技术中心领导，要求在12 月1 日正式上线，项目工期较紧且对阶段性成
果也有绩效考核要求。在系统培训方面，由于使用对象包括省级和市级信息技术
管理人员、市级业务人员、网点业务人员、11185 服务人员，角色较多且所使用
功能各异，在系统培训上存在一定难度。总结以上各种不确定性，可得出L 公司
运维管理系统项目的建设较为复杂，且存在一定的风险性。为了确保L 公司运维
管理系统项目建设的顺利进行，需要对该项目中存在的潜在风险进行系统的管理。
因此，利用项目风险管理研究方法，对L 公司运维管理系统项目风险管理进行专
题研究，对该项目的建设十分重要。 
L 公司运维管理系统项目风险管理研究，旨在利用较为成熟的项目风险管理
理论和方法，识别、分析该项目建设过程中所面临的风险。通过对项目风险的分
析，提出风险的应对策略和保障措施，保证该信息系统项目建设的成功。该课题
研究的应用，也将为L 公司信息技术中心在信息系统项目建设中，对“如何加强
项目风险管理，高质量完成项目建设”方面，提供范例。为邮政行业信息技术管
理工作，在信息系统项目风险管理方面的创新和发展，提供一些参考和借鉴。 
1.3 国内外研究现状 
1.3.1 国内研究现状 
随着科技的不断进步，信息化已成为当今人类发展的大趋势。人类文明已经
从农耕时代，工业时代，进入到信息化时代。21 世纪的我们，正处于科技迅速发
展的信息化时代。人们的日常工作和生活，公司的运营，国家的发展，经济的全
球化，这些都与信息化息息相关。 
在信息化时代，企业的发展离不开各种信息系统的支持。但在企业信息系统
项目建设中，存在较多风险因素。当前，在信息系统项目管理风险这一领域，国
内许多学者和专家已进行了深入的研究。在借鉴国内外信息技术行业发展特点的
基础上，提出了许多建设性的观点和方案。 
高清阔、张永淼（2020）认为项目风险管理主要包括项目管理规划、风险识
别、风险分析、风险应对、风险监测和控制五个方面[37]。周义峰（2012）认为项
东北大学硕士学位论文                                                  第1 章 绪论 
-5- 
 
目风险管理贯穿整个项目全过程[36]。在期刊《浅谈信息系统项目的风险管理》中，
李文彬（2017）从风险管理计划、风险识别、风险分析、风险应对计划和风险监
控5 个方面，阐述了信息系统项目的风险管理理论及常用方法[34]。在期刊《高度
综合化航空电子信息系统项目风险管理研究及思考》中，周榜兰（2020）针对高
度综合化航空电子信息系统项目的实际研制情况，构建项目研制风险管理流程，
重点研究项目的风险规划、风险识别、风险分析 ( 包括定性和定量分析) 、风险
应对及风险监控的整个风险管理过程，提出持续提升项目研制风险管理能力的相
关建议[29]。在论文《A 企业信息系统建设项目风险管理研究》中，崔洪昊（2021）
利用德尔菲法进行项目的风险识别。在项目的风险评价上，首先构建风险指标评
价体系，利用层次分析法和模糊综合评价法，对风险指标进行评价。最后，根据
评价结果，对项目风险提出对策和保障措施[22]。在论文《智慧政务门户网站项目
风险管理研究—以C 经济开发区为例》中，姜卫卫（2022）利用头脑风暴法对项
目风险进行识别[27]。李烜（2019）在《评分软件项目风险管理研究》中，利用检
查表法结合德尔菲法，对项目风险进行识别[26]。王峰（2018）在《K 公司NMS
软件项目风险管理研究》，用德尔菲法、头脑风暴法、检查表法和WBS-RBS 风险
矩阵法对项目风险进行识别[24]。龙璨（2019）在《X 市住房公积金信息系统升级
项目风险管理研究》中，依据结构化思维MECE 分析方法和2/8 原则法为原则，
利用专家打分法对项目风险进行识别[25]。李鹏（2016）在论文《A 公司软件项目
风险管理研究》中，利用WBS-RBS 风险矩阵方法，对项目各环节风险进行分析，
用头脑风暴法对风险的概率和影响进行打分，最终形成风险概率影响矩阵[23]。 
总结以上文献材料，在项目风险的识别上，采用头脑风暴法、德尔菲法、检
查表法、专家打分法、WBS-RBS 风险矩阵法等方法。在项目风险的分析上，常
见的做法是利用层次分析法和模糊综合评价法进行评估。然后在基于项目风险分
析结果的基础上，结合项目实际情况，对项目风险管理提出建设性的意见和建议。 
1.3.2 国外研究现状 
美国项目管理学会（PMI）在其制定的项目管理知识体系（PMBOK）2017
年第六版中，划分了项目管理的十大知识领域，其中就有项目风险管理的内容。
其定义的项目风险管理过程包括规划风险管理、识别风险、实施定性风险分析、
实施定量风险分析、规划风险应对、实施风险应对、监督风险，共七个子过程。
项目风险管理在现代项目管理中，占有不可或缺的地位。国外学者对项目风险管
东北大学硕士学位论文                                                  第1 章 绪论 
-6- 
 
理进行了深入的研究，并在各领域的项目管理中进行应用。 
Taylan 等（2014 年）使用新的分析工具来评估建设项目及其在不完整和不确
定情况下的整体风险。通过综合混合方法评估标准；应用相对重要性指数（RII）
方法，从获得的数据风险来确定项目优先级；通过模糊层次分析法和模糊TOPSIS
方法对建设项目进行分类。针对时间、成本、质量、安全和环境可持续性，这五
个主要标准对30 个建设项目进行了研究[15]。Aragones-Beltran 等（2014 年）用一
种基于层次分析法（AHP）/ANP（网络分析法）的多准则决策方法，用于太阳能
热电厂投资项目的选择。分析了接受或拒绝投资建议应考虑的标准，以及用于优
先考虑某些项目的风险[16]。Aven（2015）回顾了风险评估和风险管理的最新进展，
提出进行风险管理是必要的，应该予以鼓励[17]。Lin 等（2021 年）利用文献计量
分析法，挖掘项目建设过程中潜在的风险并加以总结，为风险预警提供重要指标。
用模糊集理论和机器学习的方法来进行风险的评估。并以广州地铁站为例，展示
其在土木工程项目中风险评估的应用[18]。Yazdi 等（2020 年）通过将 DEMATEL
（决策试验和评估实验室）方法与最佳最差方法 (BWM) 和贝叶斯网络 (BN) 相
结合来实现对风险因素的识别和安全管理的决策[19]。Huang 等（2017 年）应用
语言分布评估来代表FMEA 的风险评估信息，并采用改进的 TODIM（葡萄牙语
中交互式和多标准决策的首字母缩写词）方法来确定风险故障模式的优先级[20]。
Zhao 等（2021 年）针对 MAGDM （多属性群决策理论）问题提出了毕达哥拉斯
模糊 CPT-TODIM (PF-CPT-TODIM) 方法。同时，为获取权重的合理性信息，在 
PFS（勾股模糊集）下使用熵权法对属性进行分类，并将所讨论的方法应用于风
险科技项目评估中[21]。 
1.4 研究内容及研究方法 
1.4.1 研究内容 
第一章，介绍了在科技迅速发展的今天，L 公司的总公司即A 公司，所在行
业及信息化的发展历程，L 公司信息化发展情况，L 公司信息技术中心信息系统
项目建设情况的背景下，进行本课题研究的重要目的和意义；介绍了国内外在项
目风险管理方面的研究现状，并着重说明了研究的主要内容及所使用的研究方法。 
第二章，介绍该课题研究的理论基础，包括项目风险管理概念、项目风险管
理过程、信息系统项目概念、信息系统项目风险。在项目风险管理过程中，具体
东北大学硕士学位论文                                                  第1 章 绪论 
-7- 
 
介绍了风险管理规划、风险识别、风险分析、风险应对、风险监控。在信息系统
项目风险中，对常见的信息系统项目风险进行介绍。 
第三章，以L 公司运维管理系统项目为例，介绍该项目的建设背景、建设目
标、主要功能、组织机构、进度安排。然后，从信息系统项目特点的普遍性和特
殊性两个方面，对L 公司运维管理系统项目特点进行分析。最后，得出对L 公司
运维管理系统项目进行风险管理的必要性，并说明下一步研究内容。 
第四章，介绍了本研究中，风险识别的原则、方法和步骤。然后，详细介绍
该项目风险识别的过程。最后，说明该项目风险识别的最终结果，为之后的项目
风险分析打下坚实基础。 
第五章，首先根据第四章风险识别的结果，构建风险评价指标体系。然后，
采用层次分析法（AHP）评价项目风险权重，具体分为建立结构模型、构建判断
矩阵，评估结果权重排序。采用模糊综合评价法评估风险等级，具体包括指标等
级划分、模糊综合评价、项目整体风险评价。最后，基于层次分析法和模糊综合
评价法，总结风险评估结果，并运用帕累托分析法，对风险因素进行分类，以便
为下一步的风险应对策略和保障措施的制定提供依据。 
第六章，是在前几章研究和分析的基础上，针对L 公司运维管理系统项目的
风险评估结果，对主要、次要和一般的风险因素提出了富有建设性的风险应对策
略，并就项目风险管理的保障措施提出了建议和意见。以保证L 公司运维管理系
统项目建设成功，达成预期目标。 
第七章，总结全文，得出结论，并对以后在信息系统项目风险管理方面的研
究，提出一些希望。 
1.4.2 研究方法 
针对L 公司运维管理系统项目风险管理研究，本文主要通过文献研究法，了
解相关信息系统项目风险管理研究情况。采用调查法，收集项目信息。采用定性
分析法，对风险识别信息进行归纳、分析。采用定量研究法，对风险分析中的层
次分析法和模糊综合评价进行量化打分。本研究所采用的方法，具体描述如下： 
（1）文献研究法 
文献研究法主要指搜集、鉴别、整理相关文献，在此基础上，通过对文献的
研究，科学认识研究对象的一种经典的研究方法。具体做法，充分利用图书馆、
东北大学硕士学位论文                                                  第1 章 绪论 
-8- 
 
单位内部相关材料、中文数据库（知网、维普、超星）等资源获取资料。分析L
公司运维管理系统项目的风险管理情况，并结合项目风险管理的发展状况、理论
知识和好的经验案例，提出了科学的风险应对策略和风险保障措施，以确保该项
目能够顺利完成建设。 
（2）调查法 
调查法是对项目相关人员，就一些限定性的问题做出回答，发表调查者的想
法。本研究中，在项目需求调研中，使用了访谈法；在风险识别中，使用了头脑
风暴法。较好地完成了各环节的信息收集工作，为下一步的分析、总结打下坚实
的基础。 
（3）定性研究法 
定性研究法是指根据事物的现象、属性以及矛盾变化，从事物内在本质规律
来分析的一种方法。定性研究方法要有充分的理论依据，抓住事物的主要特征，
同时忽略掉性质相同的事物在数量上的差异。本研究中，在L 公司运维管理系统
项目风险识别过程中，应用了定性研究法，将文献研究和头脑风暴中识别出的项
目风险进行分析，并构建项目风险指标体系。 
（4）定量研究法 
定量研究法是对事物的量方面进行的分析和研究，其中事物的量包括事物的
规模、程度、速度、成分等内容。在本研究种，该方法被应用于L 公司的运维管
理系统项目风险分析中，具体体现在使用了层次分析法和模糊综合评价来进行调
查打分，使得风险指标得到了量化评估。
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-9- 
第2章  项目风险管理相关理论 
2.1 项目风险管理概念 
风险是由某些不确定因素引起的偏离预定目标的不良后果的一种状况。风险
是不确定的，其可以用风险发生的概率和风险所产生的后果来表示，即：R=f(P,E)。
其中R 表示风险，P 表示不确定性事件发生的概率，E 表示不确定性事件发生后
所产生的后果。由上面公式可以得出，风险是由事件、事件发生的概率、事件产
生的后果，这三个要素构成的[28]。 
项目风险是指在项目的整个生命周期中，由于某些不确定的事件或条件可能
会导致项目偏离原定目标，从而对项目产生正面或负面的影响。这些风险是一种
不确定性的因素，一旦出现，就可能对项目造成影响，有时可能是机遇，但也可
能是威胁[28]。 
在项目整个生命周期内，项目的风险是无处不在的，项目的风险可能随时发
生。究其原因，只要引起项目风险的因素存在，且达到产生风险的客观条件，项
目风险就会产生。项目风险具有以下特征，即客观性、偶然性、规律性、多样性
[28]。 
（1）客观性 
无论人们采取多少方法和措施来预防风险、降低可能的发生概率，以及减轻
风险产生的影响，风险本质上是不可消除的。风险是自然界的一种不可控制的现
象，人们只能通过风险管理和控制来减小其对项目的影响。 
（2）偶然性 
项目的风险具有不确定性，任何一种风险都是由诸多因素和不确定的条件相
互作用而形成的，是一种偶然的随机事件。 
（3）规律性 
通过运用概率统计方法来针对大量风险事件数据的规律性进行统计和分析，
有助于描述风险以及进行风险管理。  
（4）多样性 
项目风险具有多样性，特别是大型项目，其工期长、规模大、范围广、逻辑
复杂、风险源和风险种类多，致使项目整个生命周期内，面临各种各样的风险。
如政治、经济、技术、成本、进度、质量等风险。 
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-10- 
项目风险管理是指项目建设单位在项目实施的全生命周期中，对可能出现的
各种风险进行系统的计划、识别、评估和应对，并通过对风险的监控来降低损失，
以达到整个项目建设目标的管理方法[28]。项目风险管理是需要一定的经费，综合
运用各领域知识，参考类似项目好的经验和做法，积极且正确的加以运用。项目
风险的目的是为了顺利地完成项目目标。 
2.2 项目风险管理过程 
项目风险管理过程是由风险管理规划、风险识别、风险分析、风险应对、风
险监控，五大过程组成。项目风险管理的五个过程是相互作用的，并且与项目管
理的其它过程也是相互影响的。项目风险的五个管理过程，每个阶段都需要项目
风险管理人员努力完成[28]。 
 
图2.1 项目风险管理过程 
Fig. 2.1 Project risk management process 
2.2.1 风险管理规划 
风险管理规划，是在项目计划阶段，制定的针对项目风险管理的总体规划。
制定项目风险管理规划需要参考项目管理计划、项目章程、经验教训等内容进行
综合分析，通过组织会议或邀请专家进行讨论，最终拟定项目风险管理计划。项
目风险管理计划是整个项目实施过程中的指导纲领。其内容主要包括风险管理的
人员、角色、职责，管理方法、成本、进度、基准，风险的类别、级别、说明，
风险事件报告的形式等。 
2.2.2 风险识别 
风险识别是项目风险管理的基础，是在收集项目数据的基础上，通过调查、
研究、分析和应用相关工具和方法，确定可能出现的各种风险，并进行适当的分
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-11- 
类和识别，最终整理成风险登记册[28]。项目风险的识别不是一次性的活动，它贯
穿于整个项目的始终，是连续的、动态的、综合性的、需要全员参与的一个过程。
项目风险识别过程一般分5 个步骤，具体如图2.2 所示。 
图2.2 风险识别过程 
Fig. 2.2 Risk identification process 
在项目风险识别的过程中，可以利用一些工具和方法来进行风险的识别。在
项目风险信息的收集上，通常利用德尔菲法、头脑风暴法、专家访谈法等方法。
本研究中，主要是采用了头脑风暴法来辅助项目风险的识别。 
头脑风暴法是由美国创造学家A·F·奥斯本于1939 年首次提出，1953 年正式
发表的一种方法。该方法是用于激发思维、集思广益，以达到解决问题的目的。
具体做法是首先组织团队成员召开头脑风暴会议，会议组织者引导并激发团队成
员，针对会议问题发表意见和想法。团队成员要充分发挥自己的头脑，尽可能多
地提出问题的解决方案。过程中，任何人不能打断其他人员发表意见，不能对其
进行评价。头脑风暴会议，需要尽可能多的收集团队成员发表的想法方案，注重
的是发表的数量，不是质量。在会议之后，需要专门人员，对会议的内容进行整
理和分析。经过多次的头脑风暴会议，可以识别出各种潜在的项目风险，为后续
的风险管理打下基础[27]。 
2.2.3 风险分析 
风险分析是一个包含风险估计和风险评价两个方面的过程。风险估计主要是
针对已识别的风险，对其发生可能性和对项目的影响进行估计。而项目风险评价
则对风险识别和风险评估的结果进行综合分析，以全面评估项目风险。在风险分
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-12- 
析中，常用的方法和工具有层次分析法、模糊综合评价法、帕累托分析法、风险
概率与影响矩阵。 
（1）层次分析法 
层次分析法（Analytic Hierarchy Process，AHP）是一种层次权重决策方法，
最早由美国匹兹堡大学萨蒂教授在20 世纪70 年代提出。层次分析法是将一个复
杂的多层次的决策目标视为一个整体系统，并将其分解为目标、准则、方案等多
个层次[28]。在此基础上应用定性和定量的分析方法，进行决策。具体方法步骤，
如下： 
①构建层次结构模型 
分析项目风险管理中各因素之间的关系，建立系统的评价体系。层次结构模
型分为三层，分别是目标层、准则层、方案层。 
②构建判断矩阵 
在风险层次结构模型中，将同一层次的风险因素进行两两比较，并根据层次
标度表进行赋值，最终构造出判断矩阵: 















nn
n
n
n
n
n
n
a
a
a
a
a
a
a
a
a
A
ij
..
..
..
..
..
..
..
a
2
1
2
22
21
1
12
11
 
表2.1 层次标度表 
Table 2.1 Hierarchical scale 
标度 
重要程度 
含义 
1 
同等重要 
两个指标相比，同等重要。 
3 
稍微重要 
两个指标相比，一个指标比另一个指标稍微重要。 
5 
明显重要 
两个指标相比，一个指标比另一个指标明显重要。 
7 
非常重要 
两个指标相比，一个指标比另一个指标非常重要。 
9 
绝对重要 
两个指标相比，一个指标比另一个指标绝对重要。 
2，4，6，8 
上述相邻判断中值 
倒数 
A 和B 相比如果标度是3，则B 和A 相比标度就是1/3。 
③求得
i
W 和
max

 
在层次分析法中，计算
i
W 和
max

的方法主要有和法、幂法、根法三种。本研
究中，采用根法进行计算。具体方法，首先将判断矩阵A 中的指标按行进行乘积
运算，然后开n 次方，然后利用下面公式进行向量归一化，可得出权重向量W。
最后，根据公式求出矩阵的最大特征值
max

。 
求判断矩阵每一行元素的几何平均值： 
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-13- 
                   


n
i
a
W
n
n
j
ij
,...,
3,2,1
1
i



           
（2-1） 
归一化处理： 
                   



n
i
i
i
W
W
W
1
i
                        （2-2） 
判断矩阵最大特征值
max

： 
                 





n
i
i
W
AW
1
i
max
n
1

                 （2-3） 
④一致性检验 
一致性指标CI 值越小，表示一致性越大。 
一致性指标公式： 
                
1
ma



n
n
CI
x

                      （2-4） 
随机一致性公式： 
n
...
2
1
n
CI
CI
CI
RI




 
表2.2 平均随机一致性指标表 
Table 2.2 Table of Average Random Consistency Indicators 
阶数 
1 
2 
3 
4 
5 
6 
7 
8 
RI 
0 
0 
0.52 
0.89 
1.12 
1.24 
1.32 
1.41 
如果一致性指标CI=0，则表示判断矩阵A 具有完全一致性，否则需要判断
检验系数CR。如果CR<0.1，表示矩阵通过一致性检验，否则不满足一致性检验，
需要重新构建判断矩阵。 
检验系数公式： 
                         
RI
CI
CR 
                       （2-5） 
（2）模糊综合评价法 
模糊综合评价法是1965 年由美国加利福尼亚大学查德教授首次提出的，以模
糊数学为基础的一种综合评价方法。在项目风险管理中，有些风险事件是无法精
确描述的，这就需要利用模糊综合评价法来进行模糊描述。该方法运用了模糊数
学的隶属度原理，将定性评价转化为定量评价。即用模糊数学方法对受到诸多因
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-14- 
素制约的对象，做出综合性的评价。模糊综合评价法具有系统性强，结果清晰的
特点，能很好地解决模糊且难以量化的各种非确定性问题[28]。具体方法步骤，如
下： 
①构建模糊评价指标集 
用U 表示项目的风险集。其中，目标层风险指标用


i
U
U
U
U
U
,...,
,
,
3
2
1

表示。
准则层风险指标用


ij
i
i
U
U
U
U
U
,...,
,
,
3
2
1i
i 
表示。 
②构建评价对象评语集 
    评语集的对象用V 表示。本研究中，将风险分为五个等级，分别是低、较低、
中、较高、高，分别用V1、V2、V3、V4、V5 表示，即评价对象评语集为


5
4
3
2
1
,
,
,
,
V
V
V
V
V
V 
。 
③构建风险因素权重集 
风险因素权重集，已由前文中层次分析法计算求出，用


i
W
a
,...,
a,
a,
a
3
2
1

表
示。 
④构建模糊评价矩阵 
隶属度评价矩阵，表示的是评价指标和评语集之间的模糊关系。模糊评价矩
阵采用专家评价法，对评价对象进行评价，最终得到模糊评价矩阵： 
                















mn
m
m
n
n
n
m
r
r
r
r
r
r
r
r
r
R
ij
..
..
..
..
..
..
..
r
2
1
2
22
21
1
12
11
                 （2-6） 
⑤进行综合模糊评价 
用风险因素权重向量和模糊评价矩阵的乘积，计算出综合模糊评价向量，即
R
W
B


。然后根据隶属度最大的原则，求出风险评价等级。 
（3）帕累托分析法 
帕累托分析法又称为ABC 分类法，主次分类分析法。这方法通过对对象的
特征进行分析，将其分类为重点和一般，以便更好地区分和管理事物，从而提高
管理效率和水平。在对分析对象进行分类中，将发生的累计频率为0-80%，归为
A 类因素，即主要影响因素；将发生的累计频率为80-90%，归为B 类因素，即
次要影响因素；将发生的累计频率为90-100%，归为C 类因素，即一般影响因素
[27]。 
（4）风险概率与影响矩阵 
在项目管理中，风险概率与影响矩阵是对项目风险进行有效度量和定性分析
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-15- 
的常用工具。在该工具中，将风险事件、风险发生的概率和产生的影响，定义为
风险管理的三要素，将风险发生的概率和影响定义为风险事件的两个属性。使用
工具的具体方法：首先将风险发生的概率，划分为很高、高、中、低、很低五个
等级；将风险发生后对项目造成的影响，也划分为很高、高、中、低、很低五个
等级；然后，将影响程度作为横向坐标，发生概率作为纵向坐标，得到一个5 乘
5 的矩阵。最后，根据该矩阵，定义高风险、中等风险、低风险的区域，对项目
风险进行度量和评级[28]。 
层次分析法具有系统性、简洁实用、所需定量数据少、易操作的特点。在应
用层次分析法构建各准则层判断矩阵时，可应用专家打分法，经整理分析得到各
准则层判断矩阵。该方法的使用，可以让较多的专家参与分析，防止了风险分析
的主观臆断，增强了分析结果的科学性、客观性。但是，层次分析法只计算了各
风险因素的权重，而无法进行有效的评价。而模糊综合评价法，采用精确的数学
方法处理模糊的评价对象，较为科学、合理、贴近实际。L 公司运维管理系统项
目风险因素，相对来说指标不多，数据统计工作量不大。采用层次分析法和模糊
综合评价法进行项目风险分析，两种方法可以优势互补。对本研究，具有较高的
可行性和适用性。最后，应用帕累托分析法，对风险分析的结果进行分类。可将
项目风险划分为主要、次要、一般，为后续的风险应对提供有效依据。 
2.2.4 风险应对 
风险应对是在识别、估计和评价项目风险后，依据风险发生的概率和影响程
度，结合项目的总体要求，确定选择什么样的应对策略，降低风险的概率和影响
程度，减少风险所带来的损失。常见的风险应对策略有减轻、预防、转移、回避、
接受、后备措施六种[28]。 
（1）减轻风险 
是通过实施一系列的措施，例如缓和、预知，降低风险发生的概率或减轻风
险带来的影响，以减少项目风险并将其降到可接受的水平。 
（2）预防风险 
是一种积极主动的风险管理策略，主要包括有形手段和无形手段。有形手段
主要是用一些工程技术的手段，消除物质上的风险威胁。无形的手段，主要是利
用教育法和程序法，来预防项目风险。教育法是通过培训的手段，使管理者具有
风险管理的相关知识、能力、意识。程序法就是通过制定相关的规章制度、管理
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-16- 
办法等，以避免项目风险的发生。 
（3）转移风险 
是一种将风险从项目移交给其他组织或个人来分散风险的策略。这有助于减
少项目承担的风险，但并不意味着解决了风险。该策略通常通过合同和协议来实
现，从而将风险分配给有能力承担风险的合作伙伴。 
（4）回避风险 
是当项目面临严重风险，且其他所有风险管理策略都无法解决时，选择放弃
项目或调整计划以避免风险的管理策略。其目的是规避风险而非解决风险问题。 
（5）接受风险 
是管理者在面对项目风险时有意识地承担风险的策略，管理者相信自己有足
够的能力来承担风险的后果。 
（6）储备风险 
是为了确保大型项目能够达成预期目标而制定的风险管理措施。这些措施包
括预算应急措施、进度后备措施和技术后备措施等，用于应对可能的项目风险和
突发情况。 
2.2.5 风险监控 
风险监控是为实现项目风险规划、风险识别、风险评估和风险应对等各个环
节而进行的一系列全过程的监控和控制活动，以确保项目风险管理能够达到预期
目标。在风险监控的过程中，对项目的环境进行持续的监视，掌握项目的进展情
况，并及时反馈。一旦发现偏差，就必须及时采取有效的控制措施。风险监控是
项目成功的关键之一，其目的是确保项目风险管理计划的有效实施，并在项目执
行阶段识别和应对新的风险。此外，风险监控还应对每个风险事件的成败制定标
准和依据[28]。 
2.3 信息系统项目概念 
信息系统是以处理信息流为目的的人机一体化系统。它是由计算机硬件、网
络通讯设备、计算机软件、信息资源、信息用户以及相关的规章制度组成的。信
息系统项目是一种按照用户需求，运用相关技术和工具进行设计和开发的过程。
旨在集成各种信息资源，协调不同功能之间的工作，实现系统的整体效益，满足
用户需求并创造项目价值[22]。信息系统项目与一般的项目有许多共通点，如时间
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-17- 
和资源的约束、项目的生命周期等。但信息系统项目与计算机和信息技术密切相
关，因此，具有区别一般项目的特殊性。信息系统项目具有以下特点[39]： 
（1）目标不明确性 
信息系统建设目标，就是客户的目标。但在项目建设初期，客户常常只能提
出一些初步的建设需求，并不能明确想法，提出确切且详细的项目需求。项目的
目标和规划，很大程度上是由项目小组制定的。 
（2）需求变更频繁 
随着客户对项目参与的深入，会激发客户产生新的需求。客户的需求发生变
化，项目的目标、规划、范围也会随之变化。在信息系统项目初期，需求变更成
本较小，越到后期，需求变更的成本会越大。并且需求的变更，可能还会带来新
的意想不到的问题。这时，就需要项目组成立项目变更控制委员会，对项目需求
的变更进行评估。并不是客户的每一个变更需求，都要满足。 
（3）智力密集型 
信息系统项目建设工作，其技术性强，需要具有计算机相关知识和开发实践
经验的专业性人才来完成。信息系统项目基本上都较为复杂，涉及面广，既需要
懂技术，又需要明白相关的业务知识，是一项对人才要求较高的高强度脑力工作。
因此，在项目实施过程中，如果发生项目组成员的流动或离职，则会影响项目的
建设。 
（4）与信息技术密切关联 
信息技术的快速发展，为信息系统项目的建设带来了前所未有的变革。新技
术、新工具的应用，使信息系统项目建设更高效，但同时也增加了项目技术环境
的复杂性。因此，信息系统项目技术的选型和驾驭能力，与项目的成功有着密切
的联系。 
（5）受人力资源影响较大 
信息系统项目的组织结构，成员的责任心、能力和成员的稳定性对项目具有
重要的影响力。信息系统项目的开发，具有较强的人为因素和个人风格。为了高
效且高质量地完成项目，需要依靠全体项目组成员团结努力，发挥集体的聪明才
智。其中，项目组成员不仅要有良好的技术水平和工作经验，还要有较强的责任
心和职业道德。 
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-18- 
2.4 信息系统项目风险 
任何项目建设都是有风险的，项目风险具有客观性、普遍性、偶然性、必然
性。项目风险是客观存在的，它可能发生在项目范围、成本、进度、质量、人力
资源、沟通、采购等各个环节。所有项目的建设中，风险是普遍存在的。项目风
险的发生，可能在项目建设周期的任何时间点，可能是人为的，也可能是社会、
自然等环境因素。项目风险所产生的影响，管理者是无法精确预测的。因此，项
目风险的发生是偶然的，但它不是完全无法预测和控制。项目风险可以通过风险
经验的积累和先进的管理方法，必然会避免和减少项目风险。项目管理者一直致
力于降低风险发生的概率和减少风险带来的影响，但没有任何办法可以完全消除
风险。 
信息系统项目风险除了具有以上特征外，还有可变性、多样性、多层次性。
信息系统项目可能会受到相关政策制度的变化，或者管理者的决策等因素，从而
影响项目。信息系统项目在整个项目周期中，随着时间的推移，也会随之发生变
化。有的风险可能不存在了，有的风险可能由轻微变成严重，这些都是可能的。
此外，由于信息系统项目一般较为复杂、周期长、规模大、人员多等特点，使得
信息系统项目风险，可能是多种多样的，多层次的[25]。 
常见的信息系统项目风险类型包括需求风险、技术风险、管理风险、人员风
险、安全风险、质量风险以及沟通风险[22,25]。 
（1）需求风险 
需求风险主要包括以下几个方面：客户只能初步描述需求，不能准确的表达，
对项目需求不明确；需求调研人员不够专业，客户参与度低，没有充分的调研需
求；需求变更频繁，没有成立变更控制委员会，没有制定相应的需求变更计划；
对需求确认和变更，没有进行有效的控制和跟踪。 
（2）技术风险 
技术风险主要包括硬件风险、软件风险、系统集成风险。 
硬件风险主要是指信息系统项目建设中，所使用的硬件设备包括计算机、服
务器、打印机等设备的风险。这些硬件设备是信息系统项目建设所必须的，其配
置、性能、稳定性、兼容性等指标，需要达到项目要求。 
软件风险主要是指项目建设所需的开发工具、开发所需的软件环境、数据库
软件、服务器部署软件，以及项目开发所采用的系统框架、开发语言等内容的风
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-19- 
险。在选择系统开发所需的软件中，可能会面临诸多选择。版本较新且技术先进
的软件，开发时可能会更高效，效果可能会更好。但是，新技术在刚刚推出时，
往往会不稳定，盲目地追求先进的技术，可能会带来意想不到的高风险。 
系统集成风险，主要是针对模块化开发的项目，在软件集成方面、硬件集成
方面、以及软硬相集成方面，所存在的项目风险。在软件集成方面，接口制定不
规范，沟通不畅，没有按要求进行开发等问题，均会导致软件集成失败。在硬件
集成方面，对硬件设备不了解，没有经过充分的测试，也可能会导致硬件集成的
失败。在软件和硬件集成方面，要充分了解软件技术和硬件设备，对软件和硬件
相集成的兼容性和稳定性，要进行大量测试。随着信息技术快速的更新换代，在
硬件设备和开发软件方面不断的出现新技术。因此，只有充分了解所应用的技术，
才能保证项目的顺利完成。 
（3）管理风险 
管理风险包括的范围较广，管理对项目来说十分重要。项目管理涉及范围、
成本、进度、质量、人力资源、风险以及沟通等多个方面。任何方面出现偏差，
例如项目经理的管理经验不足，项目组织架构不合理，变更控制不力等因素，都
可能会造成项目成本超支、进度延误、质量降低等情况，最终可能会导致项目失
败。 
（4）人员风险 
人员风险主要包括两个方面，一是项目组成员的能力和素质，二是项目组成
员的变动。信息系统项目往往较为复杂，因此需要项目团队成员具备相关专业知
识、经验和出色的责任心和职业道德，尤其是在关键岗位上的成员。项目组成员，
一旦确定，最好不要变动。一旦项目组成员产生变动，特别是关键岗位的人员，
可能会对项目产生重要影响。项目成员在项目初期，发生变动，所带来的项目风
险影响会小些，越是到项目建设的关键时期，项目成员的变动，所产生的风险越
大。这时，项目经理应统筹协调，尽量挽留变动人员，解决问题。如果无法挽留，
新的成员加入，是需要一定的成本和时间的。需要掌握项目的相关信息，掌握项
目建设所需的开发知识，并需要时间与项目团队进行磨合，这些都可能会给项目
带来风险。 
（5）安全风险 
安全风险主要包括系统风险、网络风险、病毒风险。系统风险是指信息系统
项目开发过程中，所开发的产品存在的风险。在产品开发的过程中，系统所采用
东北大学硕士学位论文                                  第2 章 项目风险管理相关理论  
-20- 
的技术十分重要，包括系统框架，计算机语言等。有的技术存在重大安全漏洞，
用这样的技术开发完成，后期如果无法修复漏洞的话，可能会导致系统无法使用。    
计算机网络是信息系统项目所必须的，是项目的基础。畅通的网络，是项目
建设，系统平稳运行的保证。如果网络不通畅，就可能给项目沟通、项目开发、
项目运行带来风险，从而对项目造成影响。 
防病毒，对信息系统项目十分重要。目前，计算机病毒多种多样，一台计算
机被病毒感染，就有可能导致整个网络的计算机瘫痪，有可能使开发成果毁于一
旦。有的病毒使计算机无法使用，勒索巨额赎金才能恢复。这些风险对信息系统
项目影响巨大，安全风险应予以足够的重视。 
（6）质量风险 
质量风险是在项目开发过程中，对项目建设没有按照标准去执行；项目工期
紧，为了赶工而忽略项目质量；在项目测试过程中，没有做好测试计划，测试不
充分等原因，造成项目在交付时，达不到标准，工期延误、成本超支、给后期项
目维护造成困难的风险。在软件开发过程中，开发人员应按照开发标准规范进行
代码的编写。所开发的功能，应按照项目详细设计所描述的功能进行开发，应满
足项目需求。不能因为项目工期紧而忽略项目质量，项目经理在其中应做好统筹
协调工作。在项目测试过程中，应首先做好测试计划、测试用例，严格按照测试
计划进行测试。应先进行技术测试、再进行业务测试，测试通过后，应该让测试
人员在测试报告中签字确认。只有重视项目质量，高标准要求，才能做出成功的
项目，降低后期项目维护的成本，得到客户的好评。 
（7）沟通风险 
沟通风险主要是项目各干系人之间的沟通不畅，对项目所造成的风险。信息
系统项目建设过程中，沟通不畅，可能会影响工作效率，造成工期延误、返工，
甚至停工。在项目计划初期，应对项目沟通的方式、方法、等级、频次等做好规
划。有的信息需要及时地通知到每一位项目成员，有的信息需要及时汇报给项目
决策者，有的信息在每一次变更通过时，要及时地通知给项目开发人员。每一个
环节都要做好，才能保障项目沟通的通畅。此外，项目组成员应在具有相关专业
知识的同时，也要具有良好的沟通能力。
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-21- 
第3章  L 公司运维管理系统项目介绍及特
点分析 
3.1 项目介绍 
3.1.1 项目建设背景 
L 公司经过多年的全面信息化建设，在各业务环节中已经建立了信息系统，
基本实现了信息化，但在省分公司信息技术部门对于各市信息技术部门进行技术
指导时，以及各市分公司信息技术部门对于各业务部门及对所负责的自有网点进
行服务支撑时，却依然面临一个没有信息系统可以对信息技术部门的运维工作进
行支撑的局面。省、市两级信息技术部门的日常运维工作依然停留在沟通情况靠
电话，情况处理记录靠纸笔，工作量统计没依据的尴尬境地。这一情况已经越来
越成为一个制约信息技术部门进一步提升运维服务质量的重要因素。因此，L 公
司信息技术中心针对这一问题，在充分调研各市分公司的实际情况的基础上，计
划设计开发L 公司运维管理系统。 
3.1.2 项目建设目标 
L 公司运维管理系统项目是一个立足全省的重要信息化项目。项目整体的建
设目标主要有四个方面，分别是建立完善的运维管理平台，提升维护效率和运维
响应速度，提高运维服务的满意度，提供数据支持和考核依据。 
（1）建立完善的运维管理系统平台 
通过引入先进的技术手段和工具，打造一套高效、智能、可靠的运维管理系
统平台。该平台将解决省分公司信息技术中心对各市信息技术工作的指导，各市
分公司信息技术中心对各业务部门以及所负责的自有网点的服务支撑工作。切实
解决目前L 公司运维管理的痛点问题，从而提升L 公司运维管理的水平和核心竞
争力。 
（2）提升维护效率和运维响应速度 
项目的建设，将建立完整的运维流程和工作规范，有效地管理运维资源，加
强预防性维护，减少故障出现率和处理故障的时间和代价，以更快、更准确地响
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-22- 
应业务需求。 
（3）提高运维服务的满意度 
该项目的建设，将建立全流程的运维闭环管理，能够快速解决运维管理问题，
确保L 公司业务的高可靠性和高可用性。同时，解决增加台席等计划性服务缺少
直接联动机制，工作准备缺少提前性的问题；增加了基层网点对服务处理结果的
反馈，以提高运维服务满意度。 
（4）提供数据支持和考核依据 
运维管理系统平台，将解决网点故障上报依靠电话，处理过程缺少电子记录，
工作缺少量化统计的现状。通过归纳、分析、挖掘和展现数据，形成高质量的数
据仓库和信息报表，帮助决策者了解运维管理状况，为优化全省运维管理和加强
运维队伍建设提供依据。 
除此之外，在项目质量方面，要保证该项目的功能符合乙方需求，保证系统
的可靠性、易用性、安全性、可维护性和高效性。在项目进度方面，要制订详细
的规划和时间表，根据项目实际情况确定阶段性的里程碑和交付任务。制定详细
的进度管理计划，确保项目进度符合计划。在项目成本方面，应制定详细的成本
预算，合理利用和规划人力资源，尽量减少不必要的成本投入。 
3.1.3 项目主要功能 
L 公司运维管理系统在运维处理流程方面，主要分为网点工作人员、11185
客服、市级调度员、市级技术人员、省级调度员、省级技术人员，六个角色。网
点工作人员主要负责网点故障信息的上报、服务处理的评价。11185 客服主要负
责接收通过电话途径上报的故障信息，将其录入运维管理系统中。市级调度员是
由市公司信息技术中心值班人员担任，主要负责下属机构故障信息的任务分配。
市级技术人员，主要负责对市级调度员所分配的故障任务进行处理。省级调度员
是由L 公司信息技术中心运行维护部值班人员担任，主要负责下属机构故障信息
的任务分配。省级技术人员主要是由L 公司信息技术中心运行维护部技术人员担
任，主要负责对省级调度员所分配的故障任务进行处理。具体系统流程，如图3.1
所示。 
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-23- 
 
图3.1 系统流程图 
Fig. 3.1 System Flow Chart 
L 公司运维管理系统的功能主要包括网点实时故障上报、网点服务计划请求、
11185 故障上报、自领调度、故障补录、故障任务分配、故障处理、故障批量处
理、市公司故障报告、网点故障处理结果反馈、网点派修单打印、故障关闭、故
障查询，共13 项功能。 
（1）网点实时故障上报 
网点实时故障上报功能，是当网点出现系统软件、硬件设备、以及其他故障
问题时，登陆运维管理系统，在服务支撑菜单下，点击实时服务请求，进行故障
上报。上报的故障信息将会提交到市分公司信息中心处。 
（2）网点服务计划请求 
网点服务计划请求功能，用于网点需要增加台席、网点搬家等计划服务请求
时，可使用该功能进行服务请求。 
（3）11185 故障上报 
当服务请求的发起人，不能够通过系统进行上报故障时，可以通过拨打11185
客服电话，进行故障上报。11185 客服人员，在收到故障上报人的电话后，会采
集上报故障信息，将其提交至所属市分公司信息中心，进行故障处理。 
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-24- 
（4）自领调度 
该功能用于省、市分公司技术人员在值班日将自身角色更换为调度。更换为
调度角色后，可以对故障信息进行任务分配。 
（5）故障补录 
该功能是用于省、市分公司技术人员补录业务人员直接向省、市分公司调度
反映的各类服务请求。该类服务请求是在上报人没有通过系统进行上报的情况下，
相关处理人才可以进行信息的补录。 
（6）故障任务分配 
故障任务分配用于省、市分公司技术人员在成为调度后，将系统接收到的服
务请求分配给本单位其他技术人员。任务分配提交后，任务的主要负责人就会收
到系统自动发送的提醒短信。 
（7）故障处理 
故障处理功能用于省、市分公司调度或其他技术人员对业务人员通过系统上
报的服务请求进行处理，在完成处理后，故障报告的发起人会收到系统自动发送
的反馈短信。 
（8）故障批量处理 
故障批量处理功能用于省、市分公司调度或其他技术人员在遇到出现网点大
面积故障时，进行统一的处理和回复，在完成批量处理后，故障报告的发起人会
收到系统自动发送的反馈短信。 
（9）市公司故障报告 
市公司故障报告功能用于市分公司技术人员向省分公司上报服务请求。该功
能是在市分公司无法解决运维故障问题时，可使用该功能向省分公司汇报。在提
交故障请求后，上报故障请求的市分公司技术人员和省分公司当班调度人员就会
自动收到系统发送的提醒短信，方便提醒和跟踪故障请求的进展情况。 
（10）网点故障处理结果反馈 
当故障处理结束后，网点故障恢复正常，此时，网点工作人员可以登陆运维
管理系统，在服务请求反馈功能处，对故障处理的情况进行评价。如果网点工作
人员关注了企业微信号，还可以通过企业微信，对该项运维管理服务进行评价。
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-25- 
评价等级分为非常满意、满意、一般，并且还可以对故障处理提出反馈意见。 
（11）网点派修单打印 
故障处理人员，可以在故障网点，登陆系统，进行派修单打印。派修单是参
照各地市信息中心现有派修单内容开发的，一式两份，网点和市分公司信息中心
各一份。 
（12）故障关闭 
该功能是网点工作人员，在通过系统或者通过11185 渠道进行故障上报时，
出现了误报的情况，需要对该条故障进行撤回而开发的功能。 
（13）故障查询 
故障查询功能主要分为三类，一是用于故障上报发起人对于故障处理情况进
行查询；二是市分公司技术人员对于本市内的故障信息进行查询、统计、分析；
三是省分公司技术人员对全省范围内的故障信息进行查询、统计、分析。 
3.1.4 项目组织机构 
在项目立项后，由L 公司领导组织公司技术部门及项目相关人员，召开项目
会议。会议中，强调了运维管理系统项目的重要性，并任命甲方项目经理，乙方
项目负责人。在任命项目经理后，由项目经理研究制定项目甲方组织机构，明确
了项目各小组职能划分和主要职责。项目甲方组织结构，如图3.2 所示。 
图3.2 项目甲方组织结构图 
Fig. 3.2 Organizational chart of Party A 
 
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-26- 
项目甲方由项目甲方领导、项目经理、管理小组、需求小组、开发小组、运
维小组、测试小组、质量小组，共16 人组成。其中管理小组2 人，负责人为项目
经理；需求小组2 人，负责人为需求管理D；开发小组6 人，负责人为研发工程
师E；运维小组1 人，负责人为运维工程师F；测试小组2 人，负责人为测试员
G；质量小组2 人，负责人为质量管理员H。在制定甲方项目组织结构的同时，
也制定了各小组及负责人的职责。 
（1）甲方领导 
对项目整体负责，定期听取项目经理汇报，并根据项目的实际情况，协调项
目所需资源。 
（2）项目经理 
负责沟通协调项目各干系人，制定项目组织结构，制定项目管理计划，监督
并控制计划执行；负责项目组整体的组织协调工作，项目建设方案的决策；组织
并负责项目需求变更控制。 
（3）管理小组 
负责参与项目计划的制定，协助项目经理监督并控制项目计划执行；负责整
理各小组工作日志，并向项目经理汇报情况；负责项目相关文档的管理；负责项
目后期的相关培训工作。 
（4）需求小组 
负责前期项目需求的调研，需求说明书的编写，以及项目需求变更的评估。 
（5）开发小组 
负责项目技术方案的制定，项目概要设计，详细设计，并完成项目功能代码
的开发工作。 
（6）运维小组 
负责开发环境、测试环境、正式环境的服务器搭建，包括硬件、软件、网络
设备等；负责各个环境的安全管理；负责版本服务器SVN 的搭建和管理。 
（7）测试小组 
负责项目各功能的技术测试，并指导和配合项目用户的业务测试；负责测试
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-27- 
用例的设计，测试文档的编写，以及测试结果的反馈，并出具最终测试报告。 
（8）质量小组 
负责项目功能、代码、相关文档的质量的监督和控制；负责项目整体质量的
评估。 
项目乙方由乙方项目领导1 人、项目乙方负责人1 人、项目乙方需求负责人1
人组成。项目乙方领导主要是负责协调需求方各干系人，把握项目整体情况。项
目乙方负责人主要负责项目乙方的具体工作管理，并负责向乙方项目领导汇报项
目工作情况。项目乙方需求负责人主要负责项目需求的制定，协助甲方确定项目
需求，与甲方做好沟通和对接工作。 
3.1.5 项目进度安排 
 L 公司运维管理系统项目计划从2023 年5 月4 日开始，到2023 年12 月4
日交运维止，建设周期为7 个月，共分为11 个阶段，分别是需求调研、需求分析、
概要设计、详细设计、系统配置、系统研发、系统测试、系统培训、系统试运行、
系统上线、系统运维，具体安排，如表3.1 所示。 
表3.1 项目进度表 
Table 3.1 Project Schedule 
阶段 
时间点 
责任人 
阶段性工作描述 
需求调研 
2023.05.04- 
2023.05-19 
需求管理D 
在项目初步需求的基础上，对省中
心、部分市级信息技术中心的运维
管理部门，部分营业网点等系统管
理和使用者，以访谈和调查问卷的
形式进行具体调研。 
需求分析 
2023.05.22- 
2023.06.02 
需求管理D 
对调研的需求进行具体分析，确定
项目需求，并形成项目需求说明书。 
概要设计 
2023.06.05- 
2023.06.16 
研发工程师E 
完成项目概要设计。 
详细设计 
2023.06.19- 
2023.06.30 
研发工程师E 
完成项目详细设计。 
系统配置 
2023.07.03- 
2023.07.14 
运维工程师F 
完成网络、数据库、服务器的配置，
包括开发环境、测试环境、正式环
境、SVN 服务器。 
 
 
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-28- 
                                                                       续表3.1 
阶段 
时间点 
责任人 
阶段性工作描述 
系统研发 
2023.07.17- 
2023.10.27 
研发工程师E 
完成系统研发，形成符合详细设计
的系统功能。 
系统测试 
2023.08.11- 
2023.10.27 
测试员G 
设计测试用例，进行技术测试后，
再进行业务测试，并形成测试报告。 
系统培训 
2023.10.30- 
2023.11.03 
项目管理C 
完成系统帮助文档和培训课件的制
作，并通过电视电话会议及现场指
导的方式进行培训指导。 
系统试运行 
2023.11.06- 
2023.11.24 
经理B 
解决系统试运行期间所有问题，为
系统正式上线做好准备。 
系统上线 
2023.11.27- 
2023.12.01 
经理B 
完成系统上线交业务工作。 
系统运维 
2023.12.04- 
以后 
运维工程师F 
待系统运行稳定平稳后，交信息技
术中心运行维护部，对系统进行后
续技术支持。 
 
 
图3.3 项目进度图 
Fig. 3.3 Project Progress Chart 
在项目工作顺序安排上，首先是项目需求的调研和分析，然后是概要设计和
详细设计，与此同时完成系统配置工作。接下来是系统研发，同时进行系统测试。 
系统测试过程很重要，先进行技术测试，测试通过后，再进行业务测试，过程中，
如遇到系统功能问题，则提交测试结果给研发小组进行修改，循环往复，直至问
题解决。所有功能测试通过后，进行为期一周的系统培训。之后，进行为期一个
月的系统试运行。在此阶段，需要解决测试中未暴露出的问题，待系统平稳运行
后，进行系统上线。具体工作顺序，如图3.4 所示。 
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-29- 
 
图3.4 项目工作顺序图 
Fig. 3.4 Project Work Sequence Diagram 
3.2 项目特点分析 
信息系统项目具有目标不明确性、需求变更频繁、智力密集、与信息技术密
切关联、受人力资源影响较大的特点。而L 公司运维管理系统项目是一个全省性
的信息系统项目。除了具有信息系统项目普遍性特点外，还具有其自身特点。下
面将从信息系统项目特点的普遍性和特殊性两个方面，对L 公司运维管理系统项
目进行分析。 
（1）普遍性特点 
①目标不明确性 
在本项目中，虽然项目乙方已经提出了项目的基本需求，但是项目需求仍旧
比较粗糙，项目的目标及规划不够明确。 
②需求变更频繁 
本项目仍在项目建设初期，随着乙方对项目参与的深入，可能会变更或者产
生新的需求。这种情况是信息系统项目建设过程中，普遍存在且无法预料的。 
③智力密集 
项目本身很重要且较为复杂，既需要懂技术、懂业务、又要懂管理，是一项
需要较多员工参与的脑力工作。因此，项目组成员的稳定性，对项目建设十分重
要。 
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-30- 
④与信息技术密切关联 
对于信息系统项目建设来说，技术的选型与应用，关系着项目的成败。 
⑤受人力资源影响较大 
任何信息系统项目的建设，都与项目组成员的个人能力、技术水平、责任心、
职业道德息息相关，L 公司运维管理系统项目也不例外。 
（2）特殊性特点 
①省内自建项目 
该项目是省内自建项目，项目甲方是L 公司信息技术中心软件部、乙方是信
息技术中心运维部，项目的使用单位是L 公司，具体包括省、市、网点三级，使
用部门和用户较多。 
②项目十分重要 
从项目的建设背景和建设目标中，了解到本项目十分重要。L 公司运维管理
系统项目是一个全省性的重大项目，旨在提高全省运维服务质量和效率，提升运
维服务水平。 
③项目较复杂 
从项目的主要功能中，了解到本项目分为省、市、网点三级。目前，系统有6
个角色，初步需求功能13 个，系统中的运维处理流程较为复杂。 
④项目组人员紧张 
从项目的组织结构中，了解到项目共分为8 个小组，每个小组都有不同的职
能分工。本项目的组织结构是矩阵型结构，项目组成员分别是从工程部、软件部、
运维部三个部门抽调的。整个甲方项目组共16 人，有的项目组成员，在参与本项
目工作的同时，还可能参与其他项目的工作。 
⑤项目工期紧 
从项目的进度安排中，了解到项目的建设周期只有7 个月。整个工期共分为
11 个阶段，最短只有两周，最长也只有3 个月。12 月1 日，就需要系统正式上线
运行，项目工期较紧。 
⑥项目预算少 
在项目预算方面，公司领导要求尽量节省项目成本。项目的成本支出主要在
设备采购、项目组成员加班、调研等环节。 
⑦要求兼容性好 
由于系统使用的部门和用户多，访问的终端设备的型号、操作系统也不尽相
东北大学硕士学位论文                   第3章 L公司运维管理系统项目介绍及特点分析 
-31- 
同。因此，需要该系统具有良好的兼容性，能够在不同终端设备上访问、运行。 
⑧要求系统安全可靠 
    由于该系统十分重要、用户较多、访问量较大，对网络、数据、运行环境等
方面提出较高要求。 
    综合以上项目特点，L 公司运维管理系统项目在建设过程中，存在一定的项
目风险。为了保证项目建设的顺利成功，有必要对该项目进行风险管理研究。接
下来，将对L 公司运维管理系统进行风险识别，为后续的风险分析打下坚实的基
础。
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-32- 
第4章  L 公司运维管理系统项目风险识别 
4.1 风险识别思路 
4.1.1 风险识别原则 
在本项目中，依据6 项原则进行风险识别。这些原则包括全局考虑原则、谨
慎排除原则、及时反馈原则、全员参与原则、经济性原则、科学量化原则。 
（1）全局考虑原则 
在分析项目风险时，有些风险较为复杂，可能存在于系统的多个环节。这时，
我们分析项目风险，就需要站在全局的角度去思考，不能简单和片面地去评估和
度量风险。 
（2）谨慎排除原则 
在获得项目初始风险清单后，分析项目风险时，该原则可以帮助我们避免错
误和偏见的影响，做出更为有效和合理的判断和决策。当面临不能排除也不能肯
定的风险时，应按照确认风险来处理。 
（3）及时反馈原则 
在项目的各个阶段都有可能会发生风险，且风险各不相同。项目管理者应实
时监控风险措施执行效果，识别项目新风险，并将监控的结果及时的向相关责任
人反馈，避免因反馈不及时而带来新的项目风险。 
（4）全员参与原则 
项目风险的识别和分析，应采取全员参与的原则。熟话说“众人拾材火焰高”，
“团结就是力量”，要充分发挥集体力量的优势。在项目团队中，有各领域的专家
和技术骨干，也有各领域的普通团队成员，大家各有各的优点，每个人都有自己
擅长的地方。因此，全员参与原则，很有必要。 
（5）经济性原则 
项目风险多种多样，有的项目风险所带来的影响可能没有去处理这个风险所
付出的成本多，出于经济性的考虑，可以综合评估是否需要对这一类风险采取措
施。 
（6）科学量化原则 
项目风险的评估应该是客观的，应进行科学量化，并以此为依据做出判断。
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-33- 
科学量化原则，能够提高项目风险管理的科学性和有效性。在本研究中，采用了
层次分析法和模糊综合评价法对L 公司运维管理系统项目进行科学评估，以期望
达到更科学、更合理的风险评估结果。 
4.1.2 风险识别方法 
在本研究中，对L 公司运维管理系统项目风险识别，首先采用文献研究法，
梳理出信息系统项目建设的一般风险因素。经过整理和分析，得到项目的初级风
险清单。然后，根据L 公司信息系统项目建设的实际情况，对初级风险清单中的
部分风险因素进行过滤和重新定义，得到修正风险清单。最后，采用头脑风暴法，
对修正风险清单进行讨论，得出最终的项目风险识别结果。采用头脑风暴法的好
处是可以充分调动项目团队集体的力量，集思广益，发散思维。 
在头脑风暴会议展开前，项目风险管理组织者会向团队成员介绍会议的规则，
并介绍一些辅助分析方法，包括SWOT 分析方法、类比法、经验分析法、风险核
对表法，供团队成员参考。在头脑风暴会议中，需要每一位项目成员认真思考项
目风险识别这一目标课题，可采用一些辅助方法进行自我分析。每一位成员针对
会议问题发表意见时，发散思维，尽可能多地表达意见和建议，其他成员不能够
打断。会议中会安排专人进行记录，头脑风暴会议要求成员多发表意见，不用过
分注重质量。经过头脑风暴会议，进行整理、分析、修正，最终得出项目风险识
别表。 
4.1.3 风险识别步骤 
（1）确定风险识别目标 
首先，需要确定项目风险识别的目标。项目风险识别的目标就是识别风险，
防患于未然。除此之外，项目管理者还要结合项目的实际情况，根据项目的各个
环节范围、重点关注情况，制定更详细的目标。 
（2）进行文献研究 
    查找与本研究相关的文献，包括书籍、期刊、硕士论文、本单位内部信息系
统项目建设资料。所查文献，需要紧紧围绕信息系统项目风险管理，这一主题。
根据所查阅的文献资料，通过分析，总结出信息系统项目建设常见的风险类别及
风险因素，形成初级风险清单。 
（3）修正风险清单 
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-34- 
    根据L 公司信息系统项目建设的实际情况，对初级风险清单中的部分风险因
素进行过滤和重新定义，得到修正后的项目风险清单，为头脑风暴会议做准备。 
（4）进行头脑风暴会议 
①确定参与者和参与规则 
项目管理人员需要明确项目风险识别的参与者，特别是重要的参与者，并制
定相关的参与规则。这需要管理者首先了解项目的具体情况，项目组人员构成。
相关领域的专家和技术骨干是重要的参与者。原则上是全员参与，重要的参与者
必须参与。参与的规则，需要参考头脑风暴法的具体规则，结合SWOT 分析方法、
类比法、经验分析法、风险核对表法等方法，对风险识别目标进行具体分析。 
②收集项目风险资料 
收集项目风险资料，是进行头脑风暴的基础。收集的资料主要包括项目需求
说明书，项目章程，项目计划，包括范围计划、 成本计划、进度计划、质量计划、
人力资源计划、沟通计划等计划。项目的前提、假设、资源、制约因素等内容。
收集与本项目类似的信息系统项目案例资料，借鉴过去建设项目的经验和教训。 
③评估项目风险影响 
在修正的项目风险清单的基础上，组织召开头脑风暴会议，并特别邀请相关
领域的专家和技术骨干，对项目风险因素表中的每一项风险进行概率和影响的评
估。在此过程中，需要遵循全局考虑原则、谨慎排除原则、经济性原则，认真地
对每一项项目风险进行考虑。会议中要安排专人进行记录整理。 
④分析会议，得出结果 
在头脑风暴会议的基础上，对会议的内容进行分析。得出风险识别的最终结
果，即项目风险识别表。 
4.2 风险识别过程 
L 公司运维管理系统项目是由L 公司信息技术中心承建的省内自建项目。该
项目是一项应用于全省的重大管理类信息系统项目。旨在提高全省运维服务质量、
效率和水平，规范运维服务标准和流程，以科学技术提高企业信息技术部门运维
管理水平，助力企业发展。 
由于项目规模较大，流程较为复杂，涉及角色较多，因此该项目存在较大风
险。项目中的风险具有多种多样、隐藏较深、不易发现的特点。为了能够全面地
识别项目中的风险，顺利地完成项目建设，在本研究中，采用文献研究法，得到
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-35- 
项目的初级风险清单。接下来，考虑项目的实际情况，对项目初级风险清单进行
修正。最后，采用头脑风暴法，对修正后的风险清单，进行头脑风暴会议。会后，
通过对头脑风暴的内容进行整理、分析，最终形成项目的风险识别表，成为本项
目风险管理的有利依据。下面具体介绍本项目风险识别的过程： 
（1）文献研究 
①通过研读《项目风险管理》一书，了解到软件项目一般有以下5 类风险，
分别是产品规模风险、需求风险、相关性风险、管理风险、技术风险。在本书中，
还具体说明了5 种风险类别中的风险因素。 
②在《智慧政务门户网站项目风险管理研究—以C 经济开发区为例》论文中，
将其研究的信息系统风险分为决策风险、管理风险、人员风险、技术风险、安全
风险，并具体说明了风险类别中的风险因素。 
③在《A 公司软件项目风险管理》研究中，将其研究的软件项目风险分为需
求风险、技术风险、过程风险、管理风险，并具体说明了风险类别中的风险因素。 
④在《A 企业信息系统建设项目风险管理研究》中，将其研究的信息系统项
目建设风险分为质量风险、技术风险、人员风险、控制风险、安全风险，并具体
说明了风险类别中的风险因素。 
⑤在《信息系统项目风险因素分析》中，阐述了信息系统项目开发中的风险
因素，包括需求定义、项目设计、项目实施、人员、产品外包、系统用户、过程
管理，共7 大类36 项风险因素。 
⑥在L 公司信息技术中心近三年开发的信息系统项目资料中，总结出了本单
位项目建设过程中，常见的信息系统项目建设风险项目六项。具体包括需求风险、
技术风险、管理风险、人员风险、决策风险、安全风险。并在研究过程中，认真
地梳理了风险类别中的风险因素。 
最终，经过整理和分析得出初级风险清单，如表4.1 所示。 
表4.1 初级风险清单表 
Table 4.1 List of Primary Risks 
序号 
风险类别 
风险因素 
1 
需求风险 
需求定义不准确 
2 
成为基准后，需求仍在不停变化 
3 
追加需求 
4 
缺少需求的变更管理 
5 
需求定义时间过长 
6 
不恰当不合理的需求 
7 
技术风险 
架构风险 
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-36- 
续表4.1 
序号 
风险类别 
风险因素 
8 
技术风险 
技术选择风险 
9 
集成风险 
10 
技术规范风险 
11 
硬件风险 
12 
管理风险 
项目目标风险，目标定位不明确 
13 
项目沟通风险 
14 
项目进度风险 
15 
项目质量风险 
16 
项目成本风险 
17 
人员风险 
人员流动风险 
18 
人员培训风险 
19 
人员能力风险 
20 
人员数量风险 
21 
人员沟通风险 
22 
决策风险 
政策风险 
23 
领导风险 
24 
顶层设计风险 
25 
组织决策风险 
26 
安全风险 
系统漏洞风险 
27 
网络安全风险 
28 
数据安全风险 
29 
软硬件环境风险 
30 
分包商风险 
（2）修正初级风险清单 
根据L 公司运维管理系统项目的实际情况，对上一步获得的初级风险清单，
进行深度分析，对部分风险因素进行过滤和重新定义。具体分析如下： 
①在L 公司运维管理系统项目中，
“需求定义不明确”、
“需求定义时间过长”、
“不恰当不合理的需求”，均是由需求调研导致的。因此，将以上三项风险因素合
并为需求调研风险。将需求风险类别中的“成为基准后，需求仍在不停变化”、“追
加需求”、“缺少需求的变更管理”，重新定义为“需求变更风险”。 
②在技术风险类别中，技术选择风险包含了架构风险。因此，去掉架构风险。
在L 公司运维管理系统项目中，有现成的技术标准和规范，且技术规范风险中的
内容与管理风险类别中的质量风险有重复。因此，去掉技术规范风险。 
③在L 公司运维管理系统项目中，不存在项目目标不明确的风险。因此，去
掉管理风险类别中的“项目目标风险，目标定位不明确”。 
④在人员风险类别中，人员沟通风险与管理风险类别中的沟通风险内容重复。
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-37- 
因此，去掉人员沟通风险。同时，人员培训风险与人员能力风险也有内容上的重
复。因此，去掉人员培训风险。 
⑤在L 公司运维管理系统项目中，不存在决策上的风险。因此，去掉整个决
策风险类别。 
⑥L 公司运维管理系统项目是省内自建项目，不存在分包商。因此，去掉安
全风险类别中的分包商风险。此外，在L 公司运维管理系统项目中，将软硬件风
险，定义为运行环境风险更为合适。 
最后，得到修正后的风险清单，如表4.2 所示。 
表4.2 修正风险清单表 
Table 4.2 Revised Risk List 
序号 
风险类别 
风险因素 
1 
需求风险 
需求调研风险 
2 
需求变更风险 
3 
技术风险 
技术选择风险 
4 
集成风险 
5 
硬件风险 
6 
管理风险 
项目沟通风险 
7 
项目进度风险 
8 
项目质量风险 
9 
项目成本风险 
10 
人员风险 
人员流动风险 
11 
人员能力风险 
12 
人员数量风险 
13 
安全风险 
系统漏洞风险 
14 
网络安全风险 
15 
数据安全风险 
16 
运行环境风险 
（3）头脑风暴会议 
①确定会议人员 
由项目经理组织召开L 公司运维管理系统项目风险识别讨论会。为了能够全
面、精准地识别出项目风险，会议拟定项目组全员参与。参与人员主要有项目建
设的管理者、项目各领域的专家、技术骨干等人员，具体包括管理小组2 人（含
项目经理）、需求小组2 人、开发小组6 人、运维小组1 人、测试小组2 人、质量
小组2 人，共计15 人。会议由非项目相关人员负责主持和控制会议，并做好会议
记录。会议中，不能打断参会人员的发言，且不对其做出评价。 
②召开头脑风暴会议 
项目头脑风暴会议在L 公司信息技术中心七楼会议室举行。在会议前，由项
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-38- 
目管理人员发布通知。具体内容包括会议的时间、地点、人员、会议目的、内容、
以及会议所需要的材料，会议规则等内容，并以邮件的形式发送给每一位参会人
员。其中，会议所需材料包括项目规划书、项目可行性分析报告、项目需求说明
书、项目各部分管理计划等能够反映项目建设情况的材料。会议中，参会人员根
据个人的工作能力和经验，从L 公司运维管理系统的实际情况出发，发散思维，
畅所欲言，紧扣会议主题。同时，指派专人对会议进行记录和录音。会后，与每
位发言人核实发言主要内容，形成会议纪要，以便后续整理分析，得出项目风险
识别表。 
③整理分析会议记录 
在会议结束后，由项目管理人员对会议中记录的内容进行整理、分析。最后
总结出五大类十六项风险因素，以及风险因素的具体说明。L 公司运维管理系统
项目风险识别表，如表4.3 所示。 
表4.3 项目风险识别表 
Table 4.3 Project Risk Identification Form 
类别编号 
类别名称 
风险编号 
风险名称 
说明 
B1 
需求风险 
B11 
需求调研风
险 
由于项目初期需求不够具体和明
确，需要项目组调研系统各角色使
用者，整理需求。并需要与运维管
理部门一起深入讨论、研究需求。
整个过程，需要各部门相关人员积
极配合，一起努力完成需求的确认
工作。因此，存在一定不确定性。 
B12 
需求变更风
险 
需求确定后，在项目建设过程中，
会存在项目提出方根据项目建设的
情况，不断地变更项目需求。需求
的频繁变更，会对项目的建设造成
影响。 
B2 
技术风险 
B21 
硬件风险 
项目建设过程中，需要计算机、服
务器、打印机、手机等不同品牌、
不同类型、不同配置的硬件设备。
硬件设备的情况，对项目具有一定
影响。 
 
 
 
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-39- 
续表4.3 
类别编号 
类别名称 
风险编号 
风险名称 
说明 
B2 
技术风险 
B22 
技术选择风
险 
技术选择主要包括系统框架的选
择，开发语言的选择。技术选择对
信息系统项目至关重要。 
B23 
系统集成风
险 
本项目采用模块化开发，在软件数
据交互，前后台接口规范，软硬件
结合等方面，需要加以关注。 
B3 
管理风险 
B31 
成本风险 
由于该项目是省内自建项目，在项
目预算方面，公司领导要求尽量节
省项目成本。因此，可能会造成项
目预算不足的情况。 
B32 
进度风险 
在项目准备阶段，可能存在环节较
多，层层审批的情况，受人为因素
和环境影响较大。在项目实施阶段，
可能存在未按照项目进度计划严格
执行，项目范围多次变更，项目管
理经验不足等情况。这些不确定性
因素，都可能影响项目进度。 
B33 
质量风险 
系统界面设计不符合公司要求，系
统代码编写不规范，系统测试不充
分，这些潜在的不确定性因素，都
会造成系统功能缺陷，达不到验收
标准。 
B34 
沟通风险 
项目沟通没有严格按照沟通计划执
行，存在信息沟通不及时，不通畅
的情况，可能会使管理者不能实时
掌握项目情况，造成项目不达标而
返工，项目工期延误等情况，对项
目影响严重。 
B4 
人员风险 
B41 
人员数量风
险 
由于L 公司信息技术中心可能同时
承建多个项目，导致运维管理系统
项目存在人员不足的情况。 
B42 
人员流动风
险 
项目建设骨干人员可能存在人事调
动、因个人原因休假或离职等情况。 
 
 
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-40- 
续表4.3 
类别编号 
类别名称 
风险编号 
风险名称 
说明 
B4 
人员风险 
B43 
人员能力风
险 
项目各环节人员，包括需求、管理、
测试、开发、运维等环节人员，可
能存在能力不足、经验不足的情况。 
B5 
安全风险 
B51 
网络安全风
险 
系统的研发、部署、访问均需要网
络的支持。网络安全是信息系统项
目的基础和保障，要保证网络通畅。 
 
B52 
数据安全风
险 
项目组人员无数据安全意识，导致
数据泄露；系统、数据库、管理后
台弱口令，导致数据被其他无权限
的人获取到；系统被黑客攻击，数
据被窃取等情况，均会对项目造成
影响。 
B53 
系统漏洞风
险 
系统整个框架设计存在安全漏洞，
导致系统被黑客攻击而无法使用。 
B54 
运行环境风
险 
系统运行环境中，存在因安全防御
性低导致病毒入侵；系统硬件服务
器性能不够，不足以承载大量访问。 
4.3 风险识别结果 
（1）需求风险 
    项目需求定义了信息系统项目所实现功能的范围，包括管理系统信息处理的
逻辑、硬件要求、运行要求、界面要求等内容。项目需求是项目建设的基础，项
目建设的后续工作都是基于项目需求来展开的。因此，一个具体且明确的项目需
求对项目的建设至关重要。 
L 公司运维管理系统项目在建设初期，只有初步的项目需求，需求不够详细，
很多功能只是大概描述一下，甚至有的功能一语概括。L 公司运维管理系统项目
是一个全省性的运维管理类项目，在对全省日常运维管理工作进行信息化时，需
要明确省、市级运维管理的日常工作，处理方法和逻辑。在市级层面，有14 个地
市分公司，各地市分公司的运维管理存在差异。需要在了解省、市运维管理工作
实际情况的基础上，统一全省运维管理工作的标准和方法，并与运维管理部门确
认项目需求，以保证项目的建设符合运维管理部门的实际需求。 
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-41- 
此外，项目需求提出方，对信息系统项目的需求不甚了解，需求提出不够专
业。项目组在与其对接时，许多功能的描述，需要跟需求提出方逐一确认，并引
导其进一步深入思考，进一步细化需求。在需求确认的整个过程，需要多次调研、
提炼、确认、讨论，最终在项目甲乙双方共同努力的基础上，完成项目需求。项
目需求工作，对后续项目影响较大。因此，该过程存在较大风险。该部分风险，
在本研究中归纳为需求调研风险。 
当然，即使需求确认完成后，随着项目的建设，需求提出方对项目需求的逐
步了解和深入，会产生一些新的想法和需求。这在信息系统项目建设中，很常见。
但是，如果对需求的变更把握不好，需求提出方的任何需求都满足的话，同样会
对项目产生重大影响。很可能会导致项目返工、成本增加、进度延后等情况，使
项目无法顺利完工。该部分风险，在本研究中归纳为需求变更风险。 
（2）技术风险 
    项目技术是指整个信息系统项目建设过程中，所采用的各种技术，包括技术
知识、技术方案、硬件装备、开发工具等。项目技术可行性是项目建设的前提条
件。在信息系统项目建设中，所采用的技术，对项目来说至关重要。在本项目中
所采用的技术主要分为硬件方面、软件方面。 
    在硬件方面，项目建设中所使用的计算机、服务器、打印机、手机，这些硬
件的配置，兼容性、稳定性，应达到系统开发和运行的要求。在系统的开发环境、
测试环境、正式环境，都需要这些硬件。在这些硬件基础上，进行开发、测试、
正式运行。开发所使用的计算机，应达到开发要求，并尽量配置高些。在测试环
境中，应尽量模拟实际的硬件环境。在正式环境中，系统的服务器应根据实际的
访问量，配置相应的技术指标的服务器，并配置备用服务器，保证系统可访问性、
运行的稳定性。这里，将硬件方面的技术风险总结为技术硬件风险。 
在软件方面，项目建设所使用的开发工具为MyEclipse2017，项目开发环境
中软件配置已较为成熟，并形成相关技术文档，该部分的风险，不在考虑范围内。
在信息系统项目中，软件技术的框架，所采用的计算机语言，这部分内容是十分
重要的。软件技术的选择决定了系统的安全性、稳定性，开发的方式、方法和效
率。在技术选择中，应选择成熟、稳定的技术方案，不应过分追求先进技术。但
也不应过分保守，使用落后、过时的技术。应根据实际情况出发，选择最适合的
技术方案。本研究中，将该部分软件风险总结为技术选择风险。 
在本项目开发中，所采用的是模块式开发，这样的开发模式，具有分工明确，
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-42- 
开发效率高的特点。在各模块开发完成后，需要将各模块功能进行系统集成。其
中，在前后台接口规范、软件数据交互方面，需要额外加以注意。前后台接口规
范，应予以统一，相关开发人员应及时沟通，保证相关功能能够跑通。在系统集
成后，系统数据是否交互正常，各环节系统数据结果是否符合管理逻辑，这些都
需要加以关注。本研究中，将该部分软件风险总结为系统集成风险。 
（3）管理风险 
    项目管理是依据项目建设目标、项目管理计划、项目实施计划、对项目建设
进行有效管理的过程。项目管理涉及多个方面，包括范围、成本、进度、质量、
人力资源、沟通、采购等。L 公司运维管理系统项目是省内自建项目，在项目立
项会议上，项目领导对项目提出了要求。希望该项目的建设能够利用较少的成本，
创造较大的效益。在项目实际建设过程中，如何进行成本可控又不影响项目建设，
是值得管理者深入思考的问题。本研究中，将该部分的风险总结为成本风险。 
    由于L 公司是一家大型国有企业的省级分公司，在体制上不是那么灵活，因
此，在项目立项、资源审批等项目阶段，可能存在环节较多，层层审批的情况，
并且受人为因素和环境影响较大。在项目实施阶段，项目能否按照项目进度计划
严格执行，是否按计划严格控制项目范围变更，项目管理者是否有足够经验把控
项目进度，这些不确定因素都为项目带来风险。本研究中，将该部分风险归纳为
进度风险。 
    L 公司信息技术中心在进行项目设计时，项目前台界面的设计，以及项目功
能的设计是否符合项目需求。在项目开发时，开发人员所编写的代码是否符合公
司软件开发标准规范，逻辑是否清晰，是否具有可读性。在项目测试阶段，测试
计划是否科学，测试是否充分，是否严格按照测试计划执行。以上在项目建设过
程中，项目质量方面的不确定性，会影响项目质量。因此，将该部分的风险归纳
为质量风险。 
    在项目实施前，应制定项目实施计划。项目实施计划中，包含了项目的沟通
计划。沟通，在项目管理过程中占有重要地位。在项目实施过程中，应严格按照
项目沟通计划来执行。如果没有制定科学的沟通计划，没有严格按照沟通计划来
执行，可能会造成信息沟通不及时，信息失真，管理者不能实时掌握项目的实际
情况，项目不达标，项目工期延误等情况，对项目影响严重。本研究中，将以上
风险因素归纳为沟通风险。 
（4）人员风险 
东北大学硕士学位论文                         第4章 L公司运维管理系统项目风险识别 
-43- 
L 公司信息技术中心主要负责L 公司信息化项目建设、运维等工作。在建设
L 公司运维管理系统项目的同时，还有其他工作内容。但运维管理系统项目是一
个全省性的重大项目，较为复杂。由于工期已定，有部分团队人员不是全职建设
本项目。因此，可能存在人员数量不足的风险。本研究中，将该部分因素，归纳
为人员数量风险。 
    由于项目工期较长，在项目建设期间，可能会出现团队成员因个人原因休假
的情况。此外，还存在团队成员出现人员调动、交流的情况。这些情况是客观存
在的，可能无法预料，且一旦发生很难避免。本研究中，将该部分人员风险归纳
为人员流动风险。 
    项目组织分为管理小组、需求小组、开发小组、运维小组、测试小组、质量
小组。各小组按照组织职能表中的计划分工，各司其职，在项目建设中发挥作用。
但是在项目组织人员拟定和规划时，应考虑人力资源中，员工的能力和经验。项
目组成员的能力和经验不足，会对项目建设带来影响。本研究中，将该部分人员
风险归纳为人员能力风险。 
（5）安全风险 
    对于信息系统项目来说，安全是第一位。系统的开发和运行，都是需要网络
的支持。一个安全、稳定的网络环境十分重要。网络的不稳定、不可用，会对项
目的开发和运行带来影响。因此，将网络因素所带来的风险归纳为网络安全风险。 
    项目建设过程中，可能会出现项目组人员无数据安全意识，而导致数据泄露；
系统、数据库、管理后台的弱口令，导致数据被其他无权限的人获取到；信息系
统被黑客攻击，数据被窃取。以上可能出现的安全风险因素，将其归纳为数据安
全风险。 
在项目技术选型中，所采用的技术可能存在漏洞，或者开发人员代码编写不
够规范，功能设计存在安全漏洞，将导致系统被黑客攻击而无法使用。针对以上
可能出现的安全风险因素，将其归纳为系统漏洞风险。 
在系统运行环境中，如果未安装防病毒软件或者安全防御性低，可能会导致
病毒入侵；如果系统硬件服务器性能不够，可能会导致服务器宕机，系统无法访
问。针对以上可能出现的安全风险因素，将其归纳为运行环境风险。
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-44- 
第5章  L 公司运维管理系统项目风险评价 
5.1 评价指标体系 
在第四章L 公司运维管理系统项目风险识别结果的基础上，构建项目风险评
价指标体系，如表5.1 所示。 
表5.1 风险评价指标体系 
Table 5.1 Risk evaluation index system 
项目名称 
一级指标体系 
二级指标体系 
L 公司运维管理系统
项目风险 A 
需求风险 B1 
需求调研风险 B11 
需求变更风险 B12 
技术风险 B2 
硬件风险 B21 
技术选择风险 B22 
系统集成风险 B23 
管理风险 B3 
成本风险 B31 
进度风险 B32 
质量风险 B33 
沟通风险 B34 
人员风险 B4 
人员数量风险 B41 
人员流动风险 B42 
人员能力风险 B43 
安全风险 B5 
网络安全风险 B51 
数据安全风险 B52 
系统漏洞风险 B53 
运行环境风险 B54 
5.2 基于层次分析法（AHP）评价项目风险权重 
5.2.1 建立结构模型 
根据上节构建的风险评价指标体系，并参考层次分析法的分析方法，构建L
公司运维管理系统项目的层次结构模型。该模型分为三层，即目标层、准则层、
方案层。目标层是模型的最高层，为L 公司运维管理系统项目总风险，用大写字
母A 表示；准则层是模型的中间层，为项目的一级子风险，具体是需求风险B1、
技术风险B2、管理风险B3、人员风险B4、安全风险B5；方案层是模型的最低
层，为项目的二级子风险，共分为16 项风险因素，用一级风险因素编号加序号的
方式表示。L 公司运维管理系统项目层次风险评价模型，如图5.1 所示。 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-45- 
图5.1 L 公司运维管理系统项目层次风险评价模型图 
Fig. 5.1 L Company Operation and Maintenance Management System Project Level Risk 
Assessment Model Diagram 
5.2.2 构建判断矩阵 
在构建判断矩阵中，本研究采用了项目风险概率和影响矩阵的方法，对项目
风险因素进行风险等级的评估。该方法将项目风险因素从概率和影响两个维度，
从非常高、较高、中、较低、非常低五个等级，进行综合分析。一般的，将风险
因素分高重要性，中重要性，低重要性。最后，采用专家打分法，依据专家对风
险因素的评估情况，按照项目风险打分表（附录A）进行打分，具体步骤如下： 
首先，邀请具有信息系统项目建设经验和管理经验的专家。同时，为了保证
项目风险打分的可靠性，邀请的专家，均参与L 公司运维管理系统项目建设工作，
对项目有一定的了解。具体人员包括管理小组2 人（含项目经理），需求小组2
人，开发小组2 人，运维小组1 人，测试小组1 人，质量小组2 人，共计10 人。 
其次，专家们采用项目风险概率和影响矩阵的方法，对项目风险进行综合分
析，将同层次风险因素，进行两两比较。专家打分的过程保持独立，不可相互交
流。 
最后，计算10 名专家打分的均值，四舍五入取整后，构建出项目的6 个判
断矩阵。 
（1）项目总风险判断矩阵，如表5.2 所示。 
表5.2 项目总风险判断矩阵表 
Table 5.2 Matrix of Total Risk Judgment of the Project 
项目总目标 
需求风险 
B1 
技术风险 
B2 
管理风险 
B3 
人员风险 
B4 
安全风险 
B5 
Wi 
需求风险 B1 
1 
1.5833 
0.55 
1.95 
1.667 
0.2254 
技术风险 B2 
0.6316 
1 
0.525 
1.6 
1.9333 
0.184 
管理风险 B3 
1.8182 
1.9048 
1 
2.87 
2.65 
0.3521 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-46- 
                                                                       续表5.2 
项目总目标 
需求风险 
B1 
技术风险 
B2 
管理风险 
B3 
人员风险 
B4 
安全风险 
B5 
Wi 
人员风险 B4 
0.5128 
0.625 
0.3484 
1 
1.75 
0.132 
安全风险 B5 
0.5999 
0.5173 
0.3774 
0.5714 
1 
0.1066 
①依据公式（2-1），求得判断矩阵中每一行的几何平均值。再依据公式（2-2）
进行归一化处理后，可得到项目一级风险指标权重向量
i
W =
（0.2254,0.184,0.3521,0.132,0.1066）。 
②依据公式（2-3），求得矩阵最大特征值
max

=5.0741。 
③依据公式（2-4），求得一致性指标CI： 
1
-
5
5
-
5.0741
1
ma




n
n
CI
x

=0.0185 
通过查询平均随机一致性指标表，可得RI=1.12。再依据公式（2-5），求得
CR: 
12
.1
185
0.0

RI
CI
CR
=0.0165 
因为，CR=0.0165<0.1，因此，通过一致性检验。 
（2）需求风险B1 判断矩阵，如表5.3 所示。 
表5.3 需求风险判断矩阵表 
Table 5.3 Demand Risk Judgment Matrix 
需求风险 B1 
需求调研风险 B11 
需求变更风险 B12 
Wi 
需求调研风险 B11 
1 
2.3 
0.697 
需求变更风险 B12 
0.4348 
1 
0.303 
①依据公式（2-1），求得判断矩阵中每一行的几何平均值。再依据公式（2-2）
进行归一化处理后，可得到项目一级风险指标权重向量
i
W =（0.697,0.303）。 
②依据公式（2-3），求得矩阵最大特征值
max

=2.000。 
③因为需求风险判断矩阵为二阶矩阵，查询平均随机一致性指标表可得RI
为0。因此，一致性比率为0，通过一致性检验。 
（3）技术风险B2 判断矩阵，如表5.4 所示。 
表5.4 技术风险判断矩阵表 
Table 5.4 Technical Risk Judgment Matrix 
技术风险 B2 
硬件风险 B21 
技术选择风险 B22 
系统集成风险 B23 Wi 
硬件风险 B21 
1 
0.26 
0.4117 
0.1345 
技术选择风险 B22 
3.8462 
1 
2.1 
0.5682 
系统集成风险 B23 
2.429 
0.4762 
1 
0.2973 
①依据公式（2-1），求得判断矩阵中每一行的几何平均值。再依据公式（2-2）
进行归一化处理后，可得到项目一级风险指标权重向量
i
W =
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-47- 
（0.1345,0.5682,0.2973）。 
②依据公式（2-3），求得矩阵最大特征值
max

=3.0089。 
③依据公式（2-4），求得一致性指标CI： 
1
-
3
3
-
3.0089
1
ma




n
n
CI
x

=0.0045 
通过查询平均随机一致性指标表，可得RI=0.52。再依据公式（2-5），求得
CR: 
0.52
0045
.0

RI
CI
CR
=0.0085 
因为，CR=0.0085<0.1，因此，通过一致性检验。 
（4）管理风险B3 判断矩阵，如表5.5 所示。 
表5.5 管理风险判断矩阵表 
Table 5.5 Matrix of Management Risk Judgment 
管理风险 B3 
成本风险 B31 
进度风险 B32 
质量风险 B33 
沟通风险 B34 
Wi 
成本风险 B31 
1 
0.425 
0.8083 
0.3733 
0.1386 
进度风险 B32 
2.3529 
1 
2 
0.7 
0.312 
质量风险 B33 
1.2372 
0.5 
1 
0.5917 
0.1801 
沟通风险 B34 
2.6788 
1.4286 
1.69 
1 
0.3693 
①依据公式（2-1），求得判断矩阵中每一行的几何平均值。再根据公式（2-2）
进行归一化处理后，得到项目一级风险指标权重向量
i
W =
（0.1386,0.312,0.1801,0.3693）。 
②依据公式（2-3），求得矩阵最大特征值
max

=4.0245。 
③依据公式（2-4），求得一致性指标CI： 
1
-
4
4
-
4.0245
1
ma




n
n
CI
x

=0.0082 
通过查询平均随机一致性指标表，可得RI=0.89。再依据公式（2-5），求得
CR: 
0.89
0082
.0

RI
CI
CR
=0.0092 
因为，CR=0.0092<0.1，因此，通过一致性检验。 
（5）人员风险B4 判断矩阵，如表5.6 所示。 
表5.6 人员风险判断矩阵表 
Table 5.6 Personnel Risk Judgment Matrix 
人员风险 B4 
人员数量风险 B41 人员流动风险 B42 人员能力风险 B43 Wi 
人员数量风险 B41 
1 
0.4833 
0.35 
0.1658 
人员流动风险 B42 
2.0691 
1 
0.5333 
0.3097 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-48- 
续表5.6 
人员风险 B4 
人员数量风险 B41 人员流动风险 B42 人员能力风险 B43 Wi 
人员能力风险 B43 
2.8571 
1.8751 
1 
0.5245 
①依据公式（2-1），求得判断矩阵中每一行的几何平均值。再依据公式（2-2）
进行归一化处理后，可得到项目一级风险指标权重向量
i
W =
（0.1658,0.3097,0.5245）。 
②依据公式（2-3），求得矩阵最大特征值
max

=3.0104。 
③依据公式（2-4），求得一致性指标CI： 
1
-
3
3
-
3.0104
1
ma




n
n
CI
x

=0.0052 
通过查询平均随机一致性指标表，可得RI=0.52。再依据公式（2-5），求得
CR: 
0.52
0052
.0

RI
CI
CR
=0.01 
因为，CR=0.01<0.1，因此，通过一致性检验。 
（6）安全风险B5 判断矩阵，如表5.7 所示。 
表5.7 安全风险判断矩阵表 
Table 5.7 Safety Risk Judgment Matrix 
安全风险 B5 
网络安全风
险 B51 
数据安全风险 
B52 
系统漏洞风险 
B53 
运行环境风险 
B54 
Wi 
网络安全风险 
B51 
1 
0.435 
0.4833 
0.465 
0.1315 
数据安全风险 
B52 
2.2989 
1 
1.3 
1.5833 
0.3468 
系统漏洞风险 
B53 
2.0691 
0.7692 
1 
1.5 
0.2923 
运行环境风险 
B54 
2.1505 
0.6316 
0.6667 
1 
0.2294 
①依据公式（2-1），求得判断矩阵中每一行的几何平均值。再依据公式（2-2）
进行归一化处理后，得到项目一级风险指标权重向量
i
W =
（0.1315,0.3468,0.2923,0.2294）。 
②依据公式（2-3），求得矩阵最大特征值
max

=4.0263。 
③依据公式（2-4），求得一致性指标CI： 
1
-
4
4
-
4.0263
1
ma




n
n
CI
x

=0.0088 
通过查询平均随机一致性指标表，可得RI=0.89。再依据公式（2-5），求得
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-49- 
CR: 
0.89
0088
.0

RI
CI
CR
=0.0098 
因为，CR=0.0098<0.1，因此，通过一致性检验。 
假设X 表示准则层占目标层权重，Y 表示方案层占准则层权重，Z 表示方案
层占目标层权重。依据公式
Y
X
Z


，可计算出方案层占目标层权重，得出各风
险因素权重占比，如表5.8 所示。 
表5.8 权重占比表 
Table 5.8 Weight Proportion Table 
一级指标 
权重占比X 
二级指标 
权重占比Y 
权重占比Z 
需求风险 B1 
0.2254 
需求调研风险 B11 
0.697 
0.1571 
需求变更风险 B12 
0.303 
0.0683 
技术风险 B2 
0.184 
硬件风险 B21 
0.1345 
0.0247 
技术选择风险 B22 
0.5682 
0.1045 
系统集成风险 B23 
0.2973 
0.0547 
管理风险 B3 
0.3521 
成本风险 B31 
0.1386 
0.0488 
进度风险 B32 
0.312 
0.1098 
质量风险 B33 
0.1801 
0.0634 
沟通风险 B34 
0.3693 
0.13 
人员风险 B4 
0.132 
人员数量风险 B41 
0.1658 
0.0219 
人员流动风险 B42 
0.3097 
0.0409 
人员能力风险 B43 
0.5245 
0.0693 
安全风险 B5 
0.1066 
网络安全风险 B51 
0.1315 
0.014 
数据安全风险 B52 
0.3468 
0.037 
系统漏洞风险 B53 
0.2923 
0.0311 
运行环境风险 B54 
0.2294 
0.0244 
5.2.3 评估结果权重排序 
依据上节得到的最终结果即权重占比表，将方案层各风险因素对目标层权重
占比大小，按由高到低进行排序，结果如表5.9 所示。  
表5.9 评估结果权重排序表 
Table 5.9 Ranking table of evaluation results weight 
备选方案 
权重 
排名 
需求调研风险 B11 
0.1571 
1 
沟通风险 B34 
0.13 
2 
进度风险 B32 
0.1098 
3 
技术选择风险 B22 
0.1045 
4 
人员能力风险 B43 
0.0693 
5 
需求变更风险 B12 
0.0683 
6 
质量风险 B33 
0.0634 
7 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-50- 
续表5.9 
备选方案 
权重 
排名 
系统集成风险 B23 
0.0547 
8 
成本风险 B31 
0.0488 
9 
人员流动风险 B42 
0.0409 
10 
数据安全风险 B52 
0.037 
11 
系统漏洞风险 B53 
0.0311 
12 
硬件风险 B21 
0.0247 
13 
运行环境风险 B54 
0.0244 
14 
人员数量风险 B41 
0.0219 
15 
网络安全风险 B51 
0.014 
16 
5.3 基于模糊综合评价法评估风险等级 
5.3.1 指标等级划分 
在本研究中，根据L 公司运维管理系统项目的风险因素对项目的影响程度，
将风险划分为五个等级。具体评价指标，如表5.10 所示。 
表5.10 风险等级评价表 
Table 5.10 Risk Level Evaluation Form 
风险等级 
低 
较低 
中 
较高 
高 
评价分值 
0.2 
0.4 
0.6 
0.8 
1 
5.3.2 模糊综合评价 
对L 公司运维管理系统项目中识别出的5 类16 项风险因素，依照风险等级
评价表，进行风险等级的模糊综合评价。 
在本环节中，为了得出客观性的结论，以及便于计算统计，邀请10 位专家
对二级项目风险因素进行打分。其中10 位专家均来自项目组，对本项目有足够的
了解，包括管理小组2 人（含项目经理），需求小组2 人，开发小组2 人，运维小
组1 人，测试小组1 人，质量小组2 人。 
本次专家打分按照设计的打分表（附录B）进行打分。然后，对提交上来的
打分表，按照专家评价某档次人数除以打分总人数的计算方式，进行统计汇总，
具体如表5.11 所示。 
 
 
 
 
 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-51- 
表5.11 模糊评价风险等级打分汇总表 
Table 5.11 Summary of Fuzzy Evaluation Risk Rating 
一级指标 
二级指标 
风险等级 
准则层 
权重 
占比 
方案层 
权重 
占比 
低 
较低 
中 
较高 
高 
需求风险 
B1 
0.2254 
需求调研风险 
B11 
0.697 
0.1 
0.1 
0.2 
0.4 
0.3 
需求变更风险 
B12 
0.303 
0.2 
0.2 
0.4 
0.2 
0 
技术风险 
B2 
0.184 
硬件风险 B21 
0.1345 
0.4 
0.4 
0.1 
0.1 
0 
技术选择风险 
B22 
0.5682 
0 
0.2 
0.3 
0.3 
0.2 
系统集成风险 
B23 
0.2973 
0.1 
0.3 
0.3 
0.2 
0.1 
管理风险 
B3 
0.3521 
成本风险 B31 
0.1386 
0.3 
0.4 
0.2 
0.1 
0 
进度风险 B32 
0.132 
0 
0.2 
0.3 
0.3 
0.2 
质量风险 B33 
0.1801 
0.2 
0.2 
0.3 
0.3 
0 
沟通风险 B34 
0.3693 
0 
0.2 
0.2 
0.4 
0.2 
人员风险 
B4 
0.132 
人员数量风险 
B41 
0.1658 
0.5 
0.4 
0.1 
0 
0 
人员流动风险 
B42 
0.3097 
0.2 
0.3 
0.3 
0.1 
0.1 
人员能力风险 
B43 
0.5245 
0 
0.1 
0.3 
0.4 
0.2 
安全风险 
B5 
0.1066 
网络安全风险 
B51 
0.1315 
0.4 
0.3 
0.2 
0.1 
0 
数据安全风险 
B52 
0.3468 
0.3 
0.3 
0.2 
0.2 
0 
系统漏洞风险 
B53 
0.2923 
0.3 
0.4 
0.3 
0 
0 
运行环境风险 
B54 
0.2294 
0.2 
0.2 
0.3 
0.2 
0.1 
由整理获得的模糊评价风险等级打分汇总表，可知各一级风险指标的模糊评
价矩阵。在层次分析法分析中，已获得各一级风险指标的权重向量。依据公式
R
W
B


，可计算出各一级风险模糊综合评价向量。 
最后，依据隶属度最大原则，得出各一级风险指标的模糊评价等级。具体计
算，如下： 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-52- 
（1）需求风险 
需求风险模糊评价矩阵： 
 
 
依据公式计算：  




2079
.0
3378
.0
2598
.0
1299
.0
1299
.0
303
.0
696
.0
1
1
1
1





R
R
W
B
 
再依据隶属度最大原则，确定需求风险模糊评价等级为较高风险。 
（2）技术风险 
技术风险模糊评价矩阵： 
 
 
 
依据公式计算： 
 
 
再依据隶属度最大原则，确定技术风险模糊评价等级为中风险。 
（3）管理风险 
管理风险模糊评价矩阵： 
 
 
 
 
依据公式计算：                          
 
 
再依据隶属度最大原则，确定管理风险模糊评价等级为较高风险。 
（4）人员风险 
人员风险模糊评价矩阵： 
 
 
 







0
2.0
4.0
2.0
2.0
3.0
4.0
2.0
1.0
1.0
1
R











0.1
0.2
0.3
0.3
0.1
0.2
0.3
0.3
0.2
0
0
0.1
0.1
0.4
0.4
2
R













0.2
0.4
0.2
0.2
0
0
0.3
0.3
0.2
0.2
0.2
0.3
0.3
0.2
0
0
0.1
0.2
0.4
0.3
3R




1363
.0
3092
.0
2492
.0
2277
.0
0.0776
0.3693
0.1801
0.312
0.01386
3
3
3
3





R
R
W
B




1434
.0
2434
.0
2731
.0
2566
.0
0.0835
0.2973
0.5683
0.1345
2
2
2
2





R
R
W
B











0.2
0.4
0.3
0.1
0
0.1
0.1
0.3
0.3
0.2
0
0
0.1
0.4
0.5
4
R
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-53- 
依据公式计算：                          
 
 
再依据隶属度最大原则，确定人员风险模糊评价等级为中风险。 
（5）安全风险 
安全风险模糊评价矩阵： 
 
 
 
 
依据公式计算： 
 
 
再依据隶属度最大原则，确定安全风险模糊评价等级为较低风险。 
5.3.3 项目整体风险评价 
在层次分析法中，得到项目一级指标权重向量： 
 
由上节中二级风险指标模糊综合评价，可得项目一级风险模糊评价矩阵： 
 
 
 
 
 
由公式
R
W
B


可计算出项目一级风险指标模糊评价向量： 
 
由隶属度最大原则，可确定该项目模糊综合评价风险等级为较高风险。 
5.4 评价结果分析 
在上节中，通过对项目一级风险的模糊综合评价，可得出该项目的风险等级
为较高风险。其中，需求风险权重为0.2254，风险等级为较高风险；技术风险权




1359
.0
2408
.0
2668
.0
2117
.0
0.1448
0.5245
0.3097
0.1658
4
4
4
4





R
R
W
B













0.1
0.2
0.3
0.2
0.2
0
0
0.3
0.4
0.3
0
0.2
0.2
0.3
0.3
0
0.1
0.2
0.3
0.4
5
R




0229
.0
1284
.0
2522
.0
3063
.0
0.2902
0.2294
0.2923
0.3468
0.1315
5
5
5
5





R
R
W
B


1066
.0
132
.0
3521
.0
184
.0
2254
.0

W

















0.0229
0.1284
0.2522
0.3063
0.2902
0.1359
0.2408
0.2668
0.2117
0.1448
0.1363
0.3092
0.2492
0.2277
0.0776
0.1434
0.2434
0.2731
0.2566
0.0835
0.2079
0.3378
0.2598
0.1299
0.1299
R


1416
.0
2753
.0
2587
.0
2173
.0
122
.0

B
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-54- 
重为0.184，风险等级为中风险；管理风险权重为0.3521，风险等级为较高风险；
人员风险权重为0.132，风险等级为中风险；安全风险权重为0.1066，风险等级为
较低风险。为了确保项目能够建设成功，应对以上项目风险分析结果给予充分的
重视。 
虽然，利用层次分析法和模糊综合评价法，确定了项目风险因素的权重和排
序，以及项目风险的模糊综合评价，但是，仍无法明确哪些风险因素是重要的。
接下来，根据以上分析结果，利用帕累托分析法对项目风险因素排序结果进一步
分析，从而确定项目关键性的风险因素。 
在本研究中，将由层次分析法得到的方案层权重排序结果为分析对象，对各
权重结果进行累加，从而确定本项目方案层风险因素的类别。具体分析，如表5.12
所示。 
表5.12 L 公司运维管理系统项目风险帕累托分析表 
Table 5.12 Pareto Analysis Table for Risk of Operation and Maintenance Management System 
Project of L Company 
备选方案 
权重 
累计概率 
风险类别 
需求调研风险 B11 
0.1571 
15.7% 
主要 
沟通风险 B34 
0.13 
28.7% 
进度风险 B32 
0.1098 
39.7% 
技术选择风险 B22 
0.1045 
50.1% 
人员能力风险 B43 
0.0693 
57.1% 
需求变更风险 B12 
0.0683 
63.9% 
质量风险 B33 
0.0634 
70.2% 
系统集成风险 B23 
0.0547 
75.7% 
成本风险 B31 
0.0488 
80.6% 
次要 
人员流动风险 B42 
0.0409 
84.7% 
数据安全风险 B52 
0.037 
88.4% 
系统漏洞风险 B53 
0.0311 
91.5% 
一般 
硬件风险 B21 
0.0247 
94.0% 
运行环境风险 B54 
0.0244 
96.4% 
人员数量风险 B41 
0.0219 
98.6% 
网络安全风险 B51 
0.014 
100% 
根据L 公司运维管理系统项目帕累托分析表，可得L 公司运维管理系统项目
风险帕累托分析图，能够更直观的展现项目风险的分析情况，具体如图5.2 所示。 
东北大学硕士学位论文                         第5章 L公司运维管理系统项目风险评价 
-55- 
 
图5.2 L 公司运维管理系统项目风险帕累托分析图 
Fig. 5.2 L Pareto Analysis Chart of Project Risk of Operation and Maintenance Management 
System of L Company 
由上面分析，可得出以下结论： 
（1）主要风险因素有8 种，分别是需求调研风险、沟通风险、进度风险、
技术选择风险、人员能力风险、需求变更风险、质量风险、系统集成风险。主要
风险因素，需要重点关注和应对 
（2）次要风险因素有3 种，分别是成本风险、人员流动风险、数据安全风
险。次要风险因素，也需要关注和防范，避免风险升级。 
（3）一般风险因素有5 种，分别是系统漏洞风险、硬件风险、运行环境风
险、人员数量风险、网络安全风险。一般风险因素，可在不影响项目建设的情况
下，给予一定程度上的关注。
0
0.2
0.4
0.6
0.8
1
1.2
0
0.05
0.1
0.15
0.2需求调研风险B11沟通风险B34进度风险B32技术选择风险B22人员能力风险B43需求变更风险B12质量风险B33系统集成风险B23成本风险B31人员流动风险B42数据安全风险B52系统漏洞风险B53硬件风险B21运行环境风险B54人员数量风险B41网络安全风险B51
L公司运维管理系统项目风险帕累托分析图
方案权重
累计权重
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-56- 
第6章  L 公司运维管理系统项目风险应对
策略及保障措施 
由层次分析法和模糊综合评价法，可得L 公司运维管理系统项目风险为较高
风险。为了保证项目顺利完工，需要根据项目风险评价结果，分别对项目的主要、
次要、一般风险因素制定相应的应对策略。在制定项目风险应对策略中，可以采
取减轻、预防、转移、回避、接受、储备，六种常见的应对策略。当然，在项目
的实施过程中，已识别项目风险因素发生的概率可能会发生变化，并且可能还会
出现新的未识别风险。因此，除了做好风险应对策略之外，还要加强项目风险监
控，时刻把握项目动态，及时反馈风险信息，以便迅速、有效地制定风险应对措
施。 
在完成项目风险应对策略的制定后，为了保障应对策略能够顺利实施，需要
根据项目的实际情况，制定项目风险应对策略的保障措施。 
6.1 项目风险应对策略 
6.1.1 主要风险应对策略 
在第五章的项目风险评价结果中，项目主要风险有：需求调研风险、沟通风
险、进度风险、技术选择风险、人员能力风险、需求变更风险、质量风险、系统
集成风险。项目主要风险对项目影响较大，应根据常见的风险应对策略，积极主
动地应对。下面就这8 类项目主要风险，制定具体的对策。 
（1）需求调研风险 
项目需求定义了信息系统项目建设的目的、功能范围、业务逻辑等内容，对
信息系统项目十分重要。一个明确的项目需求，可有效地降低信息系统项目的需
求风险。L 公司运维管理系统项目在建设初期，只有初步的项目需求，需求不够
详细，有些内容定义不清楚。因此，需要项目甲方和乙方共同努力，对项目需求
进行深入调研和确认。在L 公司运维管理系统项目需求调研过程中，采取以下措
施有助于预防项目需求调研风险，具体措施如下： 
①认真梳理项目需求 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-57- 
由甲方需求小组与项目乙方认真核对项目需求中的每一部分内容。对需求中，
有疑问的地方，做好标记；已明确的地方，要与乙方需求负责人再次确认。 
②调研不明确需求 
针对需求中，不明确、不具体的地方，由甲方需求小组与乙方负责人一起对
该功能所涉及的角色，进行面对面的访谈。 
③乙方全程协调参与 
在整个项目调研中，明确要求乙方需求负责人全程协调参与，防止出现相关
人员不配合的现象。 
④持续调研，直至问题解决 
规定项目小组在整理完成需求调研后，要跟乙方项目需求负责人进行书面确
认。如果需求调研有出入，不满足实际需求，则要继续调研，直至问题解决。 
⑤项目经理负责制 
调研过程，需要甲方需求小组与乙方需求小组召开多次会议，进行多次沟通、
讨论、确认。项目经理作为项目的管理者，需要全程参与项目需求会议，并负责
会议的主持、沟通、协调，听取相关人员的工作汇报，实时了解项目需求的进展
情况。 
⑥整理并确认最终需求 
访谈结束后，由项目需求小组将所涉及的项目需求进行整理。整理后，再与
乙方需求负责人进行书面确认，直至所有不明确、不具体的项目需求调研完成。 
⑦甲、乙方确认需求范围 
在完成整个需求调研过程后，由甲方项目需求小组整理一份完整的项目需求
说明书，交予项目经理与乙方。项目需求说明书经由项目经理和乙方审核后，由
项目经理组织召开项目需求确认会议。会议上，就项目需求达成一致，并进行书
面签字确认。 
（2）沟通风险 
L 公司运维管理系统项目需要项目组共同努力，才能顺利建设成功。在整个
项目建设过程中，需要团队成员间积极地沟通、协调、配合。由此可见，沟通对
信息系统项目建设来说，十分重要。在本项目中，为应对项目沟通风险所制定的
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-58- 
项目沟通管理计划主要包括两方面，分别是正式沟通和非正式沟通。其中，正式
沟通又包括日常沟通计划和基于问题的沟通计划。具体内容如下： 
①做好正式沟通 
在项目日常沟通计划中，建立日报、周总结、周计划、月汇报制度，使项目
管理者、主管领导、乙方等项目干系人能够及时了解项目实际情况。该措施有助
于预防项目沟通风险。具体内容，如表6.1 所示。 
表6.1 日常沟通计划 
Table 6.1 Daily communication plan 
沟通对象 
内容 
方式 
时间 
项目组成员 
汇报每日项目工作内容 
钉钉（网络） 
每日 
项目组成员 
本周工作总结、下周工作计
划 
会议 
每周 
主管领导、乙方 汇报项目进度、阶段性成果
情况 
会议 
每月 
在基于问题的沟通计划中，定义的问题类型主要有范围变更、系统集成、技
术测试、业务测试、计划变更、基线变更，并规定沟通的对象和沟通方式。在基
于问题的沟通中，遇到一般性问题时，可逐级汇报；如果遇到重大问题时，需要
直接向项目经理汇报情况，由项目经理判断如何处理。该措施有助于预防项目沟
通风险。具体内容，如表6.2 所示。 
表6.2 基于问题的沟通计划 
Table 6.2 Problem based communication plan 
问题类型 
沟通对象 
方式 
范围变更 
乙方、项目需求小组、变更控制委员会、
主管领导 
会议 
系统集成 
项目技术小组之间 
会议 
技术测试 
项目测试小组与项目技术小组之间 
会议 
业务测试 
项目测试小组、乙方、项目技术小组之
间 
会议 
计划变更 
项目经理、项目管理小组、项目变更控
制委员会 
会议 
基线变更 
项目经理、项目管理小组、项目变更控
制委员会 
会议 
②管理好非正式沟通 
非正式沟通具有传播速度快、范围广、效率高的特点。但是，非正式沟通会
存在信息失真、误解。同时，也可能会出现小圈子、小团体组织，影响项目组的
稳定和团队凝聚力。因此，需要管理好非正式沟通。为此，项目组可以建立工作
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-59- 
交流群，群里要有项目管理小组成员对项目工作交流群进行管理。在群公告中，
适时发布项目通知，以及一些管理要求，有助于预防项目沟通风险。 
(3)进度风险 
L 公司运维管理系统项目计划从2023 年5 月4 日开始，到2023 年12 月4 日
止，建设周期为7 个月。该项目是全省性的重点项目，主管领导十分重视，项目
工期紧，且较为复杂，存在一定的进度风险。为此，制定了以下应对措施，具体
内容如下： 
①制定科学的项目进度管理计划 
为了保证项目能够按时完工，在项目立项后，由项目经理邀请L 公司信息技
术中心技术专家和项目组技术骨干，根据项目实际情况，就项目进度管理计划进
行讨论。经过技术专家和项目组一致确认后，制定出科学的项目进度管理计划，
交主管领导审核。项目进度管理计划的内容包括项目关键里程碑、各工作先后顺
序安排、项目进度安排。该措施有助于预防项目进度风险。 
②建立工作负责人制度 
项目经过科学的讨论，确定共分为11 个阶段，每一阶段都由项目经理确定负
责人。项目阶段工作负责人需要每周向项目经理汇报进度情况。该措施有助于预
防项目进度风险。 
③建立项目管理小组  
项目经理在制定项目组织结构时，成立项目管理小组，负责对项目进度的跟
进，监督项目进度是否按照计划进行。该措施有助于预防项目进度风险。 
④研究并制定补救计划 
如果出现项目进度延期的情况，需要向项目经理说明情况。由项目经理组织
项目团队，分析原因，并制定紧急的补救计划，加快项目进度，使其尽快回到正
常进度计划上来。该措施有助于减轻项目进度风险。 
⑤加强关键里程碑的绩效考核 
将项目阶段性工作进度纳入项目绩效考核中，以强调项目进度的重要性。该
措施有助于预防项目进度风险。 
⑥灵活应对进度滞后问题 
由于L 公司运维管理系统项目建设周期长，功能较多且复杂，如遇问题导致
项目进度滞后，为保证系统上线目标，可在征得项目相关干系人同意的基础上，
灵活变通。对已确定的，且着急的功能，可以先开发。其他无关紧要的功能，可
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-60- 
在项目二期予以建设。该措施是在接受项目进度风险后，制定的灵活应对措施。 
（4）技术选择风险 
信息系统项目建设需要运用信息技术来实现。项目建设所采用的技术，在很
大程度上决定了项目的成败。因此，技术选择风险对项目影响较大。为此，制定
了以下应对措施，具体内容如下： 
①组织技术研讨 
项目经理要组织项目技术小组，并邀请信息技术中心的技术专家参与讨论，
广泛听取专家和技术骨干的意见和建议。该措施有助于预防技术选择风险。 
②综合考虑技术方案 
在项目技术选择上，要以项目的需求，项目的实际情况出发，并借鉴信息技
术中心已开发项目。选择的技术不应是过时的，也不能是新的且不成熟的，要选
择合适的技术。要充分考虑技术的兼容性和扩展性，为以后系统的建设留有余地。
此外，还要注意技术的安全性。技术方案是否漏洞多，存在安全隐患。对于漏洞
多的技术方案，坚决不能采纳。该措施有助于预防技术选择风险。 
③科学制定技术规范 
在技术规范的制定上，要充分考虑国家行业标准，集团公司软件开发标准，
制定符合本项目的软件开发规范。技术规范可包括前台界面设计、后台代码、数
据库设计等内容。在规范项目开发的同时，也为后期项目的测试、质量审核、运
维，提供方便。该措施有助于预防技术选择风险。 
（5）人员能力风险 
信息系统项目建设离不开项目团队，项目团队又是由人员组成的。项目经理
在制定项目组织结构时，需要考虑人员能力问题。为了有效应对人员能力风险，
制定了以下应对措施，具体内容如下： 
①综合考虑人员任用 
项目经理在划分各小组时，应充分地考虑每位人员的能力和特长，将合适的
人放到合适的位置上，使其发挥最大的作用。在每个小组负责人人选上，选择具
有较强工作能力，且具有一定管理经验的人担任。每位小组负责人需要带领一位
或多位人员，组成职能小组，共同完成小组的职能工作。项目小组组员，在工作
能力和经验上，可以存在一定的不足，但要求具有认真的学习态度，有责任心、
集体意识和团队精神。该措施有助于预防人员能力风险。 
②建立学习型组织 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-61- 
为了在项目建设中加强人才培养，提高项目组成员个人能力，可在项目组建
立学习型组织，营造浓厚的学习氛围。小组负责人在项目建设过程中，对本小组
成员的工作给予一定的指导和帮助。小组成员也应多向小组负责人学习和请教，
以在项目实践中快速提高个人工作能力。该措施有助于预防和减轻人员能力风险。 
③加强员工能力培训 
在项目建设中，项目经理可在不影响项目进度的情况下，适时组织项目组成
员，进行相关职能工作的培训。除此之外，项目小组负责人也可组织项目小组成
员，对本小组职能工作所需的技能，进行培训，以便更好的完成本小组工作。该
措施有助于预防和减轻人员能力风险。 
（6）需求变更风险 
在项目建设过程中，总会出现影响力高的项目干系人，随意对项目提出变更，
对项目基准造成影响。如果对需求变更处理不好，乙方的任何需求都满足的话，
很可能会造成大量的需求变更堆积，使项目失败。为此，制定了以下应对措施，
具体内容如下： 
①成立项目变更控制委员会 
针对本项目可能存在需求变更的情况，首先由项目经理组织成立变更控制委
员会。委员会由项目甲、乙方领导，项目经理、项目需求小组负责人、项目管理
小组负责人，共5 人组成。项目变更控制委员会，负责项目变更的评估和评审工
作。该措施有助于预防需求变更风险。 
②制定完善的变更控制流程 
由项目经理制定变更控制流程。在项目干系人向项目组提交变更申请时，先
由项目经理对变更进行初审。初审通过后，对于不影响项目基准的变更，项目经
理可自主决策，是否进行变更；但是对于影响项目基准的变更申请，一定要经过
变更控制委员会的审批。最后，任何需求变更都要严格按照变更流程来进行，以
减少需求变更的风险。此外，项目经理要严格控制变更，对于任何变更申请的提
出都要及时做出反映，不能延误，以免造成工期延误和资源浪费。该措施有助于
预防需求变更风险。具体项目变更流程，如图6.1 所示。 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-62- 
 
图6.1 项目变更流程图 
Fig. 6.1 Project Change Flow Chart 
（7）质量风险 
由于L 公司运维管理系统是一项全省性的重点项目，因此，项目的质量非常
重要。为此，制定了以下应对措施，具体内容如下： 
①设置项目质量小组 
为了保证项目质量，项目组成立项目质量小组。并设置了质量小组组长，负
责项目质量的监督和检查工作。该措施有助于预防质量风险。 
②规范监督检查内容 
监督和检查的内容主要包括计算机、服务器等硬件是否符合要求，开发、测
试等环境是否符合要求，项目是否按计划实施，各阶段性成果是否符合项目需求，
开发小组所编写的代码是否符合规范要求，项目各阶段所形成的文档是否规范，
是否与实际相符等内容。该措施有助于预防质量风险。 
③规定质量汇报 
质量小组要以周和月为单位，对项目进行检查。并将质量检查结果以书面质
量检查报告的形式，向项目经理汇报情况。如遇重大质量问题，应及时向项目经
理汇报情况。该措施有助于预防质量风险。 
④建立项目经理负责制度 
如遇项目质量问题，由项目经理通知相关负责人对质量问题进行整改。如果
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-63- 
出现的质量问题比较重大，且不容易解决，则由项目经理组织各小组负责人和技
术骨干，对问题进行讨论，制定问题的解决方案。该措施有助于预防和减轻质量
风险。 
（8）系统集成风险 
由于L 公司运维管理系统项目是模块式开发的，因此涉及到软件系统集成。
在系统集成方面，如果没有制定好计划，没有做好规范和分工，就会给项目开发
带来很大的影响。为此，制定了以下应对措施，具体内容如下： 
①指定专人负责 
项目经理指定技术小组负责人对系统集成负责。技术小组负责人要做好分工
和人员分配，以保证系统能够顺利集成。该措施有助于预防系统集成风险。 
②规范性开发 
由项目技术小组负责人做好项目软件开发的分工，规定前后台接口规范。要
求所有开发人员涉及到前后台交互时，都应按照接口规范进行开发。该措施有助
于预防系统集成风险。 
③充分测试 
功能模块开发完成后，先由技术小组进行技术测试。通过后，再由测试小组
进行联调测试。测试通过后，向SVN 版本控制服务器提交代码，形成最终代码。
该措施有助于预防系统集成风险。 
6.1.2 次要风险应对策略 
在第五章的项目风险评价结果中，项目次要风险有：成本风险、人员流动风
险、数据安全风险。项目次要风险虽然没有项目主要风险影响那么大，但是对项
目还是具有一定影响，也应该根据常见的风险应对策略，予以积极主动的应对。
下面就这3 类项目次要风险，制定具体对策。 
（1）成本风险 
L 公司运维管理系统项目是省内自建项目，项目经费有限。为了有效地控制
项目成本，制定了以下应对措施，具体内容如下： 
①项目经理负责制 
项目经理对项目成本负责。项目经理要严格把控项目资金的使用，项目任何
成本的支出都要经项目经理审核，重大成本支出还需要项目领导小组审批。该措
施有助于预防项目成本风险。 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-64- 
②坚持合理的控制原则 
项目实施过程中，要减少不必要、不合理的支出，以节约项目成本。要制定
合理的就餐、出差标准，不提倡加班，严控加班费用支出。该措施有助于预防项
目成本风险。 
③加强其他环节管理 
项目成本与项目测试、质量、进度环节有一定的关联性。因此，要加强项目
测试、质量、进度方面的管理，防止因项目测试不充分、项目质量不达标、项目
进度延误而造成项目的返工和延期等情况，从而增加项目成本的支出。该措施有
助于预防项目成本风险。 
（2）人员流动风险 
对于大部分私企软件公司来说，人员流动比较普遍。对于L 公司这样的国有
企业来说，人员相对稳定，不会频繁的流动。但是，也有特殊的情况，例如人力
部门制定的员工交流计划和平级调动计划、上级集团公司人员借调计划、员工个
人的休假计划、员工辞职等因素。人员流动的不确定性因素，会给项目建设带来
影响。为此，制定以下应对措施，具体内容如下： 
①积极向领导争取 
项目经理要跟L 公司领导争取，保证项目组成员的稳定。在项目实施过程中，
如遇人力部门制定的人员交流计划和平级调动计划、上级集团公司人员借调计划
的情况，应考虑从项目组之外选择。该措施有助于预防人员流动风险。 
②事前充分考虑、事后重点强调 
项目经理制定项目组织结构时，应充分考虑人员流动因素。在项目组织结构
确定后，召开会议，强调由于项目的重要性和特殊性，项目组成员要非必要不请
假。该措施有助于预防人员流动风险。 
③项目经理沟通协调 
在项目实施过程中，如果遇到项目人员流动的情况，项目经理应首先要与当
事人积极沟通，尽量避免人员流动。该措施有助于减轻人员流动风险。 
④补充新人加入 
如果人员流动不可避免，应从减轻的角度，跟领导沟通，选择有能力、有经
验的员工补充到项目组中。该措施有助于减轻人员流动风险。 
（3）数据安全风险 
针对系统数据安全，主要从制度建设和系统防范两方面入手，以达到预防数
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-65- 
据安全风险的目的。具体措施如下： 
①建立数据安全保密制度 
在制度建设方面，制定项目组数据安全保密制度。要求项目组成员对项目所
涉及的数据保密，不得传播与项目有关的企业数据。倡导员工爱岗敬业，遵守职
业道德。在必要情况下，可签署保密协议。 
②加强系统安全 
在系统防范方面，防止系统、数据库弱口令，防范系统漏洞，增加系统用户
登录校验功能，做好系统日志，记录用户操作。 
6.1.3 一般风险应对策略 
在第五章的项目风险评价结果中，项目的一般风险有：系统漏洞风险、硬件
风险、运行环境风险、人员数量风险、网络安全风险。项目一般风险发生的概率
较小或对项目影响较轻，在项目主要风险和次要风险之外，可采取较小的成本加
以适当的关注和预防。下面就项目一般风险，制定具体的对策。 
（1）系统漏洞风险 
为了应对系统漏洞风险，制定以下应对措施： 
①选择合适技术 
在项目的技术选择上，应该将该因素充分加以考虑。应根据项目的实际情况，
邀请L 公司信息技术中心技术专家参与讨论，并根据信息系统项目建设经验，确
定较为成熟、安全的技术。该措施可以预防系统漏洞风险。 
②规范开发、全面排查 
在系统开发阶段，要注意代码编写，防止因代码编写不规范而造成的系统漏
洞。在系统开发完成后，可用专业的工具对系统代码进行扫描。针对扫描出的问
题，应逐一排查和解决。该措施可以预防系统漏洞风险。 
③定期扫描和修复 
在系统运维阶段，需要定期进行系统漏洞扫描和修复。该措施可以预防和减
轻系统漏洞风险。 
④采取物理网络隔离 
可以采用物理网络隔离的方法，以防止因系统漏洞导致网络攻击、数据泄露、
黑客攻击等问题。该措施可以回避系统漏洞风险。 
（2）硬件风险 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-66- 
项目中的硬件包括计算机、服务器、打印机、手机。在项目实施及系统运行
过程中，对这些硬件的配置、性能、稳定性，有一定的要求。为了预防系统在开
发及后期运行时的硬件风险，项目经理在制定项目计划时，要对项目中所使用的
硬件参数、性能、稳定性，提出具体要求。 
（3）运行环境风险 
为了预防运行环境风险，制定以下应对措施： 
①配置合理的硬件设备 
在硬件方面，要从系统运行和用户访问量两方面综合考虑，配置合理的硬件
参数。 
②谨慎选择服务器系统 
在服务器安全方面，建议使用安全性和稳定性较好的Linux 系统。如果在
Windows 系统上运行，一定要做好服务器的安全策略。 
③严禁外接存储设备 
严禁计算机、服务器上插U 盘等外接存储设备，防止服务器感染病毒或数据
泄露。 
④采取网络物理隔离 
采用内外网隔离方式，从源头上保证系统运行环境安全。 
（4）人员数量风险 
针对项目组人员数量问题，为了预防人员数量风险，制定以下应对措施： 
①合理评估人员数量 
项目经理要根据项目的工期、工作量、重要性等方面，对项目所需人力资源
进行合理评估。 
②积极向公司领导争取 
评估完成后，项目经理要向公司领导说明情况，尽力争取较为合适的人员数
量，为项目的建设争取适当的人力资源。 
（5）网络安全风险 
可以从技术和人员两个方面采取措施。为了预防网络安全风险，制定以下应
对措施： 
①采取网络冗余 
在技术方面，可以对重要网络线路采取“一主一备”的冗余方式进行设计，
以保证系统网络稳定畅通。 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-67- 
②专人管理和维护 
在人员方面，项目经理可以从运维小组中安排专人，对网络进行管理和维护。 
L 公司运维管理系统项目十分重要且较为复杂，在项目实施过程中要不断地
进行风险监控。要监控已识别项目风险应对策略的控制情况，原有风险是否得到
了减轻或解决。同时，还要注意在解决已识别风险的过程中，是否有新的风险产
生。在项目实施的整个过程中，项目组要时刻做好项目风险监控，不断地进行风
险识别、分析、应对、监控，不断地循环往复，直至项目顺利完成。 
6.2 项目风险管理保障措施 
在对L 公司运维管理系统项目的主要、次要、一般风险制定应对策略后，为
了保障项目风险管理的顺利进行，本节主要从六个方面提出相应的保障措施。具
体包括组织保障、制度保障、文化建设、人员保障、资金保障、沟通保障。 
（1）组织保障 
项目的组织战略，对项目的建设至关重要。一个明确的组织战略，有助于项
目实施的顺利开展。为此，制定了以下组织保障措施： 
①明确组织战略目标 
项目的组织战略为项目的建设指明了目标和发展方向。L 公司运维管理系统
项目的组织战略就是为了顺利完成项目建设工作，以提高全省运维管理水平，提
升运维质量和效率。项目组织战略的首要目的就是为了顺利完成项目的建设工作，
即按工期要求，保质保量的完成项目建设工作。 
②将项目风险管理纳入项目组织战略中 
在项目建设过程中，项目风险是不可避免的，做好项目风险管理，可以有效
地保障组织战略目标的实现。因此，需要将项目风险管理纳入项目组织战略中，
并加以重视。 
（2）制度保障 
在信息系统项目建设中，一系列的管理工作，需要管理制度来保障。项目管
理制度十分重要，为此制定了以下制度保障措施： 
①建立项目风险管理常态化机制 
在信息系统项目中，项目风险管理不是一次性的，它是一个循环往复的过程，
直至项目建设完成。因此，有必要建立风险评估的常态化机制。项目组要从项目
建设的日常工作抓起，保持项目风险管理常态化。要不断地循环项目风险管理的
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-68- 
过程，即风险识别、分析、应对、监控。在项目风险管理中，项目经理为项目风
险管理的主体，项目组成员全员参与。 
②建立激励机制 
从项目管理的角度，建立激励机制，对项目风险管理也起到一定的辅助保障
作用。在激励机制方面，要对在项目建设中，有突出贡献的员工进行激励。激励
可采用精神鼓舞和物质奖励并举的方法，以公平、公正、公开为原则，努力营造
一个团结、和谐、积极向上的组织氛围。 
③建立绩效考核制度 
从项目管理的角度，建立绩效考核制度，对项目风险管理也起到一定的辅助
保障作用。在绩效考核方面，应根据日报、周报、月报、周例会、重点会议的汇
报情况，项目重要里程碑完成情况，以及日常表现情况，综合评估。要保证绩效
考核的公平、公正，以对项目的建设起到正向激励作用。 
④将工作任务和责任落实到个人 
在项目工作中，根据WBS 工作分解结构，将工作逐层分解，落实到个人。
该措施，能够使项目组成员明确自己的工作职责，可防止因职责划分不清，而影
响项目建设。同时，项目经理可据此对项目组成员进行绩效考核，可对项目的建
设起到正向激励作用。 
（3）文化建设 
项目风险管理不是项目经理一个人的事，是项目组共同的责任。培育组织内
项目风险管理文化，可以让项目组成员认识到项目风险管理的重要性。营造良好
的项目风险管理文化氛围，有助于项目风险管理工作的开展。为此，制定了以下
文化建设方面的保障措施： 
①营造风险管理文化氛围 
积极争取领导层的关注和支持，通过定期开展项目风险管理知识培训，以管
理层人员带动项目组成员，促进项目风险管理文化的培育。 
②树立正确的项目风险管理理念 
在培育项目风险管理文化中，要树立正确的项目风险管理理念。在本项目中，
总结了六条项目风险管理理念，可为项目风险管理文化建设提供指导。具体如下： 
一是重视项目风险。项目风险是在信息系统项目建设中，普遍存在的。它可
能会为项目建设带来消极和积极的影响，是不能被忽视的。因此，需要重视项目
风险。 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-69- 
二是坚持全员参与。项目风险可能存在于项目建设的各个环节中，与项目组
成员息息相关，它是组织的共同责任。此外，充分发挥集体的力量，对项目风险
的识别、分析等项目管理工作，有很大的帮助。 
三是综合分析评估。在项目风险中，许多风险不是孤立的。风险在项目中可
能存在关联和转化，有些风险还可能跨越项目的多个阶段。因此，对项目风险应
综合分析评估，不能局限于一个环节中。 
四是动态风险管理。项目风险管理不是一次性的，随着项目的实施，一些新
的项目风险会逐渐识别出来。当识别出新的风险因素时，需要进行风险分析、应
对。在整个项目建设过程中，还要对项目风险进行监控，既包括对新风险的识别，
也包括对风险应对情况的监控。整个项目的风险管理，应坚持动态管理。 
五是采取有效应对。在项目风险应对上，应采取有效的应对措施。有效的措
施需要具有可行性、适用性、经济性、合理性，且措施是及时的、系统的，执行
效果不偏离项目风险管理的目的。在风险措施的制定上，可采取参考项目风险管
理经验，咨询项目专家组，召开项目组会议讨论等办法。 
六是形成知识资产。项目组需要将项目建设过程中所积累的方法、经验、教
训进行总结，建立信息系统项目风险知识库，为以后L 公司信息技术中心项目建
设提供参考和借鉴。 
（4）人员保障 
在信息系统项目建设中，一系列的管理工作，需要项目人员来保障。项目人
员十分重要，为此制定了以下人员保障措施： 
①争取领导层的支持 
对于项目建设来说，保持项目组成员可用和稳定，十分重要。如果存在现有
人力资源无法满足项目风险管理需求的情况，可以考虑引进专业人才，以增强项
目风险管理。因此，对于人员保障方面，要积极争取领导层的支持，获得充足且
稳定的人力资源。 
②提升人员能力 
在风险管理人员能力上，可以采取“老带新”的方法，即能力强的老员工带
领能力不足的新员工，以弥补人员能力上的不足。也可以根据项目风险管理所需
的技能，组织专门的培训，提升项目组成员的风险管理能力。培训的内容，主要
包括项目建设所需的技术，信息系统项目管理知识等内容。 
③设置AB 角 
东北大学硕士学位论文           第6章 L公司运维管理系统项目风险应对策略及保障措施 
-70- 
为了应对人员变动对项目风险管理造成的影响，除了项目经理沟通协调外，
还可以考虑在重要岗位上，设置AB 角色，把项目人员方面的风险降到最低。 
（5）资金保障 
在信息系统项目建设中，项目的建设需要资金来支持。项目资金十分重要，
为此，制定了以下资金保障措施： 
①加强成本管理 
明确成本预算相关责任人，严格执行财务审批流程，对重点工作，要专款专
用，实行独立核算。同时，要加强对资金使用效果的监督。要在事前严格控制预
算，事中执行审批流程，事后监督使用效果。 
②设置风险储备金 
针对项目风险管理，要按一定比例设置项目风险储备金，以保证有充足资金
开展项目风险应对措施。其中，应急储备金由项目经理进行管理和控制，管理储
备金则需要项目领导小组审批通过后，方可使用。 
（6）沟通保障 
为了保障项目风险管理工作的顺利开展，需要制定项目沟通管理计划。为此，
制定了以下沟通保障措施： 
①制定合理的沟通制度 
主要是建立项目经理负责制，管理小组协助制。项目风险管理的主要负责人
是项目经理，一切重大的风险问题，均要第一时间向项目经理汇报。项目管理小
组负责协助项目经理开展项目风险管理工作。 
②规定适当的沟通方式 
在沟通的方式上，规定日报、周报使用钉钉软件上的固定模板进行汇报；月
报采用Word 或者PPT，以会议的方式进行汇报；重大风险问题的紧急汇报，则
要采用电话汇报的方式。 
东北大学硕士学位论文                                            第7 章 结论与展望 
-71- 
第7章  结论与展望 
7.1 结论 
本文主要根据项目风险管理理论知识，并结合L 公司运维管理系统项目的实
际情况，研究如何科学、有效地对信息系统项目风险进行管理。在本研究中，首
先采用文献研究法、头脑风暴法、定性分析法对项目进行风险识别；然后，依据
风险识别结果，对项目风险进行分析。首先，构建本项目的风险评价指标体系；
应用层次分析法，得到项目中各风险因素的指标权重；应用模糊综合评价法，得
到本项目的风险等级；应用帕累托分析法，得出本项目的主要风险因素、次要风
险因素、一般风险因素。最后，根据项目风险分析的结果，针对项目的主要、次
要、一般风险，制定完善的风险应对策略和保障措施，以保证项目顺利实施。总
结全文，本研究主要有以下成果： 
（1）在L 公司运维管理系统项目风险识别中，共识别出5 类16 项风险因素。
依据风险识别结果，构建项目风险指标评价体系，具体分为目标层、准则层、方
案层。其中，准则层为一级风险指标因素，具体包括：需求风险、技术风险、管
理风险、人员风险、安全风险。方案层为二级风险指标因素，具体包括需求调研
风险、需求变更风险、硬件风险、技术选择风险、系统集成风险、成本风险、进
度风险、质量风险、沟通风险、人员数量风险、人员流动风险、人员能力风险、
网络安全风险、数据安全风险、系统漏洞风险、运行环境风险。 
（2）应用层次分析法，得出本项目各风险因素的指标权重。应用模糊综合评
价法，得出一级、二级风险指标的风险等级。L 公司运维管理系统项目整体风险
为较高风险。在项目一级风险指标中，需求风险为较高风险，技术风险为中风险，
管理风险为较高风险，人员风险为中风险，安全风险为较低风险。由分析得出结
论，应对L 公司运维管理系统项目风险管理加以重视。 
（3）应用帕累托分析法，依据层次分析法所得风险因素权重排序结果，进行
分析。得出以下结论：主要风险因素有需求调研风险、沟通风险、进度风险、技
术选择风险、人员能力风险、需求变更风险、质量风险、系统集成风险，需要重
点关注和应对。次要风险因素有成本风险、人员流动风险、数据安全风险，也需
要关注和防范，避免风险升级。一般风险因素有系统漏洞风险、硬件风险、运行
环境风险、人员数量风险、网络安全风险，应在不影响项目建设的情况下，加以
东北大学硕士学位论文                                            第7 章 结论与展望 
-72- 
适当的关注。 
（4）根据L 公司运维管理系统项目风险分析结果，采取减轻、预防、转移、
回避、接受、储备，六种常见的风险应对策略。针对主要、次要、一般三类风险
因素，制定科学合理的风险应对策略，并根据风险因素的重要程度，予以不同程
度的应对，保证了项目顺利实施。 
（5）从组织保障、制度保障、文化建设、人员保障、资金保障、沟通保障六
个方面，制定完善的项目风险保障措施，保障项目风险管理的顺利实施。 
7.2 展望 
由于作者研究水平有限，在本文项目风险管理研究中，存在以下不足之处。 
在论文的撰写方面，虽然在导师的指导下，通过查阅大量文献资料，经过认
真地分析，反复地修改，最终完成了论文的撰写；但是，通过对论文内容的审阅，
还是发现存在一些措词不够严谨，逻辑思维混乱的地方。本人会对论文进行持续
改进，争取做到完美。 
在风险识别中，采用了文献研究法、头脑风暴法和定性分析法。头脑风暴法
使用起来比较简单，但在使用过程中，容易受环境因素所干扰，且参与头脑风暴
专家的水平，会对头脑风暴结果有一定影响。本研究中，共识别出16 个项目风险
因素，根据定性分析法，将其划分为5 类。综合风险识别方法的缺点，风险识别
的结果可能具有一定的局限性。以后，将重点研究项目风险识别的方法，使项目
风险识别更科学、全面。 
在风险分析中，层次分析法和模糊综合评价法的应用，主要是采用专家打分
的方法。可能会存在主观作用较强，导致分析结果存在偏差。以后，将在工作之
余，继续学习和借鉴一些更好的分析方法，使得项目风险分析结果更科学、适应
性更强。 
在制定项目风险应对措施中，个别风险因素的应对措施可能不够全面。后期，
将会重点研究一些科学的方法和先进的管理工具，应用于项目管理中，以预防和
减轻项目风险。 
最后，希望广大专家和学者在信息系统项目风险管理方面，探索出更科学、
更完善的理论、技术和方法，为我国信息系统项目建设做出更大的贡献。 
东北大学硕士学位论文                                                     参考文献 
-73- 
参考文献 
[1] 胡伟. 基于移动APP 创新邮政信息系统运维模式探讨[J].邮政研究，2017，第
33 卷（第1 期）：9-12. 
[2] 张朋，徐洪敏. 浅谈新时期运维管理[J]. 现代经济信息.2016，NO.21：125. 
[3] 焦健，周卡达. IT 服务管理在邮政行业的实践[J]. 电子政务，2010，NO.Z1：
63-66. 
[4] 张红梅. 论企业信息系统的运维体系建设[J]. 中国管理信息化，2021，NO.09：
117-119. 
[5] 张剑. 信息系统安全运维[M]. 成都：电子科技大学出版社，2016，2-34. 
[6] 苗辉. 试论邮政信息技术运行维护[J]. 科技资讯，2017，NO.01：120-121. 
[7] 王刚. 邮政信息王安全教育研究[J]. 管理工程师，2016，NO.01：22-26. 
[8] 唐文雄. 邮政信息网病毒安全防范策略[J]. 邮政研究，2013，第29 卷（NO.3）：
15-16. 
[9] 杨海荣. 邮政概论[M]. 北京：北京邮电大学出版社，2002，5-20. 
[10] 王为民，周晓燕. 邮政企业经营管理务实[M]. 北京：北京邮电大学出版社，
2016.5，1-8. 
[11] 严琴. HB 邮政IT 服务管理研究[D]. 武汉：华中科技大学，2013. 
[12] 李宏伟，魏勇. 强化运维管理 改进运维手段 多措并举提高IT 服务水平[J]. 
通信管理与技术，2016，NO.03：50-53. 
[13] 侯毅，孙波. IT 运维管理解决方案[J]. 信息安全与技术.2011，NO.11：58-60. 
[14] 王志丰，戴延峻. 利用“两库”资源,提高IT 服务水平[J]. 现代邮政，2012(3)：
48-50.  
[15] Taylan O, Bafail A O, Abdulaal R M S,  Kabli M R. Construction projects 
selection and risk assessment by fuzzy AHP and fuzzy TOPSIS methodologies[J]. 
APPLIED SOFT COMPUTING, 2014, 17: 105-116. 
[16] Aragones-Beltran P, Chaparro-Gonzalez F, Pastor-Ferrando J P, Pla-Rubio A. An 
AHP 
(Analytic 
Hierarchy 
Process)/ANP 
(Analytic 
Network 
Process)-based 
multi-criteria decision approach for the selection of solar-thermal power plant 
investment projects[J]. ENERGY, 2014, 66: 222-238. 
[17] Aven T. Risk assessment and risk management: Review of recent advances on 
their foundation[J]. EUROPEAN JOURNAL OF OPERATIONAL RESEARCH, 2015, 
253(1): 1-13. 
[18] Lin S S, Shen S L, Zhou A, Xu Y S. Risk assessment and management of 
excavation system based on fuzzy set theory and machine learning methods[J]. 
AUTOMATION IN CONSTRUCTION, 2021, 122. 
[19] Yazdi M, Khan F, Abbassi R, Rusli R. Improved DEMATEL methodology for 
effective safety management decision-making[J]. SAFETY SCIENCE, 2020, 127. 
[20] Huang J, Li Z J,  Liu H C. New approach for failure mode and effect analysis 
using linguistic distribution assessments and TODIM method [J]. RELIABILITY 
ENGINEERING & SYSTEM SAFETY, 2017, 167: 302-309. 
[21] Zhao M W, Wei G W, Wei C, Wu J. Pythagorean Fuzzy TODIM Method Based on 
东北大学硕士学位论文                                                     参考文献 
-74- 
the Cumulative Prospect Theory for MAGDM and Its Application on Risk Assessment 
of Science and Technology Projects[J]. INTERNATIONAL JOURNAL OF FUZZY 
SYSTEMS, 2021, 23(4): 1027-1041. 
[22] 崔洪昊. A 企业信息系统建设项目风险管理研究[D]. 济南：山东建筑大学，
2021. 
[23] 李鹏. A 公司软件项目风险管理研究[D]. 上海：华东理工大学，2016. 
[24] 王峰. K 公司NMS 软件项目风险管理研究[D]. 北京：中国地质大学，2018. 
[25] 龙璨. X 市住房公积金信息系统升级项目风险管理研究[D]. 北京：中国矿业
大学，2019. 
[26] 李烜. 评分软件项目风险管理研究[D]. 北京：北京邮电大学，2019. 
[27] 姜卫卫. 智慧政务门户网站项目风险管理研究—以C 经济开发区为例[D]. 石
家庄：河北经贸大学，2022. 
[28] 马海英. 项目风险管理[M]. 上海：华东理工大学出版社，2017.2，11-157. 
[29] 周榜兰. 高度综合化航空电子信息系统项目风险管理研究及思考[J]. 项目管
理技术，2020，18（1）：110-115. 
[30] 崔阳，陈勇强，徐冰冰. 工程项目风险管理研究现状与前景展望[J]. 工程管理
学报，2015，29（2）：76-80. 
[31] 姚树春，郁春江，陈芝荣. 论信息系统项目风险管理[J]. 中国西部科技，2010，
9（19）：56-57. 
[32] 石方荣. 企业项目风险管理现状及完善措施[J]. 管理研究，2019，06：21-22. 
[33] 何泳仪，周翼. 企业信息系统项目风险管理研究[J]. 管理研究，2020.16：8-9. 
[34] 李文彬. 浅谈信息系统项目的风险管理[J]. 科技资讯，2017，27：28-32. 
[35] 杨亚菲. 浅谈信息系统项目风险管理[J]. 科技咨询，2019，01：138-140. 
[36] 周义峰. 浅析我国项目风险管理研究现状[J]. 中国管理信息化，2012，15（16）：
31-32. 
[37] 高清阔，张永淼. 浅析信息系统开发的项目风险管理[J]. 管理探索，2020，07：
45-46. 
[38] 安红昌. 信息系统项目的风险管理研究[J]. 计算机工程与设计，2004，25（12）：
2145-2147. 
[39] 李凤梅. 信息系统项目风险管理浅析[J]. 电子技术与软件工程，2013，18：
249-250. 
[40] 林楠. 信息系统项目风险管理研究[J]. 信息与电脑，2015，17：164-165. 
[41] 许晓翠. 信息系统项目建设风险管理的思考[J]. 信息工程，2015，12：147. 
[42] 邵伟伟. 企业风险管理优化策略[J]. 经济师，2022，11：286-287. 
[43] 王东. 企业新产品开发风险控制与保障措施研究[J]. 经贸实践，2018，07：172. 
[44] 刘文飞. LS 公司新产品研发项目风险管理研究[D]. 长春：吉林大学，2018. 
[45] 那莉莉. 浙商银行沈阳分行资金业务风险管理改进研究[D]. 长春：吉林大学，
2017. 
[46] 常金玲. 信息系统项目的风险因素分析[J]. 情报理论与实践，2006，NO.03，
318-320. 
 
东北大学硕士学位论文                                                       附录A 
-75- 
附录A 风险因素赋值打分表 
请仔细阅读本次专家打分标准，并结合您的项目管理经验对本项目5 大类一
级风险评价指标和16 类二级风险评价指标的风险因素进行打分。 
（一）、评价标准表 
标度 
重要程度 
含义 
1 
同等重要 
两个指标相比，同等重要。 
3 
稍微重要 
两个指标相比，一个指标比另一个指标稍微重要。 
5 
明显重要 
两个指标相比，一个指标比另一个指标明显重要。 
7 
非常重要 
两个指标相比，一个指标比另一个指标非常重要。 
9 
绝对重要 
两个指标相比，一个指标比另一个指标绝对重要。 
2，4，6，8 
上述相邻判断中值 
倒数 
A 和B 相比如果标度是3，则B 和A 相比标度就是1/3。 
例如：您认为“需求风险”比“技术风险”对L 公司运维管理系统项目的选
择稍微重要，则在打分表对应表格内填入“3”分；如果您认为“管理风险”比“人
员风险”对L 公司运维管理系统项目的影响介于明显重要和非常重要之间，则在
打分表对应表格内填入“6”分。 
（二）、一级风险指标打分表 
 项目总目标 
需求风险 B1 
技术风险 B2 
管理风险 B3 人员风险 B4 安全风险 B5 
需求风险 B1 
1 
 
 
 
 
技术风险 B2 
- 
1 
 
 
 
管理风险 B3 
- 
- 
1 
 
 
人员风险 B4 
- 
- 
- 
1 
 
安全风险 B5 
- 
- 
- 
- 
1 
（三）、二级风险指标打分表 
需求风险 B1 
需求调研风险 B11 
需求变更风险 B12 
需求调研风险 B11 
1 
 
需求变更风险 B12 
- 
1 
 
技术风险 B2 
硬件风险 B21 
技术选择风险 B22 
系统集成风险 B23 
硬件风险 B21 
1 
 
 
技术选择风险 B22 
- 
1 
 
系统集成风险 B23 
- 
- 
1 
 
 
 
 
 
东北大学硕士学位论文                                                       附录A 
-76- 
管理风险 B3 
成本风险 B31 
进度风险 B32 
质量风险 B33 
沟通风险 B34 
成本风险 B31 
1 
 
 
 
进度风险 B32 
- 
1 
 
 
质量风险 B33 
- 
- 
1 
 
沟通风险 B34 
- 
- 
- 
1 
 
人员风险 B4 
人员数量风险 B41 
人员流动风险 B42 
人员能力风险 B43 
人员数量风险 B41 
1 
 
 
人员流动风险 B42 
- 
1 
 
人员能力风险 B43 
- 
- 
1 
 
安全风险 B5 
网络安全风险 
B51 
数据安全风险 
B52 
系统漏洞风险 
B53 
运行环境风险 
B54 
网络安全风险 
B51 
1 
 
 
 
数据安全风险 
B52 
- 
1 
 
 
系统漏洞风险 
B53 
- 
- 
1 
 
运行环境风险 
B54 
- 
- 
- 
1 
东北大学硕士学位论文                                                       附录B 
-77- 
附录B 模糊评价风险等级打分表 
请认真了解本次风险等级打分标准，分析16 类二级风险指标对L 公司运维管
理系统项目的影响程度。本表中，将风险划分为低、较低、中等、较高、高，五
个等级。请依据您丰富的项目建设和管理经验，以及对L 公司运维管理系统项目
的了解，对表格中的二级风险指标进行评价。例如您认为“需求调研风险”为高
风险，请您在对应的框内打对号。 
一级指标 
二级指标 
风险等级 
低风险 
较低风险 
中风险 
较高风险 
高风险 
需求风险 
B1 
需求调研风
险 B11 
 
 
 
 
 
需求变更风
险 B12 
 
 
 
 
 
技术风险 
B2 
硬件风险 
B21 
 
 
 
 
 
技术选择风
险 B22 
 
 
 
 
 
系统集成风
险 B23 
 
 
 
 
 
管理风险 
B3 
成本风险 
B31 
 
 
 
 
 
进度风险 
B32 
 
 
 
 
 
质量风险 
B33 
 
 
 
 
 
沟通风险 
B34 
 
 
 
 
 
人员风险 
B4 
人员数量风
险 B41 
 
 
 
 
 
人员流动风
险 B42 
 
 
 
 
 
人员能力风
险 B43 
 
 
 
 
 
安全风险 
B5 
网络安全风
险 B51 
 
 
 
 
 
数据安全风
险 B52 
 
 
 
 
 
系统漏洞风
险 B53 
 
 
 
 
 
运行环境风
险 B54 
 
 
 
 
 
东北大学硕士学位论文                                                         致谢 
-78- 
致谢 
本人是工科出身，已从事信息技术工作十余年。随着工作时间的增加，越来
越感受到自己在管理方面的不足，迫切地需要知识来充实自己。因此，在2020
年，再次走入校园，提升自己。 
时光匆匆，两年半的研究生生涯即将结束。回顾这段学习经历，虽然充满努
力和艰辛，但也收获颇丰。在学习方面，我学到了很多管理学领域的知识，充实
了自己；在交际方面，我在东北大学这个平台上，认识了许多良师益友；在生活
上，我能够重新踏入校园，体验到丰富多彩的校园生活。研究生阶段的结束，让
我心中充满了不舍，但更多的是感激之情。 
首先，我要感谢为我传道授业解惑的老师们，特别是我的研究生导师。他精
湛的专业知识和无私的指导，使我在研究生期间取得了很大的进步。正是老师认
真、专业的指导，才使我能够顺利地完成毕业论文。 
其次，我要感谢在背后默默支持我的家人们。正是他们的支持，才使我能够
全心全意地投入到研究生的学习中。家永远是我最温馨的港湾，每当我在学习上
遇到困难和挫折时，他们总是鼓励我，让我树立信心，克服困难。我永远感激他
们的爱和支持。 
然后，我还要感谢我的研究生同学。在研究生阶段，我们一起上课，一起学
习，一起探讨知识，一起聊工作和生活。在准备毕业论文过程中，我们一起交流
经验、分享知识、相互帮助。我们是研究生同学，更是生活中的益友。 
最后，我要衷心地感谢各位评委老师。感谢老师们在答辩中认真审阅我的论
文，并提出宝贵的意见和建议，使我能够百尺竿头更进一步，老师们辛苦了！ 
在以后的工作和生活中，我会继续踏实工作，努力学习，活到老学到老，不
断提升自己。“砥砺奋进一世纪，筑梦辉煌迎百年”，在母校百年华诞之际，感谢
我的母校东北大学，祝福母校未来一片辉煌。
东北大学硕士学位论文                                                   论文数据集 
-79- 
论文图数量：9 
论文表数量：20 
论文总页数：88 
论文参考文献数量：46 
