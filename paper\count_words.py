import glob
import os
import sys
import io
import re

# Set stdout to utf-8 to handle Unicode characters in filenames on Windows
sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

def count_words_in_file(file_path):
    """Counts the words in a single Markdown file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
            chinese_chars = re.findall('[\u4e00-\u9fa5]', content)
            english_words = re.findall('[a-zA-Z]+', content)
            word_count = len(chinese_chars) + len(english_words)
            return word_count
    except Exception as e:
        print(f"  Error reading file {file_path}: {e}")
        return 0

def process_path(path):
    """Processes a given path (file or directory) to count words in .md files."""
    total_word_count = 0
    file_count = 0

    if os.path.isfile(path) and path.endswith('.md'):
        markdown_files = [path]
    elif os.path.isdir(path):
        markdown_files = glob.glob(os.path.join(path, '**', '*.md'), recursive=True)
    else:
        print(f"Provided path '{path}' is not a valid Markdown file or directory.")
        return

    if not markdown_files:
        print(f"No Markdown files (.md) found in '{path}'.")
        return

    print(f"Counting words in Markdown files found in '{path}':")
    for file_path in markdown_files:
        word_count = count_words_in_file(file_path)
        if word_count is not None:
            print(f"- {os.path.relpath(file_path)}: {word_count} words")
            total_word_count += word_count
            file_count += 1

    print("\n" + "="*40)
    print(f"Total word count across {file_count} file(s): {total_word_count}")
    print("="*40)

if __name__ == "__main__":
    if len(sys.argv) > 1:
        # User provided a path
        target_path = sys.argv[1]
        process_path(target_path)
    else:
        # Default to current directory
        process_path('.')