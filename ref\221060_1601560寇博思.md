 
 
分类号                     密级                    
UDC                     
 
 
学  位  论  文 
 
基于复杂网络和强化学习的期货量化投资策略研究 
 
 
 
 
   
作者姓名： 寇博思 
作者学号： 1601560 
指导教师： 张吉善 副教授 
 
东北大学工商管理学院 
申请学位级别： 硕士 
学 科 类 别 ： 管理学硕士 
学科专业名称： 管理科学与工程 
论文提交日期： 2018 年12 月12 日 论文答辩日期：2018 年12 月17 日 
学位授予日期： 2019 年1 月 
答辩委员会主席：戢守峰 教授 
评  阅  人  ： 于春海 副教授  ， 赵庆国 教授 
 
 
东  北  大  学 
2018 年12 月 
 
 
 
 
A Thesis in Management Science and Engineering 
 
 
 
 
Quantitative Investment Portfolio and Trading Strategy Based on 
Complex Network and Reinforcement Learning  
By Kou Bosi 
 
 
 
 
 
Supervisor: Associate Professor Zhang Jishan 
 
Northeastern University 
December 2018 
 
 
 
-I- 
独创性声明 
本人声明，所呈交的学位论文是在导师的指导下完成的。论文中取得的
研究成果除加以标注和致谢的地方外，不包含其他人已经发表或撰写过的
研究成果，也不包括本人为获得其他学位而使用过的材料。与我一同工作的
同志对本研究所做的任何贡献均已在论文中作了明确的说明并表示谢意。 
                               学位论文作者签名： 
日  期： 
 
 
学位论文版权使用授权书 
本学位论文作者和指导教师完全了解东北大学有关保留、使用学位论
文的规定：即学校有权保留并向国家有关部门或机构送交论文的复印件和
磁盘，允许论文被查阅和借阅。本人同意东北大学可以将学位论文的全部或
部分内容编入有关数据库进行检索、交流。 
 
作者和导师同意网上交流的时间为作者获得学位后： 
 
半年□    一年□    一年半□       两年□ 
学位论文作者签名：               导师签名： 
签字日期：                       签字日期： 
 
 
东北大学硕士学位论文 
 
摘要 
-II- 
基于复杂网络和强化学习的期货量化投资策略研究 
摘  要 
随着市场的不断发展，量化投资越来越被人们所熟知，相关研究已经拓展到了多个
方面。但是期货市场量化投资策略的获利机会却远远不如以前。在过去研究的量化投资
中，关于品种的配对选择、开仓平仓阈值等一些参数的设定都缺少方法，这样很难保证
量化投资获得的利润一直最大。因此，针对期货市场价格的波动情况，避免企业的主观
态度影响配对投资决策，如何有效地选择配对品种以及制定投资策略，以最大化企业的
利润是一个具有现实意义的重要研究课题。 
基于此，本文在已有的量化投资研究成果的基础上，考虑了量化投资配对交易过程
中重要的两个环节：品种的配对组合选择与配对交易策略。具体来说分别研究了考虑复
杂网络的量化投资配对品种的组合选择、基于强化学习的量化投资品种交易策略。本文
主要进行了以下三个方面的研究工作： 
(1) 在对复杂网络和强化学习对量化投资品种选择和交易策略问题相关文献进行系
统综述的基础上，给出了相应的概念和理论分析，并给出基于复杂网络和强化学习的量
化投资品种选择与交易策略的研究框架。 
(2) 考虑复杂网络拓扑性对品种进行组合选择。介绍了期货市场中数据的选取和处
理方法，期货间品种波动相关性的计算从而构建价格波动网络，并运用复杂网络的拓扑
性对网络的度分布、平均路径、聚类系数等进行分析，进一步挑选配对品种；再将配对
品种进行平稳性检验及协整性检验，验证配对品种的有效性。最后通过算例与数值分析，
对所选择的配对品种进行配对检验，确定配对交易的品种。 
(3) 基于强化学习算法对选定的配对品种进行投资决策。结合期货量化交易的特点
以及期货市场中的实际情况，在选定配对交易品种的基础上进行配对交易策略。基于强
化学习探讨交易过程中规则的确定，对传统的配对交易阈值进行优化改进，并通过神经
网络的正反向传播进行模型的构建作为代替强化学习中存储经验值Q 表的工具，并结合
算例实验对配对品种进行仿真测试及训练，并进行绩效风险分析，验证量化投资交易策
略的有效性。同时说明研究量化交易策略对企业的意义，可以有效规避企业风险以及发
现品种价格，为企业提供合理的建议支持与收益保障。 
本文提出的基于复杂网络和强化学习的量化投资品种组合与交易策略研究可以用
于解决期货市场的配对交易问题，也为企业进行相关决策提供了指导。 
关键词：期货市场；复杂网络拓扑性；配对交易；收益率；强化学习
 
  
 
东北大学硕士学位论文 
 
Abstract 
-III- 
Quantitative Investment Strategy Based on Complex Network and 
Reinforcement Learning 
Abstract 
With the continuous development of the market, quantitative investment has become more 
and more familiar, and related research has been extended to many aspects. However, the profit 
opportunities of the quantitative trading strategy in the futures market are far less than before. 
In the quantitative transactions studied in the past, there are no methods for setting the 
parameters such as the pairing selection of varieties, the opening and closing thresholds, etc., 
so it is difficult to ensure that the profits from quantitative transactions are always the largest. 
Therefore, aiming at the fluctuation of futures market prices, avoiding the subjective attitude of 
enterprises affecting paired trading decisions, how to effectively select matching varieties and 
formulate trading strategies to maximize corporate profits is an important research topic of 
practical significance. 
Based on this, the existing research results of quantitative investment decision-making, 
this paper considers two important aspects in the process of quantitative investment matching 
transaction: the pairing combination selection and the pairing trading strategy. Specifically, the 
combination of quantitative investment matching varieties considering complex networks and 
the quantitative investment trading strategies based on reinforcement learning are studied. This 
paper mainly carried out the following three aspects of research work: 
(1) Based on a systematic review of the literature on complex network selection and 
trading strategy for complex networks and reinforcement learning, the corresponding concepts 
and theoretical analysis are given, and quantitative investment based on complex networks and 
reinforcement learning is given. Research framework for variety selection and trading strategies. 
(2) Considering the complex network topology to select combinations of varieties. This 
paper introduces the selection and processing methods of data in the futures market, the 
calculation of the fluctuation correlation of futures varieties to construct a price fluctuation 
network, and analyzes the degree distribution, average path and clustering coefficient of the 
network by using the topology of complex networks. The paired varieties were further selected; 
the paired varieties were tested for stationarity test and cointegration test to verify the validity 
of the paired varieties. Finally, through the example and numerical analysis, the paired test of 
东北大学硕士学位论文 
 
Abstract 
-IV- 
the selected paired species is tested to determine the varieties of the paired transaction. 
(3) Based on the reinforcement learning algorithm, the investment decision is made for the 
selected paired species. Combined with the characteristics of futures quantitative trading and 
the actual situation in the futures market, the pairing trading strategy is based on the selected 
matching trading varieties. Based on reinforcement learning to explore the rules in the 
transaction process, the threshold of the traditional paired transaction model is optimized and 
improved, and the model is constructed by forward and backward propagation of the neural 
network as a tool to replace the empirical value Q table in reinforcement learning. The 
simulation test and training of the paired varieties were carried out in combination with the 
experiment, and the performance risk analysis was carried out to verify the effectiveness of the 
quantitative investment trading strategy. At the same time, it shows that the significance of 
researching quantitative trading strategies to enterprises can effectively avoid enterprise risks 
and discover future product prices, and provide reasonable model support and income guarantee 
for enterprises. 
The quantitative investment portfolio and trading strategy research based on complex 
network and reinforcement learning proposed in this paper can be used to solve the pairing 
transaction problem in the futures market, and also provide guidance for enterprises to make 
relevant decisions. 
 
Keywords：futures market; complex network topology; paired transaction; rate of return; 
reinforcement learning
东北大学硕士学位论文 
 
目录 
-V- 
目  录 
独创性声明 ........................................................................................................ I 
摘  要 ............................................................................................................... II 
Abstract............................................................................................................ III 
第1 章 绪论 ..................................................................................................... 1 
1.1 研究背景 ................................................................................................................. 1 
1.1.1 量化投资的兴起与发展 .................................................................................. 1 
1.1.2 复杂网络对量化投资品种选择的支持作用 .................................................. 2 
1.1.3 研究基于强化学习量化投资交易策略的必要性 .......................................... 2 
1.2 问题的提出 ............................................................................................................. 3 
1.2.1 考虑复杂网络的量化投资品种组合选择 ...................................................... 3 
1.2.2 基于强化学习的量化投资品种交易策略 ...................................................... 4 
1.3 研究目的与研究意义 ............................................................................................. 4 
1.3.1 研究目的 .......................................................................................................... 4 
1.3.2 研究意义 .......................................................................................................... 5 
1.4 研究内容、研究思路与研究方法 ......................................................................... 5 
1.4.1 研究内容 .......................................................................................................... 6 
1.4.2 研究思路 .......................................................................................................... 6 
1.4.3 研究方法 .......................................................................................................... 8 
1.5 论文结构 ................................................................................................................. 9 
第2 章 相关研究文献综述 ............................................................................ 11 
2.1 文献检索情况概述 ............................................................................................... 11 
2.1.1 文献检索范围分析 ........................................................................................ 11 
2.1.2 相关文献情况分析 ........................................................................................ 11 
2.1.3 学术趋势分析 ................................................................................................ 12 
2.2 关于基于复杂网络的交易市场品种关联研究 ................................................... 14 
2.2.1 基于复杂网络理论的拓扑关联结构 ............................................................ 14 
2.2.2 基于复杂网络理论的量化交易 .................................................................... 15 
2.3 关于基于市场量化投资的配对交易决策研究 ................................................... 16 
2.3.1 基于市场量化投资的品种配对选择方法 .................................................... 16 
东北大学硕士学位论文 
 
目录 
-VI- 
2.3.2 基于市场量化投资的交易信号规则 ............................................................ 17 
2.4 关于强化学习的技术与应用研究 ....................................................................... 18 
2.4.1 强化学习技术的相关研究 ............................................................................ 18 
2.4.2 强化学习应用的相关研究 ............................................................................ 19 
2.5 已有研究成果的评述 ........................................................................................... 20 
2.5.1 主要贡献 ........................................................................................................ 20 
2.5.2 不足之处 ........................................................................................................ 21 
2.5.3 对本文研究的启示 ........................................................................................ 22 
2.4 本章小结 ............................................................................................................... 22 
第3 章 基本概念界定与理论基础 ............................................................... 23 
3.1 复杂网络 ............................................................................................................... 23 
3.1.1 复杂网络拓扑性结构的概念 ........................................................................ 23 
3.1.2 常见网络的类型 ............................................................................................ 23 
3.1.3 复杂网络拓扑结构特征 ................................................................................ 24 
3.2 配对品种的选择 ................................................................................................... 26 
3.2.1 配对品种筛选条件 ........................................................................................ 26 
3.2.2 配对选择方法 ................................................................................................ 27 
3.3 配对交易规则 ....................................................................................................... 27 
3.3.1 配对交易原理 ................................................................................................ 27 
3.3.2 配对交易信号 ................................................................................................ 28 
3.4 强化学习 ............................................................................................................... 28 
3.4.1 强化学习的概念 ............................................................................................ 28 
3.4.2 强化学习基本原理 ........................................................................................ 29 
3.4.3 强化学习一般算法 ........................................................................................ 30 
3.5 本章小结 ............................................................................................................... 32 
第4 章 考虑复杂网络的多品种量化投资组合研究 ................................... 33 
4.1 问题描述与符号说明 ........................................................................................... 33 
4.1.1 问题描述 ........................................................................................................ 33 
4.1.2 符号说明 ........................................................................................................ 33 
4.2 数据的收集 ........................................................................................................... 34 
4.2.1 数据的选取 .................................................................................................... 34 
东北大学硕士学位论文 
 
目录 
-VII- 
4.2.2 数据的处理 .................................................................................................... 35 
4.3 期货价格波动网络的构建 ................................................................................... 35 
4.3.1 波动相关性计算 ............................................................................................ 35 
4.3.2 网络模型及拓扑性 ........................................................................................ 36 
4.4 期货品种配对关系检验 ....................................................................................... 37 
4.4.1 平稳性检验 .................................................................................................... 37 
4.4.2 协整关系检验 ................................................................................................ 38 
4.5 数值与算例分析 ................................................................................................... 39 
4.5.1 数据的处理 .................................................................................................... 39 
4.5.2 期货价格波动网络的构建 ............................................................................ 45 
4.5.3 期货品种网络拓扑性评价 ............................................................................ 47 
4.5.4 期货品种配对平稳性检验 ............................................................................ 53 
4.5.5 期货品种配对协整关系检验 ........................................................................ 55 
4.6 管理启示 ............................................................................................................... 56 
4.7 本章小结 ............................................................................................................... 57 
第5 章 基于强化学习的量化投资品种交易策略 ....................................... 58 
5.1 问题描述与符号说明 ........................................................................................... 58 
5.1.1 问题描述 ........................................................................................................ 58 
5.1.2 符号说明 ........................................................................................................ 59 
5.2 交易信号规则 ....................................................................................................... 59 
5.2.1 传统交易信号的确定 .................................................................................... 60 
5.2.2 基于OU 过程的交易信号确定 .................................................................... 60 
5.2.3 自适应交易信号训练 .................................................................................... 61 
5.3 强化神经网络模型的构建 ................................................................................... 62 
5.3.1 BP 神经网络实现过程 ................................................................................... 62 
5.3.2 BP 神经网络模型的搭建 ............................................................................... 63 
5.4 强化学习配对交易策略 ....................................................................................... 65 
5.4.1 评定绩效和风险指标的选定 ........................................................................ 65 
5.4.2 强化学习算法的选择 .................................................................................... 66 
5.4.3 强化学习动态交易信号的配对交易决策 .................................................... 67 
5.5 数值与算例分析 ................................................................................................... 69 
东北大学硕士学位论文 
 
目录 
-VIII- 
5.5.1 配对品种数据需求 ........................................................................................ 69 
5.5.2 强化学习神经网络的正向更新 .................................................................... 70 
5.5.3 强化学习神经网络的反向存储 .................................................................... 71 
5.5.4 强化学习配对交易信号的训练与测试 ........................................................ 72 
5.5.5 强化学习配对交易策略的绩效结果分析 .................................................... 73 
5.6 管理启示 ............................................................................................................... 76 
5.7 本章小结 ............................................................................................................... 77 
第6 章 结论与展望 ....................................................................................... 78 
6.1 本文的主要工作及主要结论 ............................................................................... 78 
6.2 本文的主要贡献 ................................................................................................... 79 
6.3 本文的研究局限 ................................................................................................... 79 
6.4 进一步研究展望 ................................................................................................... 80 
参考文献 ......................................................................................................... 81 
致谢 ................................................................................................................. 88 
 
东北大学硕士学位论文 
 
第1 章  绪论 
-1- 
第1 章 绪论 
期货市场种类的多样化使得单一品种的交易无法满足企业及个人的需求，而传统的投
资组合决策也面临着许多问题，为避免主观意识影响收益最大化的实现，企业及个人应如
何选择商品进行交易及如何进行最优决策以获得最大的利润是一个具有实际意义的研究
课题，本章首先进行背景的介绍，从而提出要研究的问题，明确目标和意义，进一步指出
研究内容、研究思路和方法。 
1.1 研究背景 
基于复杂网络与强化学习的黑色金属期货量化投资决策问题具有广泛的实际背景和
学术背景，本节将从期货市场量化交易决策的兴起与发展、复杂网络对期货市场量化交易
决策的支持作用以及研究基于强化学习期货市场量化交易决策的必要性这三个方面进行
阐述。 
1.1.1 量化投资的兴起与发展 
当今社会，传统的投资组合理论与决策已经不能满足市场投资者们的需求，通过数量
化方式及计算机程序化发出买卖指令的方式越来越受到人们的关注。1973 年芝加哥成立了
第一家期权交易所，正式揭开了量化投资的序幕[1]，格里本伯格在1983 年为商品交易部门
编写的软件中发现了“统计套利策略”[2]，这一策略能很好地适应市场走势并产生收益。
自此，量化投资兴起，发展势头强劲。 
我国由于金融发展较晚且2008 年受到金融危机的影响，国内早期的量化产品大部分
以套利策略为主。自2010 年3 月以来，中国证券市场首次实施新的实施措施，通过保证金
融资，证券借贷业务和股指期货业务改善中国证券市场，稳定了市场，并发布了新的投资
理念和技术。[3,4]。 
虽然量化投资进入中国期货市场的时间较短且不成熟，对它的技术研究及应用发展空
间很大。计算机技术领域的飞速进步和智能数据库的完善使得量化交易快速发展，它需要
来自金融、数学、统计、信息技术和互联网等多学科领域的融合。量化投资基于历史数据
的分析和概率统计进行数据的处理和最终的决策，可以避免传统投资组合中投资者的主观
意见，降低了风险的频发性。在这样的大背景下，将量化投资应用到期货市场也开始受到
我国学术界和投资界的重视，对量化投资中各种策略的研究也相继展开。 
东北大学硕士学位论文 
 
第1 章  绪论 
-2- 
1.1.2 复杂网络对量化投资品种选择的支持作用 
复杂网络作为新兴起的学科，主要研究系统内容的关系以及系统运行的复杂性。如今
生活中很多复杂的事例都可以用复杂网络的理论或原理来解释，复杂网络的节点代表系统
中的个体，复杂网络的边代表系统中个体之间的关系，点和边构成的网络系统具有以下复
杂性：网络内节点数量众多，节点间的连接及其所代表的内容都比较复杂且具有动态演化
的特性，这就导致网络的功能和拓扑结构会发生实时变化，使得网络的复杂效应更为明显；
网络中的点之间相互影响，可能会导致不同复杂网络的互连和交互，并以复杂的耦合方式
促进每个网络的演进[5-6]。 
通过复杂网络的不断探索与研究，已有不少研究将复杂网络与经济金融领域相结合，
利用复杂网络理论来研究金融网络的结构特征并以此分析金融市场的各种现象，随着科学
技术的不断发展，复杂网络的研究也取得了实质性的进展[7-9]。Tsiotas[7]建立了股票价格相
关网络并进行了聚类分析，利用信息筛选股票，将复杂网络理论应用于量化选股中。Gatev[8]
构建的与股票相关的网络显示树状分布，表明股票之间存在强大的局部聚合属性，并且该
特征可用于筛选股票组合。刘庆[9]构建复杂的股票市场网络，在经典的Fama-French 三因
子模型的基础上，将复杂网络度值中的关键指标作为第四个风险因素，得到一个全新的多
因素模型。 
期货市场同样可以看作是一个关联网络，通过复杂网络拓扑性可以突破传统量化交易
选取品种的方式。复杂网络的拓扑性结构能够很大程度上构建产品之间的相关性并显示关
联强弱，影响投资者的配对决策，因而复杂网络对期货市场量化交易配对品种选择具有支
持作用。 
1.1.3 研究基于强化学习量化投资交易策略的必要性 
20世纪80年代，Blondel[10]提出使用差价偏离平均水平，购买相对低估的品种，卖出相
对过高估计的品种，当差价逐渐回归到平均水平时，通过收敛后收盘差价从而获取收益。
这一中性的交易策略在金融市场的交易中得到了很好的利用，关于量化投资交易策略的研
究也越来越引起学者们的关注，其中对于交易信号规则的确定也作为交易策略研究的重要
环节[11-14]。Newman[11]通过使用偏差策略检验交易的最佳收盘点，并使用数值分析案例总
结其结论。Zhou[12] 将交易规则简化为三个组合之间的最优转换问题，并证明存在最优切
换点。但是，这种方法总是以常数或标准差作为交易信号，还是无法达到预期的效益。而
随着机器学习的发展，强化学习受到关注，它的特点是不断尝试和调整其行为，不断学习
如何将状态映射到行动以获得最大的长期奖励。通过计算机程序自适应的根据市场价格变
东北大学硕士学位论文 
 
第1 章  绪论 
-3- 
化更新信号阈值参数从而实现开平仓的调整最终获取最大收益。目前强化学习模式在金融
领域主要运用于证券交易尤其高频交易和投资组合管理，胡文伟[13]认为不确定性和动态性
是解决交易信号设置的关键因素，因此强化学习算法非常适合解决此类问题。JEE[14]使用
多智能体Q学习算法定义必要的角色，做出投资决策，进行股票模拟，以韩国股市为研究
对象，证明方法的有效性。 
综上所述，研究基于强化学习期货市场的量化交易策略有助于丰富建平仓交易阈值的
相关研究，同时可避免期货市场中投资者对品种配对的主观意向和从众心理，对此提供了
科学的指导与支持。 
1.2 问题的提出 
针对相关研究的未来发展趋势，本文针对两个问题进行研究，一是考虑复杂网络的量
化投资品种组合选择，二是基于强化学习的量化投资品种交易策略。下面给出这两个问题
的详细阐述。 
1.2.1 考虑复杂网络的量化投资品种组合选择 
选择合适的品种进行组合配对是量化投资决策的核心环节之一，已有研究中针对考虑
期货市场复杂网络量化投资品种组合选择的研究还不多见。龙奥明[15]的研究表明以往投资
者在进行期货市场的投资组合时，往往按照主观臆想或从众心理进行品种的配对和买卖交
易，或是单一的进行产品的交易，这都无法达到收益的最大化。胡钢[16]通过判断相关性强
弱来决定品种的配对选择，但也过于片面，可能导致品种间无法产生价差的偏离。因此，
本文将复杂网络拓扑性引入到期货品种配对选择方法中，通过节点间度与度的分布、平均
路径长度与聚类系数等分析，以便能更好地帮助投资者完成投资决策的前提过程。 
目前对于复杂网络理论的研究已深入各个领域，如计算机领域，金融领域等，复杂网
络作为一个关系网，可以将很多复杂的体系形象化[17,18]。由于期货市场的复杂结构及期货
品种数量的增加，期货市场便也可以看作是一个复杂网络，其中以与黑色金属相关的期货
品种作为节点，以波动相关性作为两节点间的连线构建价格波动网络，线的数量与粗细程
度将反映出品种间的关系及关系强弱。构建价格波动网络后，再进行复杂网络拓扑性分析，
主要针对网络度的分布、网络平均路径与聚类系数的变化趋势来解决考虑复杂网络的量化
投资品种组合选择问题。 
在考虑复杂网络的基础上，研究如何将复杂网络拓扑性结构应用于量化投资的配对选
种环节，并研究如何根据关联网络的拓扑性对品种进行评价，从而确定品种组合，为解决
期货市场中的品种配对选择问题提供科学的指导。 
东北大学硕士学位论文 
 
第1 章  绪论 
-4- 
1.2.2 基于强化学习的量化投资品种交易策略 
在确定配对品种的基础上，交易策略也是量化投资的核心环节之一，即找到合适的时
间点进行品种的交易从而获取收益也是配对交易的重要部分。配对交易作为一种市场中性
策略，要根据价差与历史均值的偏离进行品种的开仓交易，待价差回归长期均衡价格时在
进行平仓，所以找到合适的时间点进行品种的交易从而获取收益也是配对交易的重要部分
[19-21]。最开始传统的配对交易往往按照固定常数或标准差作为判断买卖的进出信号，已经
很难保证投资者获得最大的收益，之后有学者引入了GARCH 模型进行交易，但是无法对
市场频繁的波动作出及时的变化[22-24]。所以需要通过新的办法调整和优化阈值参数，提升
配对交易模型的盈利能力和执行效率。为了解决这种问题，考虑到强化学习具有自学习自
适应环境的特点，通过环境的变化做出动作即调整交易信号可以作为交易策略确定交易信
号的方法。 
在考虑传统配对交易模型的基础上，研究如何改进交易期建仓平仓信号、强化神经网
络模型的构建，并在此基础上，如何基于强化学习通过计算机程序实时优化动态信号阈值
参数，提高投资者的收益率，上述的这些问题需要进一步深入研究。 
1.3 研究目的与研究意义 
1.3.1 研究目的 
针对本文的研究问题，通过总结和分析国内外相关研究成果，明确本文的研究方向，
提出系统的研究框架，进而提出基于复杂网络和强化学习的量化投资品种组合选择与交易
策略。下面从理论、方法和应用层面给出本文的研究目的： 
(1) 在理论层面，首先依据现实背景，并在对已有的文献进行综述及分析的基础上，针
对量化投资的两个重要环节，即配对品种的选择及配对交易策略，提出基于复杂网络和强
化学习的量化投资策略的研究框架，为进一步研究配对品种的选择和配对交易信号打下基
础，同时丰富了配对品种的选择和配对交易信号的研究内容。 
(2) 在方法层面，在考虑现有的配对品种组合选择方法中，考虑复杂网络拓扑性中度
与度的分布、平均路径等指标分析品种配对网络，提高配对品种的可靠性；针对配对交易
策略问题，提出基于强化学习的量化交易策略，对阈值参数进行迭代训练，从而实现阈值
参数的实时优化。 
(3) 在应用层面，以某钢铁贸易公司作为算例背景，验证考虑复杂网络拓扑性对配对
交易品种选择的合理性以及实用性，同时为基于强化学习量化交易策略对企业的价格发现
与风险规避提供良好的参考与借鉴。 
东北大学硕士学位论文 
 
第1 章  绪论 
-5- 
1.3.2 研究意义 
本文在传统量化投资组合决策研究的基础上，考虑复杂网络对期货品种的组合选择与
基于强化学习的配对交易信号问题进行策略研究。下面从理论意义和实际意义两个方面进
行阐述。 
(1) 理论意义 
本文研究的考虑复杂网络的品种组合选择与基于强化学习的配对品种交易策略是对
已有的传统配对交易的拓展研究。复杂网络与强化学习领域的研究主要集中在理论上的研
究，将这两者应用到期货交易市场领域还处于探索、创建的阶段，缺乏独立的、系统的理
论分析框架[9,13,16]。因此，本文在投资组合理论的基础上，拓宽到量化投资模式，通过复杂
网络分析处理期货市场大量品种组合，构建符合投资者理念的配对品种，再判断买入以及
卖出的信号，并结合实际期货市场的真实数据，尝试将BP 神经网络及强化学习相关技术
应用到数据中，建立考虑价格波动的品种配对网络模型和基于强化学习的配对品种交易决
策，分析期货品种间的相关波动性，同时观察强化学习对量化交易决策中交易信号的应用
效果。此外，以往的期货投资组合策略问题多基于遗传算法、蚁群算法等，本文采用强化
学习中的Q-learning 算法对模型训练学习。模型的建立和算法的迭代不仅深化对期货市场
价格趋势的判断，而且会探索和拓宽此类期货投资组合问题的决策思路。 
(2) 实际意义 
提高配对品种的准确度对企业提高绩效具有重要作用。本文的研究将期货交易所中的
品种价格作为数据支持，结合机器学习中强化学习算法不断学习不断优化的特点，建立考
虑价格波动的品种配对网络模型和基于强化学习的配对品种交易决策，通过计算机程序对
量化交易模型进行强化学习，验证模型与算法的适用性和有效性。通过复杂网络的拓扑性
分析寻求适合的期货配对品种，最大化消除投资者本身的主观意见和投资理念，对期货市
场繁多复杂的投资组合方式进行选择，并对选择的配对品种进行协整交易决策，从而为投
资者做出准确的量化交易策略提供有价值的参考。此外，以期货市场真实的产品数据为研
究对象，结合算例背景选定配对品种并对交易信号做出适时调整，证明研究黑色金属期货
品种交易决策对企业的意义，可以有效规避企业风险以及发现未来品种价格，为企业提供
合理的模型支持与收益保障。同时可以对期货市场产生正确的引导作用，对现实中量化投
资问题具有指导与借鉴意义。 
1.4 研究内容、研究思路与研究方法 
通过分析本文的研究问题，在综合考虑本文的研究目的和研究意义的基础上，本节将
东北大学硕士学位论文 
 
第1 章  绪论 
-6- 
对本文的研究内容、研究思路和研究方法进行阐述。 
1.4.1 研究内容 
本文基于量化交易中的两个重要环节，对期货市场中黑色金属商品期货进行研究，分
析品种的组合选择及配对品种的交易策略。本文的研究内容分为两个方面： 
(1) 考虑复杂网络拓扑性对品种进行组合选择。介绍了期货市场中数据的选取和处理
方法，期货间品种的波动相关性的计算从而构建价格波动网络，并运用复杂网络的拓扑性
对网络的度分布、平均路径、聚类系数等进行分析，进一步挑选配对品种；再将品种进行
配对检验，验证品种的有效性。最后通过算例与数值分析，结合期货交易过程中出现的价
格波动以及期货市场中的实际情况，对期货品种价格波动关联网络进行了研究，运用数学
统计的方法对度、加权度、集聚系数、路径长度等指标进行复杂网络的拓扑性分析并反映
期货价格波动网络总体特征。结合算例背景对所选择的配对品种进行配对检验，确定配对
交易的品种。 
(2) 基于强化学习算法对选定的配对品种设置交易信号。结合期货量化交易的特点以
及期货市场中的实际情况，在选定配对交易品种的基础上进行配对交易策略。基于强化学
习探讨交易过程中规则的确定，对传统的配对交易模型的阈值进行优化改进，并通过神经
网络的正反向传播进行模型的构建作为代替强化学习中存储经验值Q 表的工具，并结合算
例实验对配对品种进行仿真测试及训练，并进行绩效风险分析，验证量化投资交易策略的
有效性。同时说明研究量化交易策略对企业的意义，可以有效规避企业风险以及发现未来
品种价格，为企业提供合理的模型支持与收益保障。 
1.4.2 研究思路 
本文研究工作所依据的研究思路为：首先分析本文的研究背景，提炼本文的研究问题，
从而对与本文研究内容相关的文献综述和相关概念界定、理论基础进行介绍， 然后给出考
虑复杂网络的量化投资品种组合选择和基于强化学习的量化投资品种交易策略，同时为了
验证本文提出方法的科学性与有效性，以某一钢铁贸易公司为背景期货进行算例与数值分
析。本文拟开展研究工作所遵循的思路如图1.1 所示。 
东北大学硕士学位论文 
 
第1 章  绪论 
-7- 
研究背景分析、研究问题提出
(确定研究问题)
考虑复杂网络的多品种量化投资组合研究
（论文主要工作）
期货价格波动关联网络构建
结论与展望
相关研究文献综述
(了解已有的研究成果、分析其贡献与不足)
相关概念与理论知识的认识和分析
(阐述本文所涉及的基本概念与理论方法)
基于强化学习的量化投资品种交易策略研究
（论文主要工作）
期货品种配对关系检验
算例与数值分析
强化神经网络模型构建
算例与数值分析
强化学习配对交易策略
 
图1.1 本文的研究思路 
Fig. 1.1 The research route of the thesis 
图1.1 中的内容进行说明： 
(1) 通过搜集文献，掌握研究现状，在此基础上提出了基于复杂网络和强化学习的量
化投资品种组合选择与交易策略问题。 
(2) 针对本文提出的研究问题，结合现实中的实际背景，明确研究目的和研究意义。 
(3) 为了达到本研究的目的，揭示了研究的意义，确定了研究过程中使用的研究方法。  
(4) 针对本文的研究内容，综述与本文研究内容相关的研究成果，对已有研究成果进
行评述，给出已有研究成果的主要贡献、不足之处以及对本文研究工作的启示。 
(5) 依据文献综述，对本文涉及的基于复杂网络的拓扑性理论、配对交易以及强化学
习等相关概念与理论基础进行阐述。 
东北大学硕士学位论文 
 
第1 章  绪论 
-8- 
(6) 围绕基于复杂网络和强化学习的量化投资品种组合选择与交易策略分别开展对考
虑复杂网络的量化投资品种组合选择和基于强化学习的量化投资品种交易策略研究。 
(7) 总结本文的主要研究成果和结论，指出本文的主要贡献和局限，并对下一步的研
究工作进行展望。 
1.4.3 研究方法 
本文采用的研究方法主要包括文献分析法、归纳逻辑法、数学建模法、统计分析法和
计算实验法，首先使用文献分析等方法对研究背景进行分析，以确定研究问题；其次使用
统计分析等方法对数据进行分析处理；再次，使用数学模型法建立配对交易策略模型；再
使用实验分析法进行潜在应用研究，具体说明如下： 
(1) 文献分析法：主要指收集国内外的相关文献，并对收集到的文献进行方向鉴别、内
容研读和整理，通过对相关文献的系统研究和调查，对该领域的研究方向、内容、方法以
及热点进行准确的把握，分析已有文献中的局限之处，并结合实际应用的需要，科学且准
确地提出本文的研究内容。 
(2) 逻辑归纳法：通过对现有的研究内容和方法进行全面分析，总结出需要深入研究
的方向以及现有研究方法的不足。同时，对文中所提出的问题进行详细研究和深刻剖析，
依旧算例观察结果来总结出一些相应的结论和管理启示。 
(3) 数学建模法：将问题抽象成数学模型。基本特征包括：研究问题的抽象化和仿真
化，构建配对交易模型。 
(4) 统计分析法：运用爬虫软件获取万德数据库的统计数据，再通过计量分析工具对
数据进行分析。 
(5) 计算实验法：指将文中所构建的理论模型与实际应用相结合的方法，以适合理论
的企业为依据进行数值算例分析，通过借助计算机仿真进行训练与测试，并得出相应的结
论。 
本文的技术路线如图1.2 所示。 
东北大学硕士学位论文 
 
第1 章  绪论 
-9- 
研究框架提出
模型提出
算例实验
研究思路
明确研究问题和
研究内容
基于复杂网络和强化学习的期货量
化投资策略问题
研究问题
基于复杂网络和强化学习的期货量
化投资策略的研究框架
提出复杂网络和强化学习的期货量
化投资策略模型
将提出的模型应用于算例中
问题背景及相关文献分析，
提炼基于复杂网络和强化学
习的量化投资品种组合与交
易策略问题
研究内容
提出研究框架，并给出研究
框架的有关说明
提出考虑复杂网络的量化投
资品种组合选择
提出基于强化学习的量化投
资品种交易策略
基于复杂网络和强化学习的量
化投资品种组合与交易策略
研究方法
文献分析法
逻辑归纳法
文献分析法
逻辑归纳法
数学建模法
统计分析法
计算实验法
 
图1.2 论文的技术路线 
Fig. 1.2 The technical routemap of the thesis 
1.5 论文结构 
本文共分为6 章，各章主要内容如下： 
第1 章 绪论。介绍本文的研究背景，提出本文的研究问题，明确本文的研究目的与研
究意义，并确定研究内容、研究思路、研究方法及本文的章节安排。 
第2 章 相关研究文献综述。首先，对相关文献检索情况进行概述；其次，针对基于复
杂网络的交易市场品种关联研究，主要从基于复杂网络理论的拓扑关联结构和基于复杂网
络理论的量化选种方面进行综述；再次，对基于市场量化交易的投资组合决策方法的相关
文献进行综述，主要包括基于市场量化交易的品种配对选择方法和给予市场量化交易的建
平仓信号规则的相关研究综述；最后，总结已有文献的贡献与不足之处，以及已有研究对
本文关注问题的启示。 
第3 章 基本概念界定与理论基础。介绍本文研究所涉及的主要理论与概念，包括复杂
网络的概念与拓扑结构、配对品种的选择、配对交易规则以及强化学习的基本概念和算法。 
第4 章 考虑复杂网络拓扑性对品种进行组合选择。介绍了期货市场中数据的选取和
处理方法，期货间品种的波动相关性的计算从而构建价格波动网络，并运用复杂网络的拓
扑性对网络的度分布、平均路径、聚类系数等进行分析，进一步挑选配对品种；再将品种
东北大学硕士学位论文 
 
第1 章  绪论 
-10- 
进行配对检验，验证配对品种的有效性。最后通过算例与数值分析，结合期货交易过程中
出现的价格波动以及企业主营业务的实际情况，对期货品种价格波动关联网络进行了研究，
反映期货价格波动网络总体特征，并对所选择的配对品种进行配对检验，确定配对交易的
品种。 
第5 章 基于强化学习对选定的配对品种设置交易信号。在选定配对交易品种的基础
上进行配对交易策略，基于强化学习探讨交易过程中规则的确定，对传统的配对交易模型
的阈值进行优化改进，并通过神经网络的正反向传播进行模型的构建作为代替强化学习中
存储经验值Q 表的工具，并结合算例实验对配对品种进行仿真测试及训练，并进行绩效风
险分析，验证量化投资交易策略的有效性。 
第6 章 结论与展望。主要包括本文的主要研究工作及主要结论、本文的主要贡献，并
指出本文的研究局限以及进一步研究展望。
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-11- 
第2 章 相关研究文献综述 
目前学术界对于将量化投资应用于期货市场并应用复杂网络结构特性配对选种的研
究比较少。而相关的复杂网络结构特征的研究、量化交易决策的研究已经有了丰富的研究
成果。因此，本章针对所要研究的问题，拟从三个方面进行文献综述，通过对已有相关研
究的总结与分析，明确已有研究成果的主要贡献、不足之处及对本文研究的启示，为本文
后续的研究工作奠定理论基础。 
2.1 文献检索情况概述 
本节对期货市场量化投资相关研究的文献检索情况进行简要说明，主要包括文献检索
范围分析、相关文献情况分析和学术趋势分析。 
2.1.1 文献检索范围分析 
为了明确文献的综述范围，首先需要对复杂网络和量化投资的发展历程与未来发展趋
势进行分析，从而确定本文研究主题的范畴和相关研究文献，为后续的文献综述做准备。 
复杂网络和量化投资都是近几年来国内外学者广泛关注的问题，从目前国内外已有的
研究成果表明，复杂网络的相关研究一般为拓扑性的结构特征以及应用领域；量化交易的
相关研究主要集中在配对方法的选择与交易信号规则。另外，近年来，随着人工智能的不
断进步，一些学者将机器学习的研究成果引入到配对交易研究中，研究机器学习对传统配
对交易的阈值优化，拓展了传统配对交易模型的研究方向。结合本章研究的问题，主要关
注关于机器学习中强化学习发展认知与相关应用的研究文献。 
综上所述，为支持基于复杂网络和强化学习的量化投资品种组合选择与交易策略研究，
本文将主要针对以下三个方面的文献进行综述：关于复杂网络，综述了基于复杂网络理论
的拓扑关联结构的研究文献和基于复杂网络理论的量化交易的研究文献；关于量化交易，
综述了基于市场量化投资的品种配对选择方法和基于市场量化交易的建平仓信号规则的
研究文献；关于强化学习，综述了强化学习的技术与应用的相关研究文献。 
2.1.2 相关文献情况分析 
本文以量化交易/ Quantitative Matching Trading、复杂网络/ Complex Network、强化学
习/ Reinforcement learning、期货市场/ Futures Market 等为主题词，以Elsevier Science 数据
库、Informs 数据库、IEL 全文数据库、Wiley Online Library 数据库和中国学术期刊网全文
数据库作为检索源，进行了中英文文献检索。截至到2018 年9 月，从Elsevier Science 数
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-12- 
据库、IEL 全文数据库、Informs 数据库、Wiley Online Library 数据库、Springer Link 数据
库中检索到946 篇英文文献，从中国学术期刊网全文数据库中检索到468 篇中文文献。因
为在一些文献中，文章的研究主题与本文相关度不大。所以，通过筛选与本文研究主题相
关的英文文献和中文文献分别是90篇和43篇。文献的检索主题词、检索源及篇数见表2.1。 
表2.1 相关文献的检索情况 
Table 2.1 Searching conditions of related literature 
检索源 
主题词 
检
 项 
篇数 
有效篇数 
时间 
中国学术期刊网 
全文数据库 
量化配对交易/复杂
网络/期货市场/强
化学习 
全文 
468 
43 
2006-2018 
Elsevier Science 数据
库 
Quantitative 
Matching Trading / 
Complex Network / 
Futures Market / 
Reinforcement 
learning 
Abstract/ Title/ 
Keywords 
492 
38 
1989-2018 
Informs 数据库 
Quantitative 
Matching Trading / 
Complex Network / 
Futures Market / 
Reinforcement 
learning 
Title/ Abstract 
32 
6 
1991-2018 
IEL 全文数据库 
Quantitative 
Matching Trading / 
Complex Network / 
Futures Market / 
Reinforcement 
learning 
Title/ Abstract 
34 
10 
2008-2018 
Wiley Online Library
数据库 
Quantitative 
Matching Trading / 
Complex Network / 
Futures Market / 
Reinforcement 
learning 
Title 
21 
4 
2007-2018 
Springer Link 数据库 
Quantitative 
Matching Trading / 
Complex Network / 
Futures Market / 
Reinforcement 
learning 
Title/ 
Keywords 
367 
32 
1989-2018 
2.1.3 学术趋势分析 
本文使用了CNKI 知识搜索中的“学术趋势”分析工具，分别以复杂网络、量化交易
和强化学习三个搜索词对本文所关注的问题进行学术趋势分析。图2.1 至图2.6 分别展示
了以上三个研究主题的学术关注度和用户关注度。 
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-13- 
 
图2.1 CNKI 对复杂网络研究的学术关注度趋势分析 
Fig. 2.1 The analysis on academic attention trend of complex network by CNKI 
 
图2.2 CNKI 对复杂网络研究的用户关注度趋势分析 
Fig. 2.2 The analysis on subscriber attention trend of closed-loop supply chain by CNKI 
 
图2.3 CNKI 对量化交易研究的学术关注度趋势分析 
Fig. 2.3 The analysis on academic attention trend of quantitative matching trading by CNKI 
 
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-14- 
 
图2.4 CNKI 对量化交易研究的用户关注度趋势分析 
Fig. 2.4 The analysis on subscriber attention trend of quantitative matching trading by CNKI 
 
图2.5 CNKI 对机器学习研究的学术关注度趋势分析 
Fig.2.5 The analysis on academic attention trend of machine learning by CNKI 
 
图2.6 CNKI 对机器学习研究的用户关注度趋势分析 
Fig.2.6 The analysis on subscriber attention trend of machine learning by CNKI 
2.2 关于基于复杂网络的交易市场品种关联研究 
2.2.1 基于复杂网络理论的拓扑关联结构 
18 世纪伟大的瑞士数学家欧拉最先通过解决“格斯堡七桥问题”创建了一个新的数学
分支：图论和拓扑[25]。在以后的研究中，越来越多的学者开始注意到对网络研究的重要性
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-15- 
与必要性，首先人们关注的是规则网络，这类网络虽然结构简单明了但过于理想化，并不
能用来表示现实中的网络。 
Watts 与Strogatz (1998)[26]从规则网络着手，通过构造算法对规则网络进行改进，提出
了随机网络模型，从数学角度对复杂网络模型进行了系统性研究。描述了从规则网络到随
机的转换过程。根据结论，提出了小世界网络模型。发现小世界网络模型具有更高的聚类
和更小的平均路径，并且可以实际描述许多复杂系统。 
Barabasi 和Albert (2003)[27]表明，现实中许多的网络系统存在着动态演化现象，因此
在形成网络时需要考虑到网络的关联性和拓展性，从而提出了BA 网络模型，在该模型中
网络的顶点度分布具有幂率分布的特性，很少有节点具有大量连接，并且大多数节点具有
较少的连接。因此BA 模型也称为无标度网络，所提出的无标度网络模型对于研究复杂系
统的不平衡具有重要意义。 
Dimitrios Tsiotas 和Avraam Charakopoulos (2017)[28]为了将时间序列转换为复杂网络，
提出了算法的新扩展，该算法允许从复杂网络而不是当前适用的时间序列生成可见性图。
这种方法的目的是将时间序列领域中的可见性概念应用于复杂网络，以便转化为拓扑结构。
通过与原始网络相比较，研究由连通性产生的可见性拓扑，发现拓扑性是一种增加网络连
通性的特性，它有助于模式识别，并且它值得应用于复杂网络，以便揭示信号处理的潜力。 
Qipeng Liu 等(2018)[29]研究了顺序播种在复杂网络中的推广问题。采用经典的独立级
联模型 (ICM) 来表示扩展过程，检验了几个实际网络中的中心度、度量-度、K-壳和H-指
数，并确认度是衡量网络传播效率的一个良好指标。以幂律指数、密度、关联系数等参数
可调的无标度网络作为实验平台。通过仿真发现，在平均度较小、关联系数较大的度异构
网络中，顺序播种策略的优势较大。 
学者们不断建立和完善定量描述复杂网络的统计量和特征测度及其度量方法，分析网
络的拓扑结构特性及拓扑结构识别等特征，主要的统计特征量包括：度分布、度值的匹配
性和边权分布、平均最短路径，平均集聚系数等[15,18-20]。不同拓扑特征的广泛存在性被作
为研究不同现象以及做出预测的跳板。 
2.2.2 基于复杂网络理论的量化交易 
Chiu (1998)[30]指出量化投资理论最初只是衡量风险和收益的概念，后来逐渐成为资产
价格确定的模型。通过使用计算机程序来寻找符合投资者投资理念的股票，这种策略的选
择已成为是否在市场上获利的关键因素。Chiu 还指出，量化投资不是作为获取绝对回报的
工具，而是可以控制交易过程中的最大损失。 
Kanzano Bilal (2007)[31]提出在股票市场中构建网络的想法，这也是第一次通过股票价
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-16- 
格数据在股票市场建立网络。将股票市场看做一个网络，股票相当于节点，股票间的价格
关系相当于边，并以标普500 成分股为对象进行拓扑性分析。这一研究使得越来越多的学
者将复杂网络的思想用于金融领域，也开始通过品种价格的波动性来研究股票间的相关关
系。 
Guillaum (2018)[32]研究了基金与股票市场之间的关系，认为投资者不断关注基金市场，
因此股价下跌将导致资金回笼压力，从而导致股价下跌进一步加剧。通过复杂网络研究它
们之间的相关关系并监测基金以及股票收益率的变化表明，基金和股票市场的投资者虽然
担心股价呈现螺旋式下跌，但在短期市场回报中他们之间的影响非常小。 
Christos 等(2016)[33]通过使用统计中的向量自回复方法和复杂网络的拓扑分析，发现统
计结果服从标准分布。对于国外资产市场的发展，对此研究最合理的解释是量化交易。影
响交易的相关因素是暂时的价格压力。 
马源源(2011)[34]等以2001-2010 年间股票交易市场的价格数据作为研究对象，构建沪
深两市公司与其主要股东间的关联网络，证明该网络属于无标度网络并进行相关研究。通
过考虑网络的社团结构，模拟构建投资者与周边环境的复杂网络模型，探讨投资者由于周
边环境产生的心理变化对投资收益带来的影响。研究表明避免投资者主观想法进行投资决
策是非常有必要的。 
金秀等(2016)[35]对香港恒生指数的成分股网络进行了研究，利用粗粒度法生成股指波
动率的时间序列，并提出了一种提取股票市场结构绩效信息的方法。结果表明香港市场不
是随机变化的，而是动态稳定的并且发现香港股票市场小世界网络，其股票价格波动与流
动性密切相关。 
张伟平(2017)[36] 以上海市场的股票作为研究对象构建复杂网络，通过网络拓扑性分析
股票间的关系，通过度值和幂分布发现上海股票市场中存在少量影响较大的股票，即它的
价格变化会对其他股票产生较大的价格影响，而其他股票由于度值较小，不具有很大的影
响力。通过平面最大过滤图算法和最小生成树算法进行研究，通过聚类结构进行分析研究
市场价格波动是比较有效的。 
2.3 关于基于市场量化投资的配对交易决策研究 
2.3.1 基于市场量化投资的品种配对选择方法 
量化投资中的配对交易目前在期货市场应用比较少，起初更多的应用于股票市场，对
于品种的配对选择主要有以下三种方式：Perlin (2009) [37]，Faff (2010) [38]提出的Min 价差
法，对股票池中的股票进行统一标准化处理，选取距离最小的两支股票进行配对；Elliott 和
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-17- 
Hoek (2005) [39]提出的随机价差法。采用随机价差的方法利用股票价格的波动率选取取票
进行配对；Engle 和Granger (1987) [40]提出的协整检验法，作为目前配对交易中选择品种配
对最常见的方法，就是计算品种价格之间的相关性，通过相关性来描述和检验品种配对间
的关系。 
Whistler (2004) [41]将股票进行配对，通过计算股票间的相关系数，选择相关系数绝对
值靠近1 的股票对，通过计算股票间的价格比或价格差，应用统计分析和技术相关理论，
决定最终选取的股票对再设置进出场规则。当收盘价差或价比大于2时，开仓进行交易；
当收盘价差或价比超过3或者配对股票日收盘价超过二百日平均线时，强制止损，降低
风险的发生。 
曾俊涵(2017) [42]提出将配对交易应用于台湾股市交易市场，面对股票中的各类股票，
首先在股市MSCI 成分股中，对行业进行选择，选取有相关联系的几个行业，从中选择14
对股票，再根据股票对间相关系数的大小进行判定，观察1999 年至2003 年的股票价格趋
势变化，得出的结论是一年得到的参数最优，年化收益率达到82.46%。 
Dunis 和Gianluigi (2017) [43]认为分析股市日收盘股票价格大大降低了交易的机会，所
以提出采用高频数据进行分析，同样先在股票市场选择具有协整关系的股票对，再建立模
型，区别在于对于无协整关系的股票对也同样建立模型，以此来证明先通过选择配对股票
再建模获取收益更大，效果更明显。同时发现，股票价格间的协整程度越高，股票市场品
种配对的机会越多，获取收益的机会也就更频繁。 
2.3.2 基于市场量化投资的交易信号规则 
交易信号的建立源自股票的统计特征。对于股票对的统计特征的研究，Faff (2010) [38]，
Perlin (2009)[37]首先对交易信号做出研究，起初都以股票价差的均值或标准差作为交易信号
设置进出场规则。此外，Eberlein (2009)[44]，Wang (2009)[45]等人研究以股票价差的峰值作
为交易信号，并计算获取的收益，发现比传统的交易信号效果好，可以增加交易次数，从
而发现机会；Chen (2010)[46]等对从股票的交易过程进行研究，通过分析上下期股票对之间
的关系,发现可以通过配对达到收益正相关的效果。 
Gatev (2006)[47]，Faff (2010)[38]提出交易信号由价差的标准差来确定，开仓交易的信号
为价差大于二倍标准差，平仓的信号有两个，第一个是当价差回归均衡状态，第二个是价
差继续扩大，直到超过三倍标准差时，强制平仓，避免产生损失。大多数学者在此交易策
略的基础上进行了改进，大致可分为以下几种：Herlemont (2010)[48]突破了传统的交易信号
规则，开仓交易的信号有所改变，当价差第一次超过二倍标准差时不予建仓，待价差波动
扩大或缩小再次回到二倍标准差时在进行开仓交易；Papadakis (2007)[49]将交易信号设置为
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-18- 
在价差绝对值大于Trigger 值时进行开仓；Engelberg (2009) [50] 保持原有开仓信号规则不
变，对平仓信号进行重新设置，在开仓交易过程中判断价差在回复到均值水平最快的时间
段进行平仓；Wang (2009) [45] 在保证开仓和平仓信号不变的前提下，对止损信号进行重新
设置，以开仓时价差的绝对值系数进行止损；Kurun (2010) [51] 对开仓信号进行两种情况的
设置，将价差扩大2.5 倍或缩小至0.5 倍，这样可以根据市场的不同情况进行交易的进出
场规则。 
康瑞强(2016) [52]研究了以高频数据作为期货市场的研究对象是否可行。首先，通过期
货品种之间的协整关系找到配对品种，并验证它们是否具有长期均衡关系，可以配对。再
通过比较时变方差与常数方差对交易信号规则的设定，计算收益率，证明基于时变方差设
置开平仓阈值的效果获得收益更明显。 
陈样利(2018) [53]认为统计套利模型参数的方差具有较强的时变性，故讨论了在建立配
对模型之后，采用时变方差的标准差确定开平仓阈值，并尝试应用支持向量机对沪深股指
期货的价格作出预测，因为他认为统计套利可以通过人工智能的改进变得更加精准，最后
通过与实际走势的比较验证交易信号设置及模型的有效性。 
孔华强和王红兵(2017) [54]将统计套利中的协整配对策略应用在韩国期货市场中，以不
同到期时间的两个连续合约的收盘价作为研究对象，采用协整检验来判断二者之间的长期
均衡关系，并计算均衡系数。对于交易信号的设置，当残差序列超过
2
时开仓，进行交
易，当残差序列超过
3 时强制止损。结果发现，当除去交易成本后，2015 年8 月至2016
年7 月的套利收益率达到了33.23%，成功率达到了93.4%；2016 年8 月至2017 年7 月的
套利收益率达到了31.57%，成功率达到91.6%。 
2.4 关于强化学习的技术与应用研究 
2.4.1 强化学习技术的相关研究 
至今国内外很多学者对强化学习的理论及算法进行了研究，主要包括算法的收敛性、
泛化有关的基础理论研究以及算法的改进研究。Samuel (1959) [55] 是第一个提出强化学习
理论的学者，并且尝试将这一算法应用到游戏中，之后很多学者开始将强化学习应用于各
种领域；Yutton An (1988) [56] 提出了一种TD 算法，并详细分析了其理论，收敛性和实际
应用；Watkins (1992) [57] 提出了一种表格存储式算法来解决MDP 最优函数和策略；Phark 
等 (1998)[58]提出了一种结合Q 学习算法和TD 学习算法自适应轨的Q(λ)算法，以进一步提
高算法的收敛速度；Littman (1994) [58] 创造性地提出了最小极大Q 算法理论，该理论只针
对两台机器存在竞争的环境。随后Littman (2001) [60] 又提出了Friend-or-Foe-Q 算法，以便
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-19- 
解决一般对策模型。在该算法中，智能体之间存在两种关系：合作和竞争。Hu 和Wellman 
(2003) [61] 在强化学习算法中引入纳什均衡的想法，并提出Nash-Q 算法，此算法可以更快
的提高收敛速率。Hingh 等 (2007) [62] 提出名为Sarsa 学习算法的策略性Q 学习算法。Sarsa
学习算法以严格的TD 学习形式实现行为值函数的迭代，即选择的行为策略和值函数迭代
达成了高度的一致性。 
Tan (2016)[63]应用一种共享的强化学习思想，通过共享系统环境，令多智能体系统之间
分享学习到的策略，从而达到加快学习速率。Matsric (2017) [64]通过研究如何在可行空间内
进行行为策略和周围环境的学习，来模拟机器人模型行为系统。Sachiyo Arai 和Katia Sycara 
(2017) [65]提出了利润分配方法，并在具有资源冲突的动态多智能体环境中成功应用。
Wiering M (2017) [66] 通过一个智能体的行为，让另一个智能体当做历史行为去学习训练再
进行模型的构建，这样可以将两个智能体联系起来形成合作关系。McPartland M (2017) [67] 
成功将强化学习应用于射击游戏中，结果显示强化学习经过学习后进行测试，射中靶心的
次数高于射击爱好者。国外对机器学习的研究如火如荼的进行，国内近几年也开始对机器
学习进行研究并加以应用。 
在机器学习发展如火如荼的同时，国内学者在该领域也取得了不错的研究进展。高阳
(2018) [68]在他的文章中提出了一种元方法Q 算法，并证明了算法的收敛性。范波(2018) [69]
将协调的理论应用于强化学习，并引入分布式思想，即通过多智能体之间的相互协调，将
任务进行分解，此方法成功应用于足球游戏中。 
在提出将强化学习应用于实际的思想之后，如果要将其应用到实践中，还有许多问题
需要解决。为了使强化学习适用于一些大规模和复杂的问题，并广泛应用于实际的多智能
体系统已成为当前研究的热门领域，它已成为强化学习的重要研究方向。 
2.4.2 强化学习应用的相关研究 
机器学习的研究使得强化学习方法受到了学者们的更多关注，目前其技术已经应用于
人工智能等各个领域。Scuteri (2018) [70]等在强化学习中引入模糊控制理论，并将其应用于
停车系统，以实现自动停车的目的；张汝波(2016) [71]通过强化学习算法成功让机器人学会
在实验中躲避障碍物，达到目标终点。Bianchi (2015) [72]基于启发式函数影响运动的选择，
提出了一种启发式Q 学习算法，并将其应用于实验。结果表明简单的启发式函数也具有强
化学习的效果；Yasunobu (2017) [73]在文章中提出了一种反向Q-学习的方法，这样既使数据
可以多样化也可以不断学习寻取最优值；Song (2016) [74]考虑将神经网络引入强化学习算法，
以实现比传统强化学习更快的学习速率和策略。 
刘俊一等(2018) [75]提出了一种基于BP 神经网络的双层启发式强化学习方法，它改变
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-20- 
了传统强化学习过程的盲目性。通过在线获取动态知识用于激发基于表格的Q 学习过程，
训练提高学习效率。此方法不仅具有标准Q 学习的灵活性，而且利用了神经网络的泛化性
能，为解决大状态空间中的强化学习问题提供了一种可行的方法。 
Regina Padmanabhan (2017) [76]提出了一种基于强化学习 (RL) 的无模型方法，用于癌
症化疗药物剂量的闭环控制。具体而言，Q-学习算法用于开发用于癌症化疗药物剂量的最
佳控制器。使用模拟患者呈现数值示例以说明所提出的基于RL 控制器的性能。RL 可用于
控制药物处置，因为它不需要用于设计控制器的系统动力学的数学模型，也可以使用患者
对控制动作的响应来学习最优控制策略。 
Shahrabi (2017) [77]使用Q 因子算法的强化学习 (RL) 来增强为动态作业车间调度
(DJSS) 问题提出的调度方法的性能，该问题考虑随机作业到达和机器故障。实际上，通过
不断改进来自RL 的策略来选择任何重新安排点处的优化过程的参数。调度方法基于可变
邻域搜索(VNS)以解决DJSS 问题。还引入了一种新方法来基于所选参数的质量来计算学习
过程中的奖励值。 
KennethBogert 等 (2018) [78]将强化学习算法成功地引入到机器人路径规划过程中，该
方法用于实现机器人的移动路径规划，提高对环境的适应性。通过使用参数W 来限制移动
机器人在动态环境中的状态数，可以更有效地减小Q值表的维数，便于快速找到历史数据，
从而提高模型系统的学习效率。 
2.5 已有研究成果的评述 
2.5.1 主要贡献 
通过对已有的关于复杂网络理论及其应用的研究、关于配对交易配对品种选择方法及
交易规则的研究以及关于机器学习中强化学习的研究进行综述与归纳分析，其贡献可以总
结为以下三点： 
(1) 指明了期货市场量化交易研究的重要意义与价值。已有的关于量化交易的研究，
Eberlein (2009) [44]首先提出配对交易的品种选择与交易信号，曾俊涵(2017) [42]通过对台湾
证券市场进行量化配对选择的研究、陈祥利(2018) [53]采用时变方差的标准差确定开平仓阈
值，通过与实际走势的比较验证交易信号设置及模型的有效性对进出场交易信号的研究，
都对量化交易的配对研究指明了方向。尤其近两年关于期货市场背景下的量化交易研究，
更是表明了关于期货市场量化投资配对问题的研究是一个非常重要的研究课题，其研究具
有重要的实际意义和价值。 
(2) 为基于复杂网络和强化学习的量化投资品种组合选择与交易策略研究奠定了理论
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-21- 
基础。Tsiotas Dakker (2018) [34]的研究中考虑了股票市场投资收益会受到心理因素的影响，
构建了复杂投资网络模型，并对复杂网络进行测试。测试的结果表明，投资者心理受到周
围环境的影响，从而会影响市场的稳定性。TAN (2016) [63]基于强化学习算法，提出非套利
型的高频交易系统，打破了传统量化交易研究中主观设定交易信号阈值的局限，使得决策
结果随现实情况适时改变，更能接近现实，为本文将强化学习引入到期货市场量化交易配
对研究中提供了理论依据。 
(3) 为基于复杂网络和强化学习的量化投资品种组合选择与交易策略研究提供了方法
支持。已有的基于强化学习股票市场的研究文献的数学模型为本文的研究提供了有效地方
法支持，如高阳(2018) [68]利用多智能体的Q-learning 算法提出的股票交易框架做出投资决
策并进行股票仿真交易研究为本文的数学建模提供了基准性的方法支持；刘俊一等(2018) 
[75]将自适应的强化学习模式引入配对交易模型问题的研究为本文强化学习下期货市场量
化交易模型的构建提供了技术保障。 
2.5.2 不足之处 
目前，对于量化投资决策的研究在不断的丰富，但还处在发展阶段，存在一些不足之
处。主要体现在以下三个方面： 
(1) 对于期货市场的量化投资缺乏系统性研究。目前国内外的众多文献主要都针对股票
市场研究量化交易配对，如孔华强和王红兵(2017) [54]将统计套利中的协整配对策略应用在
韩国期货市场；Dunis 和Gianluigi (2017) [43]建议使用高频数据进行分析，并选择股票市场
中具有协整关系的股票对。配对交易目前更多的应用在股票市场，对于期货市场领域的研
究显得尤为缺乏。 
(2) 对于考虑复杂网络拓扑性结构的量化投资品种选择研究更为少见。Christos 等 
(2016) [33]提供了选择和测试配对组合的理论和方法，并测试了配对交易策略在全球各种商
品市场的有效性。结果表明配对交易策略可适用于大多交易市场。然而，随着套利交易越
来越受欢迎，市场效率越来越高，统计套利的利润机会越来越有限。将复杂网络应用在期
货市场的品种选择的研究还不多见，还没有形成系统的理论方法体系。 
(3) 对于考虑强化学习的量化投资品种交易策略尚为少见。传统的参数大多采用固定
常数或标准差设置为开平仓阈值，这种结果往往会造成交易次数受限、收益无法达到最大
等情况。之后很多学者通过优化量化参数进行量化交易，如Kurun (2010) [51]对开仓信号进
行两种情况的设置，将价差扩大2.5 倍或缩小至0.5 倍，这样可以根据市场的不同情况进
行交易的进出场规则；康瑞强 (2016)[52]虽然对交易信号做出改进，但将强化学习应用到量
化投资交易信号策略的研究少之又少。 
东北大学硕士学位论文 
 
第2 章  相关研究文献综述 
-22- 
2.5.3 对本文研究的启示 
已有的量化投资配对品种选择与交易策略问题的研究为本文的研究奠定了坚实的基
础，同时为本文进一步的研究提供了研究思路。 
(1) 可以拓展研究考虑复杂网络的量化投资品种组合选择的问题。对已有的量化投资
配对品种选择方法进行归纳与分析，结合复杂网络的拓扑性对构建的期货波动网络进行分
析，能够更合理的选择合适的期货品种进行配对，也为后面的交易策略奠定了基础。 
(2) 可以拓展研究基于强化学习的量化投资交易策略的问题。对已有的量化交易配对
策略和强化学习的应用研究进行归纳与分析，总结出期货市场量化交易配对理论与建模思
想，结合强化学习理论的数学描述，在期货市场量化交易配对问题的研究中引入强化学习
的自适应调整交易信号行为，能够更好地解释现实中投资者进行量化交易配对的决策行为。 
(3) 可以继续研究基于复杂网络和强化学习的量化投资品种组合与交易策略问题。在
运用复杂网络拓扑性进行量化交易配对选择的基础上，继续研究考虑强化学习期货市场量
化交易的交易信号相关研究，使研究结果更加符合现实需要。 
2.4 本章小结 
本章综述了近来国内外学者对于复杂网络拓扑结构及应用的研究、量化投资中配对品
种选择的方法及交易信号的研究以及机器学习中强化学习算法及应用的相关研究，并进行
了分析和总结，对已有的研究的主要贡献、不足之处和对本文研究的启示进行了阐述，更
加明确了未来需要深入的研究课题和本文的具体研究方向，为后面章节的研究工作打好了
基础。
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-23- 
第3 章 基本概念界定与理论基础 
本章将针对复杂网络和强化学习对量化投资中品种组合选择与交易策略问题，分别对
复杂网络、量化交易配对品种的选择、配对交易规则及强化学习四个方面的理论基础进行
阐述，并在此基础上，给出量化投资品种组合选择与交易策略的研究框架。 
3.1 复杂网络 
3.1.1 复杂网络拓扑性结构的概念 
复杂网络是复杂系统的抽象和描述，当将组成单元抽象为节点并将单元之间的关系抽
象为边时，任何复杂系统都可以看作是一个网络。生活中很多领域可以构建复杂网络，如
社会关系领域、医疗领域、科学领域等，个体和个体或个体与周围环境间会产生很多不同
的连接关系，这种连接关系联系在一起就形成了一个网络[79]。复杂网络可以反映真实情况
中对象间的强弱关系，它作为现实的拓扑抽象，由节点和边构成整个网络，网络图中节点
和节点间通过边构成联系，而边分为无向边和有向边，网络分为无向和有向网络。无向网
络表明节点之间存在相关性且没有方向性；有向网络指从一个节点到另一个节点存在关系，
并且存在方向。带有数字的边形成的网络为加权网络，边上的数字相当于权重，代表两节
点间的强弱关系，无数字的边构成的网络则为无权网络。复杂网络可以代表很多体系结构，
如社会关系可以构建出一个复杂网络结构。不同的人可以作为节点，是否存在关系作为连
接边的判断，而两人之间陌生到熟络的关系程度可以作为权重的大小或者边的粗细。本文
建立的复杂网络节点表示期货市场中与黑色金属相关的期货品种，用无向边的粗细表示品
种间相关关系强弱。 
给出复杂网络的定义：
( ,
)
G = V E 。网络是由节点集
( )
V G 与边集
( )
E G 共同组成的，为
( )
{ ,
,
,
,
}
1
2
3
4
5
V G = v v v v v
和
( )
{(
,
),(
,
),(
,
),(
,
)}
2
1
2
3
4
1
5
3
E G =
v v
v v
v v
v v
。E 的每条边都会有V 中的
一对节点与之对应，如果节点
1v 和
2v 构成的边(
,
)
2
1
v v
和( ,
)
1
2
v v
是同一条边，则构成的网络
为无向网络。如果一个图没有构成环或者重复的边，那么这个图为简单图[80]。 
3.1.2 常见网络的类型 
(1) 规则网络 
规则网络即网络中任意两个节点之间的关系遵循一定的规则，通常每个节点周围的节
点数量相同[81]。在规则网络中，网络具有较大的平均路径和聚类特性。比较常见的规则网
络如图3.1 所示，分别代表全局耦合网络、最近邻耦合网络与星形耦合网络。 
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-24- 
 
图3.1 常见的规则网络 
Fig. 3.1 Common rule networks 
(2) 随机网络 
随机网络与规则网络完全不同。随机网络是一些节点通过随机链接形成的一种复杂网
络，在这种情况下，网络节点间是否存在连边不再是确定的情况，而是由一定的概率来决
定。随机网络中比较经典的网络模型是ER 随机图模型，假设有N 个节点,每对节点间以相
同的概率p 链接，这样就会得到一个节点数为N 。对于任意一个给定的p ，根据此概率生
成的网络有两种相反的情况，即要么几乎具有某一种性质，要么几乎都没有该性质，随机
网络的许多性质都具有涌现的现象[82]。规则网络的聚类系数和平均路径都相对较大，而随
机网络的平均路径与聚类系数都较小，这两种网络是比较极端的情况，都不能较好的表示
现实中的网络。因此学者们提出了一些更能反映显示的网络，其中比较典型的是小世界网
络模型。 
(3) 小世界网络 
小世界理论最早来源于哈佛大学一位社会心理学家的实验，此次实验的结果被称为
“六度分离”理论。后来人们发现，不仅仅是在人际关系网，在互联网、商业关系网、生
物网络等等一系列网络中均存在这种现象，因此人们把这种现象称为网络的小世界特性。
小世界特性是指：尽管有些网络规模非常大，但其中任意两点间的平均距离却相对较小[83]。
通过选取一个最近邻耦合网络，进行边的随机化重连，对网络中的每条边，将边的一个顶
点保持不变，另一个顶点以概率p 从其它点中随机选取，其中规定任意两个不同节点间最
多存在一条边，并且网络中不存在环。当p 取0 时为规则网络，p 取1 时为随机网络，通
过p 可以实现从规则到随机网络的转化。通过所描述的构造算法，可以得到较大聚类系数、
较小平均路径长度的网络，即为小世界网络。 
3.1.3 复杂网络拓扑结构特征 
研究复杂网络时必然会涉及到网络的拓扑结构特征，包括度与度分布、平均路径、聚
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-25- 
集系数等，下面将对所涉及的网络结构性质进行介绍。 
(1) 平均路径 
平均路径与整个网络的稳定性有很大关系，也体现了节点在网络中的传播能力[84]。网
络直径越长，传播能力越差，网络直径处在较低的水平可以确保节点之间的关系也保持在
可预测的水平上。即从节点i 到节点j 的所有路径中，边数最少的一条，为最短路径，所有
节点最短路径的平均值为平均路径，见式(3.1)。 
1
=
 d
(
-1)
ij
APL
N N
i ≠j ∈G
                     (3.1) 
(2) 度 
一个节点的度值指的是与该点直接相连的边的个数。边的个数越多，度值就越大，与
该期货品种波动性相关的品种数量越多，和该节点的关系就越密切，也就是说，该节点的
权力越大。比如RB（螺纹钢）的度值为7，则表示期货市场中有7 种期货品种的价格与螺
纹钢价格相关。 
(3) 加权度 
度值考虑的为连入连出节点的变数，即与每个品种相关的其他品种数量，但未考虑到
边的权重，即加权度，本文指的是各节点间的波动相关性，见式(3.2)。 
ÎS
= 
i
i
ij
j
ω
ρ                             (3.2) 
其中，
iS 为节点i 周围的节点集合，
ij
ρ 为节点i 到节点j 的权重，即波动相关性。加权
度越高，说明期货品种与和它相关的产品的联系越紧密，还可以通过加权度和度值算得平
均相关系数来反映整体网络特征。比如加权度值最高的期货品种是WR（线材），它的度值
是8，加权度值是5.24，则说明WR 与8 种期货品种日波动价格紧密相关，平均相关系数
计算见式(3.3)。 
i
ij
ω
ρ =
N
                           (3.3) 
(4) 平均集聚系数 
集聚系数表示一个图形中节点聚集程度的系数，指的是一些具有相同特性的节点聚集
在一起，可以反映节点之间的密集程度。相对应地，举个现实生活中的例子，假如你和A、
B 都是朋友，那么A、B 也可能成为朋友。集聚系数可以分为以下三类：全部集聚，局部
集聚和平均集聚。 
局部集聚表示了一个节点与相邻结点的紧密程度。Watts 和Steven Strogatz (1998) [26]将
局部集聚系数应用于小世界网络理论中来表示紧密程度。
(
)
G = M,N 表示图G 由节点M 和
边N 构成；
ij
N 表示连接节点i 和节点j 的边；
{
,
}




i
j
j
ij
E
m n
N
n
E 表示
i
m 的第i 个相邻
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-26- 
节点；
ik 表示
i
m 相邻节点的数量。 
对于一个有向图，
ij
n 与
ji
n 是不同的，因而对于每个相邻邻节点
iE 在邻节点之间可能
存在
(
1)

i
i
k k
条边。而本文构建的是无向网络，假设节点i 和
ij
n 条边相连，有
ik 个节点和这
ij
n 条边直接相连，那么这
ik 个节点互相之间最多可能存在的边数为
(
1) / 2

i
i
k k
。局部聚集
系数等于其相邻之间的连接数与它们之间所有可能连接数的比。[85-86]，见式(3.4)。 


2
:
,
( ),
(
1)




jk
j
k
jk
i
i
i
n
m m
L i n
N
C
k k
                     (3.4) 
考虑到网络集聚是由三元组构成的多个三角形连接组成，局部集聚系数也可以等价为
式(3.5)。 
和节点相连的三角形的个数
以节点为中心的三角形的个数
i
i
C
i
                  (3.5) 
又对于度值为1 和0 的节点，公式的分子分母均为0，即Ci=0，所以公式又可以写为
式(3.6)。 
3*

网络中三角形个数
连通的三元组个数
iC
                      (3.6) 
又考虑到节点的闭三电组和开三电组，又可以等价为式(3.7)所示,再带入得到式(3.8)。 
( )
( ) +
( )
G
i
i
G
i
G
i
λ
v
C = τ
v
λ
v
                          (3.7) 
1
( ) +
( )
(
- 2) =
(
-1)
2
G
i
G
i
i
i
i
τ
v
λ
v = C k
k k
                (3.8) 
i
C 属于[0,1] 区间，
i
C 越大，说明接近完全网络，节点之间全部相互连接；
i
C 越小，说
明网络呈近似星形网络。 
平均集聚系数指的是所有结点m 的局部集聚系数的均值，可通过平均集聚系数来表示
整体网络的聚集程度大小，见式(3.9)。 
1


M
i
i-1
C
C
M
                           (3.9) 
对于本文来说，平均集聚系数越接近于1，说明波动网络的集聚系数相对较高，黑色
金属品种之间的集聚效应较强。 
3.2 配对品种的选择 
3.2.1 配对品种筛选条件 
要选择品种进行配对，就要知道筛选的条件，可以分为以下两点：  
第一，选择交易量大且活跃性高的期货进行组合，来保证投资者获取稳定收益[21]。交
易量小，流动性低的产品价格可能无法恢复均衡，这将给套利带来很大风险。实际的交易
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-27- 
市场往往不是完全有效的，通常会有一些套利机会。要想套利成功，只有市场的有效性达
到一定水平才能回归均衡。而通过买卖交易量大，活跃性高的期货，认为交易市场是有效
的，可以进行品种的配对组合并通过价差的短暂偏离进行交易，等到最终价格回归均衡再
通过平仓来获取收益[80]。 
第二，选择交易历史足够长的品种进行组合，便于投资者对价格数据进行准确分析。
选择配对品种组合的依据是对两个期货品种的价格趋势进行判断。如果交易时间很短，则
很难发现两者之间是否具有长期协整关系。所以要选择交易历史时间长且长度近似的品种
进行配对研究。 
3.2.2 配对选择方法 
期货市场拥有大量的期货品种且不同的国内外期货交易所，可以选择配对的期货品种
也相当繁多，盲目或随机的挑选品种即费时得到的结果又不理想[79-81]。因此，首先通过缩
小期货品种选择范围，再选取合适的品种进行配对是配对交易的重要步骤之一。 
对于期货品种的选择方法，目前多使用最小离差平方和法和协整法。Gatev (2006) [47]首
先将其应用于股票市场，提出一价定律，选择来自库存池的标准化扩散系列的最小平方和
的股票对作为交易对象。该方法主要通过数据分析方法不需要建模，避免了模型过拟合和
参数估计误差的影响。但是最小离差平方和法也存在着一定的问题，一是价差的稳定性不
能保证，导致效果较差；二是选择的配对品种还是具有一定的投资者主观意见，更多地按
照投资者以往经验作出判断，也没有确定两股票之间的量化关系，容易造成非理性投资现
象。 
配对交易策略的必要条件是期货配对品种高度相关且具有长期稳定变化的趋势，协整
的思想可以用来检验品种价差序列是否满足长期稳定，所以将协整应用于配对交易策略能
更好地获取稳定收益。误差修正模型可以避免投资者的主观思想，减少非理性投资的发生
[22]。 
3.3 配对交易规则 
3.3.1 配对交易原理 
配对交易作为投资市场的中性策略，首先通过历史寻找价格趋势相关性强的资产构成
配对组合在市场中获取收益，凭借配对资产或品种的强相关性出现的短暂价差偏离均值水
平，卖出相对高估的资产或品种同时买入相对低估的资产或品种，当价差逐渐回到均值水
平时，通过平仓获取价差收敛后得到的收益。配对交易主要有以下两个特点： 
 (1) 配对交易作为市场中性交易策略，尽可能地避免了投资者低买高卖的主观意念，
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-28- 
控制了投资者追涨杀跌的非理性做法，这样就可以降低由于投资者的非理性投资造成的市
场资产价格波动。配对交易的前提是配对的资产最后必须能回到长期均衡水平，由于非理
性因素造成的资产价差产生偏离均值水平的变动，利用配对交易的策略可以通过价差的偏
离进行开仓交易，当两者价差回复均衡时，进行平仓，获取由于价差波动回复后的报酬收
益。另外考虑到假设前提，还要设置一个预期止损位，倘若资产价差持续扩大不满足假设
前提，要果断进行止损。 
 (2) 配对交易作为市场中性交易策略，尽可能地避免了系统风险和个别风险。考虑到
配对交易的整个处理流程，尽管单一资产的投资存在系统性风险和个别风险，但通过历史
选择的配对资产或品种的价格高度正相关，而且交易过程中买进一种资产的同时会卖出另
一种资产，同时进行两种交易可以成功对冲系统风险。所以配对交易从整体上看只承担了
配对资产的个别风险，通过价差恢复到长期均衡水平又将个别风险转化为资产收益报酬，
配对交易获取的收益与市场涨跌无关[85]。 
3.3.2 配对交易信号 
在选择合适的配对品种之后，需要继续进行交易策略，即对配对交易中的交易信号进
行设置。设置交易信号是为了确定开平仓的时机，从而获取收益。目前的研究针对交易信
号的规则一般以价差标准差或者固定常数设置开仓及止损阈值，交易信号的设置会产生不
同的影响。开仓阈值设置过大会导致交易次数减少，价差不易到达开仓水平；过小又会导
致交易次数太频繁，增加交易费用[87]。止损阈值也是同样的道理，设置太大可能导致配对
品种不再具有协整关系，产生单个交易中的最大回撤以及巨大损失。过小又会导致交易过
程中价差碰触止损线的次数增多，从而产生较多亏损次数。 
3.4 强化学习 
3.4.1 强化学习的概念 
强化学习是机器学习的一种，又可称为增强学习或激励学习，是目前人工智能常用的
技术之一[88]。强化学习的优点是具有很强的适应能力，可以随着时间不断的更新学习状态
和学习策略，无需预先设置专家系统，无需预见受控对象和环境的模型。强制学习的目标
就是在与环境的暂时互动中学习行为策略，以获得最大奖励。强化学习系统涉及两个主体，
即智能体和环境，环境拥有各种可能的复杂状态，所有状态构成状态集S 。在t 时刻，当智
能体面对环境状态
(
)

t
t
s s
S 及前一时刻
1

t
环境状态改变的瞬时赏赏值tr 时，可在其行为
集A 中选取一个合适的行为或称动作
(
)

t
t
a a
A 来执行，于是环境状态转移到
1

tS
，同时智
能体立即得到来自环境状态改变的瞬时奖赏
1

tR
，根据此奖励，智能体更新其在
ts 状态和动
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-29- 
作上获得的经验，然后决策下一时刻
1

t
的
1

ta
动作。依此循环往复，智能体通过与环境不
断地交互作用，不断尝试并调整自身行为，不断学习如何把状态映射到动作以获得最大长
期奖赏。 
3.4.2 强化学习基本原理 
强化学习作为机器学习的一种，具有很强的自适应能力、实时学习和终身学习的特点。
强化学习过程中需要掌握以下四个函数，来实现目标最优策略[89-91]。 
(1) 策略函数 
:
( , )
t
D S
A
D s a
t 
s
a 

用策略函数来确定所有状态下需要进行的动作
表示在时刻、状态下选择动作
，
的概率
 
(2) 状态转移概率函数 
:
( )
P S
A
P S
P


表示从某个状态转移到另一个状态所有可
，
能的概率 
'
a
ss
s
s
P
a
'
为系统在状态时实施动作使状态转移到的概率 
(3) 奖赏函数 
:
( )
R S
A
R S


来自于动作与状态交互期间
，
得到的奖赏信号 
在s 状态下实施a 动作导致状态转移至
's
的期望奖赏值为
'
a
ss
R
， 
'
1
1
{
|
,
,
'}






a
ss
t
t
t
t
R
E r
s
s a
a s
s
，
1

tr
状态从s 变至's 的奖赏值。相应地，为简便计，用'a
表示在
1

t
时刻实施的
1

ta
动作。奖赏是在这个状态下采取的一个导致相对于后面所有状态
的一个价值。 
(4) 值函数 
分为状态值函数
( )
D
V
s 和动作行为值函数
( , )
D
Q
s a 。状态值函数用来估计s状态对于智
能体来说究竟好到什么程度，其衡量指标采用未来总的期望奖赏。由于未来奖赏还有赖于
未来的动作，因此该函数还与具体的策略D有关。𝑉𝑑(𝑠)为从s状态开始一直采用D策略得到
的期望奖赏，即
1
0
( )
{
|
}






D
D
k
t k
t
k
V
s
E
γ r
s
s 。 
其中，
D
E 为一直采用D策略所对应的期望值；γ 为未来奖赏值折现至现在状态的折扣
率
[0,1]

γ
； 
1
(
)
(
1)
0,1,2,
t+k+
r
t
k
t
k
k





 ：从
至
的瞬时奖赏值,
 
1
0
( , )
{
|
,
}







D
D
k
t k
t
t
k
Q
s a
E
γ r
s
s a
a  
根据动态规划相关理论的相关理论，上述两个价值函数是未来折现后的总值，这是对
长期效应的全局评价，两者都可以作为目标函数。强化学习系统的任务就是求得最优策略
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-30- 
*
D ，使值函数达到最大，即
*
arg max
( ),


D
D
D
V
s s S 。 
3.4.3 强化学习一般算法 
(1) 蒙特卡洛算法 
蒙特卡洛方法又被称为统计模拟方法，需要通过很多的样本数据进行训练或和环境之
间的交换来得到训练参数，最后训练模型以得到最接近的真实值。其中依据的数学理论就
是大数定律，算法的本质就是基于样本回报值的平均化来求解值函数[29,69,92]。蒙特卡罗方
法的值函数的迭代公式为
1
( )
(
)
[
( )]




t
t
t
t
V s
V S
α r
V s
，其中
( )
t
V s
为t 时刻的奖赏值，tr 为
步长参数。 
蒙特卡洛算法的迭代过程如下：
0
1
*
0
1
2
*







E
I
E
I
E
I
E
d
d
d
d
q
d
q
d
d
q 。说明值函数需
要很多轮后才能收敛并接近其真实值，这种情况下策略迭代是低效的。在值迭代算法中，
只需导出每个策略的近似值，然后通过使用该近似不断更新模型以获得近似策略，最后将
其收敛到最优策略。 
(2) 时间差分算法 
时差算法基于蒙特卡罗和动态规划的思想。与蒙特卡罗算法相比，时差不仅具有模型
独立性的优点，而且充分利用了环境经验。通过每个动作的长期结果更新先前的状态值函
数，常用的迭代算法见式(3.10)。 
     
1
( )
(
)
[
(
)
( )]





t
t
t
t
t
V s
V S
α r
γV S
V s
                  (3.10) 
其中，∝为学习速率，
( )
t
V s
和
1
(
)

t
V s
分别表示智能体在t 和t+1 时访问环境状态时估
计的状态值参数。
( )
t
V s
是借助后一状态
1
(
)

t
V s
来更新的，称为TD prediction。TD 算法伪
代码如图3.2 所示。 
 
图3.2 TD 算法伪代码 
Fig. 3.2 TD algorithm pseudo code 
 (3) Sarsa 算法 
通常来说强化学习算法可以简单地分为在策略与离策略。Sarsa 算法属于在策略，估计
的是动作值函数而并不是值函数。Sarsa 的动作值函数更新见式(3.11)。 
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-31- 
  
1
1
1
( ,
)
( ,
)
[
(
,
)
( ,
)]







t
t
t
t
t
t
t
t
t
Q s a
Q s a
α r
γQ s
a
Q s a
          (3.11) 
通过以上的方法，对于任何一个非终止的
ts 在到达下一个
1

ts
后都可以利用式(3.24)对
( ,
)
t
t
Q s a 进行更新，若
ts 是终止状态，则需要令
1
1
(
0,
0)




t
t
Q s
a
。之所以称此算法为Sarsa
算法，是因为网络的动作值函数的每次更新都和
1
1
( ,
,
,
)


t
t
t
t
s a s
a
有关，Sarsa 算法最后得到
了所有状态-动作的Q 函数，并可以通过Q 函数输出最优决策
*
π 。Sarsa 算法的伪代码如图
3.3 所示。 
 
图3.3 Sarsa 算法伪代码 
Fig. 3.3 Sarsa algorithm pseudo code  
(4) Q-Learning 算法 
通常在Q-learning 算法当中，动作值函数更新的时候采取的策略与选取动作的时候采
取的策略往往遵循的是不同的策略，这种方式叫做离策略[92]。Q-learning 算法的动作值函
数更新公式见式(3.12)。 
    
1
1
( ,
)
( ,
)
[
max
(
, )
( ,
)]






t
t
t
t
t
Q
t
t
t
Q s a
Q s a
α r
λ
Q s
a
Q s a
         (3.12) 
通过比较可以发现 Q-learning 算法与Sarsa 算法最大的不同就是在更新Q 值的方法
上，Q-learning 算法模型直接使用到了最大的
1
(
, )

t
Q s
a 值，相当于直接采用
1
(
, )

t
Q s
a 值最大
的动作作为状态的动作，并且与当前执行的策略，即选取动作
ta 时采用的策略没有关系。
通过离策略的方式简化了证明算法分析和收敛性证明的难度并且使得它的收敛性很早。Q-
learning 算法的伪代码如图3.4 所示。 
 
图3.4 Q 算法伪代码 
Fig. 3.4 Q algorithm pseudo code  
东北大学硕士学位论文 
 
第3 章  基本概念界定与理论基础 
-32- 
3.5 本章小结 
本章主要介绍了复杂网络、配对交易以及强化学习的相关概念和理论基础及相关分析。
首先对复杂网络的概念及拓扑结构特性进行阐述，以便针对期货市场繁多的产品种类进行
分析；其次，对配对交易的品种筛选条件及方法、配对交易规则进行了介绍，以便更好地
分析期货市场品种的配对组合。最后，对本文将要应用的强化学习算法进行了介绍，以便
更好地应用于配对交易模型中，验证方法的有效性。
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-33- 
第4 章 考虑复杂网络的多品种量化投资组合研
究 
本章针对期货商品在各类大宗期货市场的价格波动现象，基于期货品种价格间的相关
关系，建立了价格波动网络模型，并对网络进行了如下分析：首先，介绍了大宗期货市场
中期货品种的基本统计信息并对数据进行筛选；然后，给出波动相关性的计算方法并对网
络构建及拓扑性做出说明；最后通过算例实验比较，对期货品种价格波动关联网络进行了
研究，确定配对交易的品种。 
4.1 问题描述与符号说明 
4.1.1 问题描述 
随着融资融券业务正式推出，中国金融交易市场迎来了做空机制，从而衍生出了一系
列的统计套利策略，同样也延伸到期货领域[21]。其中，配对交易策略在统计套利投资策略
中受到了广泛的重视和应用，但仅仅依靠原有的办法已经无法满足投资者的收益，但种类
的增多并没有带动期货数量的相应上涨， 因此配对交易作为期货市场中最主要的一种交
易策略，配对交易中首先最主要的是选出最合适的配对品种进行交易，其中相关系数是最
常应用的方法，即将待配对期货两两配对并计算相关系数，然后观察配对期货之间的相关
性，之后选择相关系数较大的期货对作为配对期货参与后面的计算。这种方法简单直观，
直接使用期货价格历史数据计算相关性，但是由于相关系数方法是一种纯统计的方法，它
不依赖于经济模型，因此也缺乏对市场判断能力，复杂网络近年来逐渐应用于金融领域，
期货市场可以看作一个网络，通过复杂网络中的结构特性可以对期货市场的品种进行进一
步的分析，从而选取更有效地品种进行配对组合。故考虑复杂网络拓扑性进行配对品种的
选择是十分必要的。 
4.1.2 符号说明 
在考虑价格波动的品种配对网络模型问题中，首先最主要的是选出合适的配对品种
进行交易，其中相关系数是最常应用的方法，即将期货进行波动相关性的计算再进行两
两配对。但对于黑色金属商品期货而言，仅仅选择价格走势趋同性最强的品种进行配对
比较片面，可能会造成品种在交易期间由于过于相似的价格走势无法产生价差偏离，无
法实现交易，故利用复杂网络拓扑性结构进行网络特征的综合分析，避免仅考虑相关性
造成品种间价格趋势过于相似无法产生偏离，难以实现配对交易的情况发生。本章主要
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-34- 
通过数据统计及分析的方式对期货配对品种进行选择，使用的符号说明如下： 
：收盘价，
itP
i 
t 
表示第个品种在第个交易日的收盘价 
：波动率，
it
V
i
t
表示第个品种在第个交易日的价格波动率 
(
)
it
E V
i
t
：第个品种在时期内波动率的期望值 
iS
节点的
：期货品种
临近集合 
ij
ρ ：节点i 到节点j 的权重，即波动相关性 
i
ω：加权度 
C ：平均集聚系数 
ij
N ：表示连接节点i 和节点j 的边 
1
2
(
,
,
)

k
α
α α
α
：协整向量 
1
2
3
,
,
,
,
t
t
t
kt
X
X
X
X ：品种价格d 阶单整序列 
4.2 数据的收集 
4.2.1 数据的选取 
本章旨在建立期货市场品种间的期货关联网络，数据源文件来自万德数据库，数据收
集时间跨度为数据收集时间为1994-2018 年，截至到2018 年5 月，所需要的数据包括： 
(1) 大宗期货交易市场期货品种与期货结构数据 
通过万德数据库查询黑色金属相关期货品种与期货结构数据，收集数据的时间区间为
2017-2018 年。  
(2) 黑色金属期货原始可用数据 
期货市场发展历史久远，不同的期货是逐步分批进行的，不同的产品生命周期及跨度
不同，导致起止日期差别很大。选取产品品种多且交投活跃的时间段，保证研究数据数目。 
收集各品种起止日期及数据的个数，便于更好的进行数据筛选。 
(3) 与黑色金属期货相关的市值数据 
通过万德金融数据库搜集2017-2018 年黑色金属期货的日市值价格数据。 
研究期货市场的品种配对组合选择最容易得到的数据就是时间价格信息。从万德数据
库中可以找到相关数据。由于要研究配对交易所选取品种的有效性，所选取数据应具有以
下特征： 
(1) 选择活跃的期货品种。即更多交易者随时随地都存在买卖交易，在期货市场做出
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-35- 
投资决策时，公司或个人倾向于关注最活跃的期货品种。活跃的月份在交易市场中较为集
中。期货市场具有强大的“抗震性”所以比较具有代表性，从而分析和测试数据也很方便，
这样就会有更多的交易机会来产生利润。 
(2) 选择连续的期货品种。据统计，实际交易中期货可维持的活跃期一般为3-6 个月。
所谓的连续数据是指通过根据某些规则将期货进行连接，以确保数据的连续性，以便更好
地验证数据的有效性并选择配对的品种。如果数据不连续，那么分析数据的方法可能无法
发挥更好的作用。 
4.2.2 数据的处理 
获取数据后，首先应对获得的数据进行质量分析，主要是为了保证数据的正确性和有
效性。通常需要检查数据是否丢失，是否存在数据错误，检查指标是否统一，尤其是非主
数据，需要初步计算。再对收集的数据预处理，处理完这些步骤后，可以获得项目分析所
需的程序输入数据。 
数据清理主要是删除缺失值和噪声值。缺失值的处理主要通过插补，噪声过滤的常用
方法包括回归法，异常值分析法和均值平滑法。在处理商品期货数据，尤其是价格数据时，
噪声数据的处理应该更加谨慎。本文主要采用均值平滑法，用几个相邻数据的均值代替原
始数据。清理样本数据，并且根据逻辑或物理关系将满足要求的多个分布式数据源中的样
本数据集成到统一数据集中，进行数据集成处理。 
经过上述的步骤的处理，数据基本可以达到使用的要求，对数据可以进一步分析和探
索。 
4.3 期货价格波动网络的构建 
为了对期货品种间的关系作初步的分析，需要构建期货价格波动关联网络，这需要计
算品种间的波动相关性，由于期货市场是由不同品种之间的相互关系而形成的一个关系网，
所以本文所研究的是由黑色金属相关的期货品种形成的网络。以期货品种作为节点，以波
动相关性强弱作为两节点间的连线粗细程度，线的数量与粗细程度将反映出品种间的关系
及关系强弱，进而可以通过构建出的期货价格波动复杂网络对期货品种进行分析。 
4.3.1 波动相关性计算 
为了更好的观察期货品种的波动及相关关系，需要建立相关系数矩阵，波动率反映了
期货价格在某一时间周期里的活动状态，通过期货市场中挑选出的期货品种波动数据，计
算各个品种在一定时间周期内的波动率，再计算品种之间的相关系数。 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-36- 
本文构建无向加权网络。
it
p 表示期货市场中第i 个品种在第t 个交易日的收盘价，波动
率
it
V 的计算见式(4.1)。 
            
2{max[
]
min[
]}
{max[
]
min[
]}



it
it
it
it
it
p
p
V
p
p
                         (4.1) 
得到波动率后便可以计算各个品种的相关系数，见式(4.2)和(4.3)。 


2
2
2
2
cov ,
(
)
(
) (
)
(
)
( )
(
)
(
)
i
j
ij
i
j
i
i
j
j
i j
E VV
E Vi E Vj
ρ
σ σ
E V
E V
E V
E V





              (4.2) 
1
1
( )


T
i
it
i
E V
V
T
                             (4.3) 
根据式(4.1)至(4.3)可计算得出各个品种在不同时间周期内的波动率，从而可计算两两
品种间的波动相关性。Chen[46]提出以0，0.5，1 作为相关性的界限更能体现关联强弱。有
以下三种可能性，见表4.1。 
表4.1 波动相关情况 
Table 4.1 Volatility related conditions 
波动相关系数 
波动情况 
品种

,i j 波动相关性
0

ijρ
 
完全不相关 
品种

,i j 波动相关性0
0.5


ijρ
 
弱相关性 
品种

,i j 波动相关性0.5
1


ijρ
 
强相关性 
即当两个品种的波动相关性为0 时，说明它们之间呈完全不相关性。这意味着当其中
一种产品价格波动幅度很大时，另一种产品的价格有很大可能性是非常稳定的。当两个品
种的波动相关性
时，说明他们之间呈现出弱相关性。也就是说一种产品价格的
波动对另一种价格造成的影响微弱。而如果两个品种的波动相关性0.5
1


ijρ
，说明这两
种产品呈强相关性。当其中一种价格波动幅度较大时，另一种产品的价格也有可能产生相
类似的波动幅度，数值越接近1，相关性越强。 
不同周期计算出的波动率是不同的，日度波动率是基于一个工作日计算出的波动率，
周度波动率是基于五个工作日计算出的波动率，月度波动率是基于22个工作日计算出的波
动率。根据龙奥明[15]的研究表明，以日为单位计算波动率进而计算两两品种间的波动相关
系数是相对最明显有效的。 
4.3.2 网络模型及拓扑性 
期货市场是由不同品种之间的相互关系而形成的一个关系网，本文所研究的是由黑色
金属相关的期货品种形成的网络。最终的数据形式如：JM-I-0.681，其中JM 与I 分别为两种
期货产品代码，0.681 代表两品种的相关关系。即构成的网络中黑色金属品种作为节点，品种
0
0.5


ijρ
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-37- 
与品种间的波动相关性作为边，若两个品种间存在相关关系，则两者间存在无向边。网络
可视化所利用的工具是Pajek 软件，该软件可以对网络进行分析，并且能够进行可视化绘
图。将期货波动网络进行可视化可以直观的看到节点间的相关强弱关系，由于不同时间周
期下指标之间的趋势变化不同，因此所建立的网络模型也不同。另外期货市场品种繁多，
关系复杂，直接进行网络的构建很难得出有价值的信息，所以通过品种的筛选再进行网络的
构建更为合适。 
由于数据繁多的情况下格式问题可能会造成Pajek 无法识别文件，所以使用Matlab
编写程序，制作网络文件，省去手写数据的麻烦。构建Pajek 网络图后，可以较为清晰的
看到品种之间的相关关系，但仅凭相关性决定期货品种的配对组合，容易造成相关性导
致无法产生价差偏离均衡的情况，所以通过复杂网络的拓扑性结构，可以进一步缩小可
配对的期货品种组合。 
节点代表期货品种，节点间的无向边代表两两期货间有相关关系，边的权重为相关性
大小，一个节点的度则代表与此产品价格日度波动高度相关的产品数量，节点的度越大，
说明该节点在网络中具有越重要的地位。而加权度则代表与该节点高度正相关的期货品种，
加权度值越高，表示这种期货品种与相关期货的关系越密切。根据经验表明，日波动网络
更能体现出期货品种间的相关关系及变化趋势，通过对期货价格波动网络进行拓扑性分析，
可以对网络的整体性质如节点的度与加权度，网络直径及平均集聚系数进行分析。 
4.4 期货品种配对关系检验 
通过复杂网络的拓扑性结构分析，可以进一步缩小可配对的期货品种，再对选择的品
种进行配对检验，实际上作为验证，对已经挑选出的期货品种利用协整的方法检验他们是
否具有长期稳定的均衡关系。如果具有协整关系，就可以对他们继续进行交易，如果不满
足则放弃，重新挑选配对品种。 
4.4.1 平稳性检验 
(1) 单位根检验 
考虑自回归过程{ }
tY ，见式(4.4)。 
    
1
t
t
t
Y
+ Y
μ




                             (4.4) 
其中
tμ 满足经典假设，等式两边同时减去
1
tY ，见式(4.5)。 
   
1
1
1
-
+
t
t
t
t
t
Y -Y
y
Y








                         (4.5) 
当
1
ρ 时，
ty 满足平稳性条件，式(4.5)可变为差分形式，见式(4.6)。 
      
1
Δ
t
t
t
Y
σY
μ



                             (4.6) 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-38- 
根据上述推论，可以分别建立原假设和备择假设：
0
1
:
0;
:
0
H
σ
H
σ


。该检验可以通
过t 检验进行检验，检验统计量为：
ˆ
ˆ
/ ( )
δt
σ s σ

。其中ˆδ 表示参数估计值，
ˆ
( )
s σ 表示参数估
计值的标准差，在
0
H 成立时，
1

ty
是一阶单整。是否平稳根据prob 值判断，一般都以5%
为临界值水平，prob 小于0.05 就是平稳的，否则为非平稳的。 
(2) ADF 检验 
ADF 检验很好的解决了时间序列在不符合假设时如何检验平稳性的问题。检验含有序
列相关性的模型、含有截距项和序列相关性的模型以及含有趋势项、截距项和序列相关性
的模型，一般从最复杂的模型开始检验。见式(4.7)至(4.9)。 
1
1
Δ
Δ
m
t
t
i
t i
t
i
Y
σY
r Y
ε







模型一：
                    (4.7) 
1
1
Δ
Δ
m
t
t
i
t i
t
i
Y
α+σY
r Y
ε







模型二：
                  (4.8) 
  
1
1
Δ
Δ
m
t
t
t
i
t i
t
i
Y
α
β
σY
r Y
ε









模型三：
              (4.9) 
其中
i
t
α
r
β
ρ
、、、均为参数，tε 为随机误差项。若三个模型得到的结果均为非平稳，则
根据信息准则AIC，SC，HQ 选择数据最小对应的模型在进行一阶检验。 
4.4.2 协整关系检验 
(1) 协整检验 
在验证配对品种具有平稳性关系后，继续进行协整检验。一般用EG 两步法，分为以
下两步： 
第一步对变量
t
X 和
tY ，两者都为I(1)，用OLS 估计两者之间的线性关系，见式(4.10)
至(4.12)。 
   
0
1



t
t
t
Y
a
a X
μ                              (4.10) 
       
0
1
ˆ
ˆ
ˆ


t
t
Y
a
a X                                (4.11) 
   
ˆ


t
t
t
e
Y
Y                                 (4.12) 
第二步为检验回归残差项
te 的平稳性。使用的检验方法同样为ADF 检验法。若残差平
稳则可以认为
t
X 和
tY 之间存在协整关系。 
(2) 误差修正模型 
考虑到滞后分布的影响，将存在协整关系的变量用误差修正模型来刻画它们之间短期
非均衡关系[40]，设有如下(1,1)阶滞后形势，见式(4.13)。 
   
0
1
2
1
1
  







t
t
t
t
t
Y
a
a X
a X
γY
μ                     (4.13) 
对式(4.13)进行变形得到式(4.14)。 
     
0
1
1
2
1
1
Δ
(
(1
  Δ
)
)
t
t
t
t
t
Y
a
a
X
a
a
X
+
γ Y
μ








             (4.14) 
再次变形得到式(4.15)。 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-39- 
     
1
1
0
1
1
(
)
Δ
Δ
  







t
t
t
t
t
Y
a
X
λ Y
b
b X
μ                  (4.15) 
其中：
0
1
2
0
1
1
,
,
a
a
a
λ
γ b
b
λ
λ




 
1
0
1
1




t
t
Y
b
b X
代表
1

t
期的非均衡误差项。式(4.15)反映了短期非均衡状态的修正。
令
1
1
0
1
1






t
t
t
emc
Y
b
b X
，则式(4.15)可以再次变形为式(4.16)。 
    
1
1
Δ
  Δ




t
t
t
t
Y
a
X
λemc
μ                        (4.16) 
其中
1

t
emc
为误差修正项，λ 为误差修正系数。式(4.16)表明Δ
tY 不但会受到自变量波动
项Δ
t
X 的影响，还会受到误差修正项
1

t
emc
的影响。若
1
tY 大于长期平均水平
0
1
1


t
b
b X
，则
1

t
emc
项为正。那么负的
1


t
λemc
将会使Δ
tY 减少，即误差修正项会把
tY 往回拉，使其回到
均衡水平。反之，若
1
tY 小于长期平均水平
0
1
1


t
b
b X
，则
1

t
emc
项为负。那么正的
1


t
λemc
将会使Δ
tY 增加，即误差修正项同样会把
tY 往回拉，使其回到均衡水平。
1

t
emc
反映了长期
趋势对短期波动的影响，它能清楚地显示关于这种偏离的调整信息。 
4.5 数值与算例分析 
讨论了期货市场中与黑色金属相关的期货品种组合选择的配对结果，探讨考虑复杂网
络拓扑性对品种选择的影响。具体而言，分析期货品种间的波动相关性对构建网络的影响
以及拓扑性分析缩小选择范围。 
考虑某物产集团旗下的大型钢铁贸易公司，主要经营的产品品种为：钢材、有色金属、
铁矿石、焦煤、等生产、生活资料产品，业务重点主要为钢材的出口业务。基于目前钢材
现货市场发展缓慢的现状，考虑结合期货市场的期货交易进行收益。本节考虑在期货市场
繁多品种的情况下，分析品种间相关关系，并结合公司主营产品选择出期货市场中合适的
品种进行配对，并对选择的期货品种进行检验。 
4.5.1 数据的处理 
数据源文件来自万德金融数据库，由于数据库中搜集到的数据指标较为繁多，且有些
数据缺失，因此需要对原始数据进行预处理。过程如下： 
(1) 通过万德数据库查询黑色金属相关期货品种与期货结构数据，收集数据的时间区
间为2017-2018 年。将得到的数据进行整理，删除无用的列，保留的数据有交易所代码、
交易所名称、商品代码、中文名称，命名为大宗期货交易市场期货品种与期货结构数据表，
见表4.2。 
 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-40- 
表4.2 大宗期货交易市场期货品种与期货结构数据表 
Table 4.2 Futures Variety and Structure Data Sheet 
交易所代码 
交易所名称 
商品代码 
中文名称 
DCE 
大连 
J 
冶金焦炭 
DCE 
大连 
I 
铁矿石 
DCE 
大连 
JM 
焦煤 
DCE 
大连 
V 
PVC 
DCE 
大连 
BB 
胶合板 
DCE 
大连 
PP 
聚丙烯 
DCE 
大连 
FB 
纤维板 
SHFE 
上海 
ZN 
锌 
SHFE 
上海 
PB 
铅 
SHFE 
上海 
RB 
螺纹 
SHFE 
上海 
RU 
天然橡胶 
SHFE 
上海 
AL 
铝 
SHFE 
上海 
FU 
燃料油 
SHFE 
上海 
AU 
黄金 
SHFE 
上海 
WR 
线材 
SHFE 
上海 
CU 
铜 
SHFE 
上海 
HC 
热轧卷板 
SHFE 
上海 
NI 
镍 
SHFE 
上海 
SFFI 
硅铁 
CZCE 
郑州 
ME 
甲醇 
CZCE 
郑州 
TC 
动力煤 
NYMEX 
纽约 
PA 
钯 
NYMEX 
纽约 
HO 
燃料油 
LME 
伦敦 
CO 
钴 
LME 
伦敦 
AH 
铝 
LME 
伦敦 
CA  
铜 
LME 
伦敦 
FM 
钢坯 
LME 
伦敦 
PB 
铅 
LME 
伦敦 
ZS 
锌 
COMEX 
纽约 
SI 
白银 
COMEX 
纽约 
GC 
黄金 
COMEX 
纽约 
HG 
铜 
(2) 不同的产品生命周期及跨度不同，导致起止日期差别很大。故从大宗期货交易市
场期货品种与期货结构数据表中筛选出商品代码，在万德数据库中选取1994-2018 年产品
品种多且交投活跃的时间段，保证研究数据数目。统计原始可用数据，并存放到新表格中，
命名为原始可用数据表，见表4.3。 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-41- 
表4.3 原始可用数据表 
Table 4.3 Raw data sheet 
品种 
起始日期 
截止日期 
数据个数 
J 
2011 年4 月15 日 
2017 年8 月20 日 
1337 
I 
2013 年10 月18 日 
2017 年8 月22 日 
1131 
JM 
2013 年3 月22 日 
2017 年8 月22 日 
966 
JD 
2013 年12 月8 日 
2017 年8 月20 日 
715 
BB 
2013 年12 月6 日 
2017 年8 月20 日 
798 
PP 
2014 年2 月28 日 
2017 年8 月20 日 
641 
FB 
2013 年12 月6 日 
2017 年8 月20 日 
798 
ZN 
2007 年3 月26 日 
2017 年8 月22 日 
2517 
PB 
2011 年3 月24 日 
2017 年8 月22 日 
1551 
RB 
2009 年3 月27 日 
2017 年8 月22 日 
2087 
RU 
1995 年11 月2 日 
2017 年8 月20 日 
4223 
AL 
1995 年5 月17 日 
2017 年8 月28 日 
4204 
FU 
2004 年8 月25 日 
2017 年8 月28 日 
1962 
AU 
2018 年1 月9 日 
2017 年8 月22 日 
2407 
WR 
2009 年3 月27 日 
2017 年8 月22 日 
2112 
HC 
2014 年3 月21 日 
2017 年8 月20 日 
648 
NI 
2015 年3 月27 日 
2017 年8 月20 日 
234 
SFFI 
2011 年9 月24 日 
2017 年7 月28 日 
1205 
CU 
2012 年8 月27 日 
2017 年8 月20 日 
742 
PAZ15E 
2014 年9 月26 日 
2017 年8 月28 日 
309 
HOV15E 
2010 年12 月6 日 
2017 年8 月28 日 
1347 
CO03ME 
2010 年2 月22 日 
2017 年8 月28 日 
1510 
AH03ME 
2003 年12 月7 日 
2017 年8 月20 日 
3124 
CA 
2014 年12 月19 日 
2017 年8 月20 日 
3124 
FM 
2013 年9 月25 日 
2017 年8 月20 日 
918 
PB03ME 
2011 年3 月15 日 
2017 年8 月22 日 
3124 
ZS03ME 
2003 年12 月17 日 
2017 年8 月22 日 
3124 
SIZ15E 
2010 年1 月22 日 
2017 年8 月22 日 
1599 
GCZ15E 
2008 年10 月31 日 
2017 年8 月22 日 
1753 
HGZ15E 
2010 年1 月22 日 
2017 年8 月22 日 
1532 
(3) 从原始可用数据表中筛选出能完整反应价格及波动的数据，统一数据长度，将原
始可用数据表中筛选后的“商品代码”列复制至最新“筛选数据表”中，查找到所对应的
中文名称，将结果复制回筛选数据表中，见表4.4。 
 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-42- 
表4.4 筛选数据表 
Table 4.4 Filter data sheet  
合约代码 
中文名称 
J 
冶金焦炭 
I 
铁矿石 
JM 
焦煤 
ZN 
锌 
RB 
螺纹 
RU 
天然橡胶 
AL 
铝 
WR 
线材 
HC 
热轧卷板 
CU 
铜 
SFFI 
硅铁 
FM 
钢坯 
PB 
铅 
(4) 查找万德数据库中黑色金属期货的“最高价、最低价”复制到筛选数据表，并根据
不同的时间周期，分别算出黑色金属期货之间的波动相关性。以螺纹钢与铁矿石为例，说
明期货品种波动相关性的计算方法。铁矿石的日价格波动图如图4.1所示。 
 
图4.1 螺纹钢日度价格波动图 
Fig. 4.1 RB daily price fluctuation chart 
图4.1显示了螺纹钢日价格走势，绿色代表当日的收盘价低于开盘价，红色代表当日收
盘价高于开盘价，竖线的长短代表当日出现的最高价和最低价。表4.5是通过螺纹的最高价
与最低价计算出的波动率，可以更加简明的看到螺纹价格的变动趋势。从表4.5可以看出，
3000
3200
3400
3600
3800
4000
4200
20 21 24 25 26 27 28 31 1 2 3 4 7 8 9 10 11 14 15 16 17 18 21
螺纹钢日度价格波动图
开盘
最高价（元）
最低价（元)
收盘
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-43- 
螺纹钢的价格波动相对稳定，基本保持在0.02-0.06之间，稳定的原因分析是由于上半年我
国宏观经济好于预期，整体运行状况良好，延续了稳中向好的发展态势，稳的基础更加巩
固，好的态势更加明显。 
表4.5 螺纹钢日度价格波动率计算表 
Table 4.5 RB daily price volatility calculation 
合约名称 
日期 
最高价（元） 
最低价（元） 
螺纹钢每天波动 
螺纹钢 
2017 年7 月20 日 
3675 
3488 
0.05 
螺纹钢 
2017 年7 月21 日 
3547 
3467 
0.02 
螺纹钢 
2017 年7 月24 日 
3566 
3455 
0.03 
螺纹钢 
2017 年7 月25 日 
3590 
3485 
0.03 
螺纹钢 
2017 年7 月26 日 
3639 
3547 
0.03 
螺纹钢 
2017 年7 月27 日 
3599 
3504 
0.03 
螺纹钢 
2017 年7 月28 日 
3595 
3530 
0.02 
螺纹钢 
2017 年7 月31 日 
3740 
3559 
0.05 
螺纹钢 
2017 年8 月1 日 
3759 
3693 
0.02 
螺纹钢 
2017 年8 月2 日 
3748 
3662 
0.02 
螺纹钢 
2017 年8 月3 日 
3791 
3702 
0.02 
螺纹钢 
2017 年8 月4 日 
3899 
3765 
0.03 
螺纹钢 
2017 年8 月7 日 
4101 
3866 
0.06 
螺纹钢 
2017 年8 月8 日 
3996 
3841 
0.04 
螺纹钢 
2017 年8 月9 日 
4008 
3867 
0.04 
螺纹钢 
2017 年8 月10 日 
4016 
3915 
0.03 
螺纹钢 
2017 年8 月11 日 
3993 
3812 
0.05 
螺纹钢 
2017 年8 月14 日 
3894 
3786 
0.03 
螺纹钢 
2017 年8 月15 日 
3809 
3695 
0.03 
螺纹钢 
2017 年8 月16 日 
3769 
3696 
0.02 
螺纹钢 
2017 年8 月17 日 
3860 
3721 
0.04 
螺纹钢 
2017 年8 月18 日 
3900 
3776 
0.03 
螺纹钢 
2017 年8 月21 日 
3994 
3803 
0.05 
同样地，铁矿石的日价格波动图可以更好地显示价格波动趋势及状态，如图4.2所示。
图4.2显示了铁矿石日价格走势，绿色代表当日的收盘价低于开盘价，红色代表当日收盘价
高于开盘价，竖线的长短代表当日出现的最高价和最低价。从图中可以较明显的看出铁矿
石7-8月份的价格波动也相对稳定，基本价格稳定在510元-600元之间。 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-44- 
 
图4.2 铁矿石日度价格波动图 
Fig. 4.2 I daily price fluctuation chart 
从表4.6可以看出，铁矿石的价格波动相对稳定，基本保持在0.03-0.07之间。  
表4.6 铁矿石日度价格波动率计算表 
Table 4.6 I daily price volatility calculation  
合约名称 
日期 
最高价（元） 
最低价（元） 
铁矿石每天波动 
铁矿石 
2017 年7 月20 日 
536.5 
515.5 
0.04 
铁矿石 
2017 年7 月21 日 
523 
508.5 
0.03 
铁矿石 
2017 年7 月24 日 
524.5 
502 
0.04 
铁矿石 
2017 年7 月25 日 
531 
512.5 
0.04 
铁矿石 
2017 年7 月26 日 
534.5 
514 
0.04 
铁矿石 
2017 年7 月27 日 
527.5 
515 
0.02 
铁矿石 
2017 年7 月28 日 
534 
523 
0.02 
铁矿石 
2017 年7 月31 日 
570.5 
529.5 
0.07 
铁矿石 
2017 年8 月1 日 
555 
543 
0.02 
铁矿石 
2017 年8 月2 日 
547.5 
532 
0.03 
铁矿石 
2017 年8 月3 日 
557 
535.5 
0.04 
铁矿石 
2017 年8 月4 日 
556 
539 
0.03 
铁矿石 
2017 年8 月7 日 
587.5 
542 
0.08 
铁矿石 
2017 年8 月8 日 
574 
543 
0.06 
铁矿石 
2017 年8 月9 日 
568.5 
547 
0.04 
铁矿石 
2017 年8 月10 日 
572 
552.5 
0.03 
铁矿石 
2017 年8 月11 日 
571 
527.5 
0.08 
铁矿石 
2017 年8 月14 日 
543 
524.5 
0.03 
铁矿石 
2017 年8 月15 日 
533.5 
518.5 
0.03 
铁矿石 
2017 年8 月16 日 
533 
518 
0.03 
铁矿石 
2017 年8 月17 日 
561.5 
524 
0.07 
铁矿石 
2017 年8 月18 日 
584 
546.5 
0.07 
铁矿石 
2017 年8 月21 日 
601 
561 
0.07 
450
500
550
600
650
20 21 24 25 26 27 28 31 1
2
3
4
7
8
9 10 11 14 15 16 17 18 21
铁矿石日度价格波动图
开盘
最高价（元）
最低价（元）
收盘
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-45- 
通过螺纹钢和铁矿石的日波动率，运用式(4.1)-(4.3)可得出螺纹钢与铁矿石的相关系数
为
ijρ =0.789，同理可得到13种期货品种间的价格波动相关性见表4.7。 
表4.7 期货品种价格波动相关性计算表 
Table 4.7 Futures price volatility correlation calculation  
 
冶金
焦炭 
铁矿石 
焦煤 
锌 
螺纹钢 
天然
橡胶 
铝 
线材 
热轧
卷板 
铜 
钢坯 
硅铁 
铅 
冶金焦炭 
1 
 
 
 
 
 
 
 
 
 
 
 
 
铁矿石 
0.756 
1 
 
 
 
 
 
 
 
 
 
 
 
焦煤 
0.901 
0.681 
1 
 
 
 
 
 
 
 
 
 
 
锌 
0.898 
0.654 
0.898 
1 
 
 
 
 
 
 
 
 
 
螺纹钢 
0.736 
0.789 
0.436 
0.584 
1 
 
 
 
 
 
 
 
 
天然橡胶 
0.743 
0.457 
0.498 
0.719 
0.767 
1 
 
 
 
 
 
 
 
铝 
0.905 
0.536 
0.752 
0.897 
0.697 
0.864 
1 
 
 
 
 
 
 
线材 
0.368 
0.344 
0.298 
0.318 
0.446 
0.209 
0.314 
1 
 
 
 
 
 
热轧卷板 
0.839 
0.808 
0.574 
0.711 
0.971 
0.817 
0.809 
0.407 
1 
 
 
 
 
铜 
0.797 
0.778 
0.695 
0.697 
0.759 
0.52 
0.634 
0.634 
0.802 
1 
 
 
 
钢坯 
0.469 
0.271 
0.583 
0.42 
0.112 
0.264 
0.119 
0.119 
0.116 
0.24 
1 
 
 
硅铁 
0.897 
0.526 
0.86 
0.946 
0.539 
0.729 
0.911 
0.334 
0.657 
0.65 
0.287 
1 
 
铅 
0.916 
0.66 
0.771 
0.877 
0.789 
0.817 
0.924 
0.486 
0.875 
0.85 
0.19 
0.617 
1 
4.5.2 期货价格波动网络的构建 
以2017 年8 月的数据为例，以日（1 交易日）为单位构建了期货波动网络图。本文首
先采取手动编辑数据的方法绘制网络图，编写方式如下： 
*Vertices 13 （表示以下共有13 个顶点） 
1 "J" 
2 "I" 
3 "JM" 
4 "ZN" 
5 "RB" 
6 "RU" 
7 "AL" 
8 "WR" 
9 "HC" 
10 "CU" 
11 "FU" 
12 "FM" 
13 "PB" 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-46- 
*Edges 
2 1 0.756 (从点2 指向点1 的无方向的连线，线值为0.756) 
3 1 0.901 
3 2 0.681 
4 1 0.898 
4 2 0.654 
4 3 0.898 
5 1 0.736 
6 3 0.498 
再按照4.3 节提到的代码进行处理，通过Pajek 软件生成期货价格波动网络图，如图
4.3 所示。 
 
图4.3 期货价格波动网络图 
Fig. 4.3 Futures price volatility network  
图4.3 是利用Pajek 软件建立的黑色金属品种间的期货波动网络图，由Pajek 画出的期
货波动网络可视图呈现一个圆形，在圆的周边黑色点代表不同的期货品种，由于相关系数
是双向的，内部的黑色线条则代表两者有相关性的无向边。本文构建的网络属于无向加权
网络。本文在构建网络时，需要对边设置阈值来显示网络中各节点之间的关系。根据Chen[46]
提出以0.5 作为恒定相关系数的强弱标准较为合理，所以本文将阈值设置为0.5。如果两期
货品种波动相关性低于0.5，波动网络图形中这两个节点间不会存在边，认为他们的相关关
系较弱；如果两期货品种波动相关性大于等于0.5，波动网络图形中这两个节点间存在一条
边，边的权重即为两期货品种的相关系数，粗细代表其关系的强弱。 
从图4.3中可以直观的看出节点所代表的品种间相关关系，边权重代表两品种波动相关
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-47- 
性大小。而且从图4.3可以看出，有的节点拥有的连边较少，而有的节点拥有的连边非常多，
关于这个现象在后文中将进行讨论。 
通过直接的观察，可以发现黑色金属品种间的期货波动网络具有很明显的复杂性，但
无法观察出其具有何种特征，因此需要对其拓扑结构特征进行分析研究，通过进一步研究
网络的结构才能明确这个网络所具有的性质。 
4.5.3 期货品种网络拓扑性评价 
期货品种之间的关联强弱可以通过网络节点的连接反映出来，为了进一步研究黑色金
属期货市场的结构和发展，需要通过实际网络的结构特性进行分析。网络的基本结构性质
主要包括网络的度与度分布、平均网络直径、聚类系数等,针对网络的这几种结构性质,文
章将在这一部分对上文中建立的期货品种关联网络进行拓扑结构分析。 
(1) 网络节点度与度分布 
图4.4 和图4.5 为期货品种的度值与加权度值的变化趋势以及日度价格波动累计概率。 
 
图4.4 期货价格波动网络图 
Fig. 4.4 Daily fluctuation network degree and weighted arrangement 
从图4.4 和图4.5 可以看出，度值与加权度之间存在着相似的变化趋势，当一种期货
品种的度值较高时，其加权度值也相对较高。这也就表示，此期货品种较为重要，且与其
他期货品种的相关度较高。以上品种中度值较高的期货品种有铁矿石，焦炭，线材，当这
3 种期货显示出较明显的价格波动时，其余期货品种的价格波动也会发生显著变化。同时
考虑到加权度，从图中可以看出热轧卷板和铁矿石的加权度较高，说明这两个期货品种与
其他期货品种相关性较为紧密。而相对于铅、钢坯、铜这三种期货品种来说，度值和加权
度值最小，说明它们的成交量相对稀疏，交投不活跃，与黑色期货品种的关系相对较弱。
冶金
焦炭
铁矿
石
焦煤
锌
螺纹天然
橡胶
铝
线材热轧
卷板
铜
硅铁钢坯
铅
系列2
9
10
9
5
8
5
5
9
9
4
6
4
4
系列1 5.56
5.96
5.41
3.23
4.97
2.78
3.04
5.38
5.66
2.67
4.21
2.57
2.13
0
2
4
6
8
10
12
值
日波动网络度与加权度排列情况
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-48- 
总的来说，日价格波动网络中，螺纹的度值为8，加权度为4.97，说明有8 种产品（焦炭、
铁矿石、焦煤、锌、线材、钢坯等）的日度波动价格与螺纹的日度波动价格高度相关且平
均相关系数的估算值为0.62。图4.4 也同时证明了网络中不是所有的节点都具有相同的度，
大多数节点的度值分布在区间[1,10]内。 
 
图4.5 日度价格波动网络度累计概率分布情况 
Fig. 4.5 Daily price volatility network degree cumulative probability distribution 
 (2) 网络节点平均路径长度与聚类系数 
本节针对选出的13种期货品种，通过平均路径长度与聚类系数对期货波动价格关联网
络进行分析，探讨网络的小世界特性。本节应用pajek软件进行计算，以2017年8月份的数据
为例，通过pajek计算后的网络平均路径长度如图4.6所示。 
 
图4.6 8 月份期货价格平均路径长度 
Fig. 4.6 Average path length for August 
由计算结果可知，2017年8月份期货价格波动关联网络的路径长度约为1.51，也就是说，
网络中任意两个节点间距离的平均值为1.5。利用此方法将2017年6月-2018年5月每个月的
期货价格波动关联网络的平均路径长度进行计算，统计数据见表4.8。 
 
0.00%
20.00%
40.00%
60.00%
80.00%
100.00%
120.00%
0
1
2
3
4
5
6
7
累
积
概
率
度
日度价格波动网络度累计概率分布情况
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-49- 
表4.8 各月份平均路径长度 
Table 4.8 Average length of each month 
年份 
2017/6 
   2017/7 
  2017/8 
2017/9 
 2017/10 
 2017/11 
平均路径长度 
1.55 
1.47 
1.51 
1.86 
1.24 
1.33 
年份 
   2017/12 
   2018/1 
  2018/2 
2018/3 
2018/4 
2018/5 
平均路径长度 
1.17 
2.13 
2.04 
1.94 
2.38 
1.92 
表4.8可以看出2017-2018年的期货价格波动关联网络的平均路径长度均较小，距离大
多在1到2之间，说明构建的日价格波动期货网络中两节点间可以通过较少的边进行连接，
这也表示挑选出的13种期货品种，它们之间平均只需要通过1到2个期货品种就能彼此之间
构成联系。这种关系有利于更好地观察期货品种价格的日波动性，减少观察次数。同时，
由于两种期货价格之间存在相关性，许多不确定的供需关系只会导致两种期货价格的上涨
和下跌，但对价差的影响不大。可以通过一种期货品种对另一种期货品种的价差进行预测，
同时降低了投资风险。但是较小的平均路径也存在劣势条件，网络中的某一节点变化可能
会影响到其他节点的变化，从而对整体网络造成较大影响。对于期货市场来说，单个期货
品种价格的波动可以轻易影响到其他品种价格波动。也就是说，由于季节变化、库存影响
等客观因素造成的某一期货品种价格波动可能会造成多个期货品种价格波动幅度增大。 
同样的，通过pajek计算后的网络平均聚类系数如图4.7所示。 
 
图4.7 8 月份期货价格平均聚类系数 
Fig. 4.7 August futures price average clustering coefficient  
聚类系数反应的是网络中节点的聚集程度，与网络中边的方向没有关系[48]。本文所要
计算的是Watts-Strogatz聚类系数，即先计算每个节点的聚类系数，然后进行非加权平均，
所得结果即为所求。运用Pajek软件可直接计算出所需结果。计算各月的网络平均聚类系数，
结果见表4.9。 
 
 
 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-50- 
表4.9 各月份平均聚类系数 
Table 4.9 Average clustering coefficient for each month 
年份 
2017/6 
2017/7 
2017/8 
2017/9 
2017/10 
2017/11 
平均聚类系数 
0.62 
0.47 
0.67 
0.74 
0.35 
0.41 
年份 
2017/12 
2018/1 
2018/2 
2018/3 
2018/4 
2018/5 
平均聚类系数 
0.28 
0.87 
0.83 
0.78 
0.91 
0.76 
图4.8 给出了平均路径长度与聚类系数的变化趋势。从图4.8 中可以看出，平均路径
长度与聚类系数的变化趋势大致上具有一致性，在2017-2018 年间这两个指标明显呈现出
上升-下降-上升的趋势，平均路径描述的是网络中点的分离程度，由此可以看出这一年期
货品种的网络规模是在不断发生变化的，这也导致网络的平均路径发生相应变化，网络内
部节点间的聚集程度也会受到网络规模的影响。 
 
图4.8 平均路径长度与聚类系数变化趋势 
Fig. 4.8 Average path length and clustering coefficient change trend 
从以上结果可以看出，各月网络的平均聚类系数比较大，而平均路径又比较小，对应
于小世界网络。网络的宏观结构将显着影响系统中的波动性转换，这对了解期货产品价格
波动网络的性质很重要。  
(3) 波动网络重要产品及指标比较 
通过前两节的分析，期货价格波动的总体情况见表4.10。 
 
2018/5, 1.92
2018/5, 0.76
0
0.5
1
1.5
2
2.5
2017/4 2017/6 2017/8 2017/9 2017/112017/12 2018/2 2018/4 2018/5
长
度
时间
平均路径长度与聚类系数变化趋势
平均路径长度
平均聚类系数
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-51- 
表4.10 黑色金属相关期货价格波动总体情况 
Table 4.10 General situation of price fluctuations of ferrous metal-related futures 
合约代码 
交易所 
产品名称 
度 
加权度 
集聚系数 
J 
大连 
冶金焦炭 
9 
5.56 
0.89 
I 
大连 
铁矿石 
10 
5.96 
0.94 
JM 
大连 
焦煤 
9 
5.41 
0.75 
ZN 
上海 
锌 
5 
3.23 
0.61 
RB 
上海 
螺纹 
8 
5.38 
0.81 
RU 
上海 
天然橡胶 
5 
2.78 
0.53 
AL 
上海 
铝 
5 
3.04 
0.59 
WR 
上海 
线材 
9 
4.97 
0.85 
HC 
上海 
热轧卷板 
9 
5.66 
0.87 
CU 
上海 
铜 
4 
2.67 
0.45 
SFFI 
上海 
硅铁 
6 
4.21 
0.66 
FM 
大连 
钢坯 
4 
2.57 
0.47 
PB 
上海 
铅 
4 
2.13 
0.41 
通过网络拓扑性的综合分析可以帮助本文找到几种关系与黑色金属期货最为紧密的
期货品种，通过度值的大小首先选出焦炭、铁矿石、焦煤、螺纹、线材、热轧卷板，又因
为加权度值越高的期货品种与相关期货的关系越紧密，所以在上述品种之中去掉线材，而
集聚系数的大小又代表了品种间的集聚效应，由于焦煤的集聚系数在剩下的5 类品种之中
最小，所以最后筛选出焦炭、铁矿石、螺纹、热轧卷板四种期货品种。从万德数据库中找
到这四种期货品种并两两配对，选取样本数量225，总体回归函数与样本回归方程见式(4.17)
至(4.19)，进行回归性分析。  
0
1
i
i
i
y
x






                         (4.17) 
0
1
i
iy
x







                           (4.18) 
2
2
0
1
1
1
(y
) =
(y
x )













n
n
i
i
i
i
i
i
Q
y
                    (4.19) 
令
0
ˆ



0
Q
，
1
0
ˆ
Q




进行求解可得 
ˆ
ˆ
2(
) (
)
ˆ
ˆ
ˆ
(
)
ˆ
ˆ
























0
1
0
0
1
0
1
1
0
i
i
i
i
i
i
Q
y
x
y
x
y
n
x
 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-52- 
2
2
ˆ
ˆ
2(
) (
)
ˆ
ˆ
ˆ
-2(
)
ˆ
ˆ
i
i
i
i
i
i
i
i
i
i
i
Q
y
x
x
y x
x
x
x y
x
x

























0
1
0
0
1
0
1
0
 
将
0
ˆ



0
Q
，
1
0
ˆ
Q




建立方程组可得 
2
0
2
2
(
)
i
i
i
i
i
i
i
x
y
x
x y
n
x
x









 
1
2
2
(
)
i
i
i
i
i
i
n
y x
y
x
n
x
x









 
得到四种品种的初步回归曲线，如图4.9-4.11 所示。 
 
图4.9 螺纹钢与铁矿石回归线 
Fig. 4.9 Rebar and Iron Ore Tropic 
其中X 轴代表焦炭，Y 轴代表螺纹钢，
0
0.549
0 52
ˆ
ˆ
.
β
β


1
，
4
，回归方程为
0.549
0. 52
Y
X


4
。 
 
图4.10 螺纹钢与焦炭回归线 
Fig. 4.10 Rebar and coke Tropic 
其中X 轴代表焦炭，Y 轴代表螺纹钢，
1
0
ˆ
ˆ
0.481
= 0.225
β
β

，
，回归方程为
0.481
0.225


Y
X
。 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-53- 
 
图4.11 螺纹钢与热轧卷板回归线 
Fig. 4.11 Rebar and hot rolled coil Tropic 
其中X 轴代表卷板，
轴代表螺纹钢，
0
ˆ
ˆ
0.900
0.005
β
β


1
，
，回归方程为
0.9
0.005


Y
X
。 
从表4.10 可以看出，四种黑色期货产品两两间确实存在着较为紧密的关系，但对两个
配对交易的期货产品来说，相关性固然重要，但若两种期货的价差序列在一定时间周期内
无明显的波动，就无法构建投资组合进行配对交易，更不能满足开仓止损的条件，那么建
立配对交易模型就失去了意义。所以若想从中进一步选出最合适的品种进行配对，还需要
引入“可配对系数”，以2017 年6 月-2018 年5 月的期货价格为研究对象，如图4.12 所示。 
 
图4.12 黑色金属相关期货品种间可配对系数情况 
Fig. 4.12 Permissible pairing coefficient between ferrous metals related futures  
可配对系数即为两期货品种标准差的均值，通过选取可配对系数大于1 的两个期货品
种作为配对交易的研究对象，从图4.12 可以看出，铁矿石与螺纹期货，螺纹与热轧卷板期
货的可配对系数均大于1，可以考虑作为配对品种组合。  
4.5.4 期货品种配对平稳性检验 
根据上节得到的数据可以看出，螺纹钢和铁矿石，螺纹钢和热轧卷板均可以作为较好
的配对品种进行交易。对于钢铁贸易公司来说，需兼顾上游和下游的买卖交易，既要担心
Y
产品名称
相关系数
可配对系数
焦炭&铁矿石
0.756
0.445
焦炭&螺纹
0.736
0.947
焦炭&热轧卷板
0.839
0.356
铁矿石&螺纹
0.789
1.216
铁矿石&热轧卷板
0.808
0.381
螺纹&热轧卷板
0.971
1.432
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-54- 
原材料的价格上涨，又要担心成品价格的下跌，所以选择铁矿石和螺纹钢作为配对交易品
种可以更好的解决风险问题。 
(1) 验证配对期货品种的平稳性 
通过网络拓扑性分析，得出螺纹钢和铁矿石的收盘价格呈现高度相关性，但是若想对
两品种进行配对交易，需要它们保持长久的平衡状态，就要对两品种进行平稳性检验。 
首先通过螺纹钢和铁矿石的价格取自然对数分别进行单位根检验，若非平稳，再对二
者进行ADF检验，观察检验结果进行总结。建立一个序列，quick-series statistics-unit root test，
并运行，如图4.13所示。 
 
图4.13 Eviews平稳性检验操作图 
Fig 4.13 Eviews Stationarity Test Operation Diagram 
运行结果见表4.11。 
表4.11 ADF 检验结果 
Table 4.11 ADF test results 
变量 
Test critical value 
T-statistic 
Prob 
平稳性 
1% level 
5% level 
10% level 
RB 
-3.996431 
-3.428503 
-3.137665 
-2.090167 
0.5482 
非平稳 
I 
-3.996431 
-3.428503 
-3.137665 
-2.263359 
0.4519 
非平稳 
∆RB 
-3.996592 
-3.428581 
-3.137711 
-15.72838 
0.0000 
平稳 
∆I 
-3.996592 
-3.428581 
-3.137711 
-16.41847 
0.0000 
平稳 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-55- 
从表4.11中可以看出RB与I的P值为0.5482和0.4519，大于0.05，不拒绝原假设，是非平
稳的。再做一阶差分检验，Prob的值均为0小于0.05，拒绝原假设，说明一阶差分后的RB和
I具有平稳性，可以在1%的显著性水平下通过检验，认为螺纹钢和铁矿石的价格对数为一
阶单整序列，可以继续用EG两步法进行协整检验。 
4.5.5 期货品种配对协整关系检验 
在螺纹钢和铁矿石确认两者具有平稳性之后，通过EG检验法继续对两者进行协整关系
检验。利用eviews工具对两期货品种的收盘价格做回归分析，验证初步回归分析结果，见
式(4.20)。 
  



t
t
t
RB
C
βI
μ                            (4.20) 
对螺纹钢和铁矿石价格的自然对数进行OLS回归，见表4.12。 
表4.12 螺纹钢和铁矿石价格自然对数OLS 估计结果 
Table 4.12 Natural logarithm OLS estimation results for rebar and iron ore prices 
Variable 
Coefficient 
Std. Error 
t statistic 
Pr(>|t|） 
Intercept 
0.50287 
0.420739 
54.46 
0.000137*** 
I 
0.42304 
0.005561 
31.95597 
0.000275*** 
Signif.  Codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1  
Residual standard error: 0.09251 on 1210 degrees of freedom 
Multiple R-squared:  0.8423,         Adjusted R-squared:  0.8427 
F-statistic:  2.289e+04 on 1 and 1210 D F,  p-value: <2.2e-16 
从表4.13可以看出，估计参数显著，则两期货品种的关系见式(4.21)。 
  
0.50287
0.42304



t
t
t
RB
I
μ                    (4.21) 
对残差序列
0.50287
0.42304



t
t
t
μ
RB
I 再进行平稳性检验，见表4.13，可以得出残差
序列平稳。所以两期货品种存在协整关系，可以进行配对交易。 
接着建立螺纹钢和铁矿石关系的误差修正模型，见式(4.22)。 
    
0
1
2
1
3
1







t
t
t
t
t
RB
C
C I
C I
C RB
μ                  (4.22) 
将式(4.21)改为差分形式变形见式(4.23)。 
0
1
1
2
1
3
1
Δ
Δ
(
)
(1
)









t
t
t
t
t
RB
C
C
I
C
C I
C RB
μ             (4.23) 
对式(4.22)进行最小二乘回归，见表4.14。 
 
 
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-56- 
表4.13 残差ADF 检验结果 
Table 4.13 Residual ADF test results 
Augmented 
Dickey-Fuller 
test statistic 
T-statistic 
Prob 
平稳性 
-2.692161 
0.0000 
平稳 
Test critical values: 
1% level 
-2.574593 
5% level 
-1.942147 
10% level 
-1.615821 
表4.14 残差OLS 估计结果 
Table 4.14 Residual OLS estimation result 
Variable 
Coefficient 
Std. Error 
t statistic 
Pr(>|t|） 
Intercept 
∆𝐼𝑡 
0.018624 
0.562154 
0.005349 
0.027335 
  4.9764 
25.122 
0.000146*** 
0.001475*** 
RB𝑡−1 
𝐼𝑡−1 
-0.029041 
0.012748 
0.005361 
0.005847 
-4.126 
4.528 
0.000312*** 
0.002347*** 
Signif.  Codes:  0 ‘***’ 0.001 ‘**’ 0.01 ‘*’ 0.05 ‘.’ 0.1 ‘ ’ 1 
Residual standard error: 0.02136 on 1207 degrees of freedom 
Multiple R-squared:  0.5981        Adjusted R-squared:  0.6848 
F-statistic:  170.3 on 1 and 1207 D F,  p-value: <2.2e-16 
对得到的结果均保留三位小数，可得回归方程见式(4.24)。 
1
1
Δ
0.019
0.562Δ
0.013
0.029







t
t
t
t
t
RB
I
I
RB
μ          (4.24) 
令误差修正项
1
1
1
1
0.448
0.029








t
t
t
t
t
emc
RB
I
RB
μ 。对式(4.24)进行变换见式(4.25)。 
    
1
Δ
0.562Δ
0.029




t
t
t
t
RB
I
emc
μ                   (4.25) 
该式表明，当实际收盘价差与均衡状态发生偏离时，
1

t
emc
会按照0.029 的幅度对偏差
进行修正，并得到协整向量为(1，-0.448)，相当于买入1 手螺纹钢时，要与0.448 手铁矿石
进行对冲，即卖出0.448 手铁矿石进行交易。 
4.6 管理启示 
通过考虑复杂网络的量化投资品种配对组合，在期货市场品种繁杂的情况下对品种进
行配对组合的选择。期货品种的选择作为量化投资过程中的首要环节，品种选择的合适与
否对于后面的配对交易策略十分重要，同时选择合适的配对品种也为企业提供以下管理启
示： 
(1) 以往进行交易时，通常会根据以往的相关经验或当前期货品种价格水平进行交易，
含有很强的主观意愿。但考虑复杂网络拓扑性进行品种的组合选择，可以利用期货品种的
东北大学硕士学位论文 
 第4 章  考虑复杂网络的多品种量化投资组合研究 
-57- 
网络特征进行筛选，再通过配对关系检验确定筛选的配对品种，通过协整的方法来检验他
们是否确实具有长期稳定的均衡关系。 
(2) 提高了期货市场品种配对的合理性。仅通过品种间相关系数确定配对品种存在着
一定的问题，不能保证价差的稳定性，动态效果比较差。期货市场是一个复杂网络，用黑
色金属期货构建的关联性网络呈现出期货品种间的关系，通过复杂网络的拓扑性缩小配对
品种选择范围，根据公司的不同主营业务可以进一步确定配对品种，通过对数据的采集和
处理并进行协整性检验，证明配对的平稳性和有效性，可以为后续量化交易策略提供有力
的前提保障。 
4.7 本章小结 
本章以量化配对交易中的品种配对选择为前提，针对品种间相关性及复杂网络拓扑性
分析，给出了考虑复杂网络的量化投资品种组合选择结果，本章主要工作如下：  
(1) 构建期货价格波动关联网络。在对期货市场目前形势分析以及国内外各大交易所
品种价格数据预处理的前提下，计算期货品种间的价格波动相关性，对期货市场中与黑色
金属相关的多个品种进行初步选择，再通过pajek 软件对筛选出的品种进行网络构建，直
观地看出品种间的相关强弱关系，为后文进一步分析期货市场品种间关系从而确定配对品
种奠定基础。 
(2) 期货品种复杂网络拓扑性分析及检验。由于只考虑品种波动相关性过于片面，未
考虑市场形势等方面，因此本文使用复杂网络的拓扑性对期货品种间的关系进行进一步分
析，通过度与度的分布、平均路径长度及聚类系数等，对期货品种进一步筛选。再对品种
进行配对检验，包括平稳性检验与协整关系检验，验证配对的有效性。 
(3) 结合算例背景对期货品种进行最终筛选。通过数据的处理以及对网络的拓扑性分
析，再对选择的品种进行配对检验，强调配对品种组合选择的重要性及管理启示，为企业
进行量化投资决策提供了品种配对选择的保障。 
本节提出的考虑复杂网络价格波动对期货品种进行配对组合的选择，为后文基于强化
学习算法的配对交易策略提供了前提。
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-58- 
第5 章 基于强化学习的量化投资品种交易策略 
本章主要在已选出的配对期货品种基础上进一步的分析，继续进行配对交易，探讨
交易过程中交易信号的确定，并基于强化学习算法对交易信号进行自适应优化，制定基
于强化学习模式下的量化投资品种交易策略。 
5.1 问题描述与符号说明 
5.1.1 问题描述 
非理性预期将导致价格上下波动，而配对交易能够减小波动的幅度。若投资者倾向
于低买高卖,价格将会产生一个没有实际期货支撑的上涨的惯性，所以价格短暂上涨后
会迅速下跌。同理,对于价格下降的商品，非理性因素也会导致该类商品产生下跌的惯
性，而当理性因素处于市场的主导地位时，由于非理性因素导致的价格波动将会回到有
实际期货支撑的正常水平。基于这种情况，投资者可以通过对这两种期货进行配对交易
来得到这两种期货高估和低估的价格差收益。配对交易策略为在选择恰当的配对期货品
种后，当配对期货价格差异达到入场点，买入价格低的期货，同时卖出价格高的期货，
当价格差异回到长期均衡的时候，便终止头寸结束该交易。所以考虑在选定品种的基础
上进行交易信号的设置很重要。 
配对交易策略即是在选择恰当的配对期货品种后，设置进出场时机，同时，为了控
制风险，以免价差进一步扩大，需要确定适当的止损点。大多学者通过研究股票市场来
确定传统交易信号，而配对交易的开平仓阈值和止损阈值的基础则为市场的常数标准差，
对于价格相对低估的品种进行买进，而将价格相对高估的品种售出，通过短时间内出现
的预期价差的偏差，在均值回复的作用下一定会恢复到正常水平，但是往往达不到最大
的收益，故建仓平仓的信号确定显得尤为重要。 
对于黑色金属商品期货交易而言，根据固定的交易信号已经不能满足投资者的需求，
随着人工智能的发展，强化学习应用于越来越多的领域，它拥有自学习自适应的能力，
可以通过计算机进行训练和测试数据，以达到实时调整交易信号从而获取更大收益的目
的。 
 
 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-59- 
5.1.2 符号说明 
为了便于分析，对本章使用的符号说明如下： 
：时期
品种的价格
t
M
t
M
 
0
0
：时期
品种的价格
t
M
t
M
 
：时期品种的价格
t
N
t
N
 
0
0
：时期品种的价格
t
N
t
N
 
OU
：是一个
过程
t
X
 
：均值回归速率
θ
 
：
的均值
t
μ
X
 
：标准维纳过程
t
W
 
：开仓价格
a
 
：开仓价格
b
 
：收益率
r
 
1：交易期
τ
 
2：等待期
τ
 
：净收益
τ
NP
 
1：第层隐藏层和输出层的输入值
z
l
 
1：第层隐藏层和输出层的输出值
a
l
 
：第天期货的品种净值
i
D
i
 
(
)：投资组合的报酬率
p
E R
 
：无风险利率
f
R
 
：投资组合的标准差
p
σ
 
Γ：奖励递减速率 
5.2 交易信号规则 
量化配对交易的过程一般分为两个环节，一是配对的选择，二是交易信号的设置。
通过期货价格波动关联网络的分析得到配对期货品种之后，分析配对期货品种之间的价
格差距判断是否存在交易机会。一旦出现交易机会，就会按照设定的开平仓规则进行买
卖。本节首先会介绍传统的交易信号确定方法，接着提出强化学习下自适应交易信号的
训练。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-60- 
5.2.1 传统交易信号的确定 
大多学者通过研究股票市场来确定传统交易信号。而配对交易的开平仓阈值和止损
阈值的基础则为市场的常数标准差。对于价格相对低估的股票进行买进，而将价格相对
高估的股票售出，而短时间内出现的预期价差的偏差，在均值回复的作用下一定会恢复
到正常水平[90]。当价格差恢复到均衡水平时，投资者在这时要将之前买入的相对低价的
股票售出，同时买入价格相对较高的股票，并对两边平仓，此时两只股票的价格差减小，
投资者从中获得相应利润。但由于存在异方差，利用常数作为阈值往往获取不到最大的
利润,丧失很多套利机会,甚至造成严重的亏损。在设置开仓阈值时，如果阈值过小，则
会导致开仓次数频繁，交易费用增加，使得单次交易的利润减小。而如果开仓阈值设置
的过大,又会使交易“门槛”变高造成交易机会减少，而使亏损次数增加。此外，当止损
阈值过高时，在单次交易中可能会出现较大的回撤，还可能导致配对股票出现不协调，
价差序列发散的情况，在这种情况下投资者将产生巨额亏损，因此，从交易期长度的角
度出发、改变固定常数作为阈值条件等方面对阈值的确定做出改进可以增加收益的可能
性。 
5.2.2 基于OU 过程的交易信号确定 
基于OU 随机过程对配对交易的阈值进行寻优，通过最大化时间收益，利用计算出
的价差对阈值进行设置，从而制定交易期进出场规则，这种方法较好地避免了异方差产
生的影响。 
假设两种期货分别为M 和N，将它们的价格取对数进行配对，见式(5.1)至(5.3)。 
  
0
0
0
ln(
)
ln(
)
(
)
[ln(
)
ln(
)]
,
0







t
t
t
t
t
M
M
α t
t
β
N
N
ε t
            (5.1) 
   
ln(
)
ln(
)


t
t
t
X
M
β
N
                         (5.2) 
      d
(
)



t
t
X
θ μ
X dt
σdW                        (5.3) 
令
2
,
(
)



τ
t
θ
τ
θt Y
X
μ
σ
转换式(5.3)得到式(5.4)。 
      dY
Y
2


τ
τ
t
dt
dW                          (5.4) 
交易周期可分为交易期和等待期两部分：
1
0
inf{ ;
|
}



t
τ
t Y
c Y
a
和
2
0
inf{ ;
|
}



t
τ
t Y
a Y
c ，完整的交易周期为：
1
2


T
τ
τ 。假设[0, ]τ 内有
τ
N 次交易，则净
收益
τ
N 为
(
)



τ
τ
NP
c
a
p N 。单位时间的期望收益见式(5.5)。 
   
[
]
[
]
lim
(
)lim
[ ]
τ
τ
τ
τ
E NP
E N
c
a
p
μ
c
a
p
τ
τ
E T









           (5.5) 
另外对, ,
θ σ μ 的估计采用极大似然估计法中的对数似然函数，见式(5.6)。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-61- 
1
1
1
1
(
)
2 (
)
2 (
)
2
1
1
(
)
1
(
)
ln( (
))
ln(1
)
2
2
1























i
i
i
i
i
i
i
i
θ t
t
n
n
t
t
θ t
t
θ t
t
i
i
x
μ
x
μ e
n
θ
L X
P X
e
σ
e
 (5.6) 
在配对交易中，使用参数估计值计算出的阈值是非常有效的，但是对于大量的数据
计算来说，运用极大似然估计法显然速度慢且复杂，所以将基于OU 过程得到的阈值对
于期货市场繁杂的品种以及数据来说，还不能作为最合适的阈值确定方法，但可以将取
品种的价格对数来消除异方差性作为交易信号自适应训练数据的前提。 
5.2.3 自适应交易信号训练 
配对交易的重要环节之一就是要确定合适的交易阈值。以往的研究通常仅采用静态
常数作为策略的固定阈值，此方法具有一定的局限性，通过实时动态变化的阈值参数可
以更好地进行交易决策，而强化学习算法恰好可以实现自学习、与环境自适应的特点。
故对交易信号进行具体的描述，即为两个阈值参数：开仓阈值和止损阈值。开仓阈值则
相当于一个提醒器，它是动态变化的，当配对品种之间的价格差距超过了开仓阈值，说
明此时可以进行交易，建立头寸；而止损阈值则相当于报警器，当止损阈值大于开仓阈
值且配对品种间的价格差距与均值越来越大，并且没有回复趋势的时候，止损阈值这一
报警器启动，进行强制平仓止损。具体的交易信号事例如图5.1 所示。 
 
图5.1 交易信号示例 
Fig. 5.1 Trading signal example 
从图5.1 可以看出，开仓信号区间为[-a,a]，平仓止损区间为[-c,c]，举例说明建仓平
仓的规则：当价差在第5 个点超过上边界a 时，进行建仓，即做多一种期货品种的同时
做空另一种品种，并且预计价差会在某一时期收敛回水平线，即在第12 个点进行平仓，
完成一个交易回合。如果价差长时间没有呈现回复趋势并仍在扩大价差，那么当价差超
过止损边界线即第14 个点时，强制止损。对于价差抵达下边界则进行反向头寸，原理
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-62- 
相同。 
强化学习算法需要通过Q 表或者Sarsa 表对关于每个状态下的不同动作的分值或奖
励得分进行存储，以便测试时可以更快的运用到训练的数据，但考虑数据的繁多性以及
复杂性，考虑通过搭建神经网络对数据进行存储。 
5.3 强化神经网络模型的构建 
5.3.1 BP 神经网络实现过程 
神经网络模型主要通过数据的不断输入更新迭代，经过不断的学习训练得到最合适
的w 参数，w 代表权重，最开始随机给定初始值，如图5.2 所示。 
 
图5.2 简单神经元模型 
Fig. 5.2 Simple neuron model  
.
( .
_
([2,3]),
2,
0,
1))




w
tf Variable tf truncated
normal
stddev
mean
seed
表示去掉过
大偏离点的正态分布，也就是如果随机出来的数据偏离平均值超过两个标准差，数据
将重新生成。 
在优化参数的过程中，需要通过对神经网络进行前向和反向传播实现参数的固
定，学习神经网络实现过程分为以下四个步骤： 
(1) 准备数据集，即提取特征，作为输入数据作为神经网络的X1、X2… 
(2) 构造前向传播结构，从输入到输出先搭建计算图，再通过会话执行，可以得到
较好的神经网络。 
(3) 构造反向传播结构，将大量特征数据回传给神经网络系统，得到大量计算输出，
将计算得到的输出与实际的差反向传回神经网络，调整神经网络参数，直到模型达到要
求。 
(4) 对训练好的模型进行测试，将新的数据送入神经网络。 
以上主要分为训练和使用过程。前三步为训练数据不断迭代，第四步为使用过程，
一旦参数固定优化完毕，即可使用这些参数进行测试。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-63- 
5.3.2 BP 神经网络模型的搭建 
简单的神经网络模型难以满足优化需求，在此基础上引入偏置项bias 和激活函数f，
可以有效避免仅使用, ,
ε x w的纯线性组合。 
本文采取的激活函数sigmoid 为
1
( )
1


x
f x
e
，在tensorflow 中表现为tf.nn.sigmoid()。
通过引入激活函数和偏置项，神经元模型变化为如图5.3 所示。 
 
图5.3 非线性神经元模型 
Fig. 5.3 Non-linear neuron model 
前向传播即搭建模型的过程，输入值可以通过参数得到相应的输出值，例如将螺纹
钢与铁矿石的期货价差作为特征值输入神经网络，通过权重的加权计算，神经网络输出
一个数值Y，前向传播模型构建如图5.4 所示。 
 
图5.4 前向传播网络模型 
Fig. 5.4 Forward propagation network model 
用tensorflow 描述见式(5.7)至式(5.12)。 
   
(1)
(1)
(1)
(1)
1.1
1.2
1.3
(1)
(1)
(1)
2.1
2.2
2.3






W
W
W
W
W
W
W
                       (5.7) 
      
(1)
11
12
13
[
,
,
]

a
a
a
a
                            (5.8) 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-64- 
    
(2)
1.1
(2)
(2)
2.1
(2)
3.1










W
W
W
W
                             (5.9) 
     
(1)
(2)

y
a W
                             (5.10) 
    
1
.
(
,
)

a
tf matmul X W                          (5.11) 
      
2
.
( ,
)

y
tf matmul a W
                        (5.12) 
其中，X 是1*2 矩阵，表示一次输入一组特征，用
层数
前,后
W
表示待优化的参数。对于
第一层的W，表示为一个2*3 列的矩阵，a 作为第一个计算层，它是一个1*3 列的矩阵，
第二层的W 要满足3*1 的矩阵，得到相应地矩阵后，可以计算Y 的值。 
对于反向传播的训练方法，以减小loss值为优化目标，可以使用梯度下降法，
Momentum 优化器，Adam 优化器等优化方法。神经网络反向传播模型参数更新推导见
式(5.13)至(5.25)。 
   
1
1
( )
(
)




l
l
l
l
a
f z
f w a
b
                     (5.13) 
   
2
2
1
( , , , )
2


l
J w b x y
a
y
                      (5.14) 
1
1
( , , , )
( , , , )
( (
)
) (
)
(
)(
)
(
)
l
l
l
l
l
l
T
l
l
l
l
J w b x y
J w b x y
z
f z
y f' z a
a
y a
f' z
w
z
w














  (5.15) 
   
( , , , )
( , , , )
( (
)
) (
)
(
)
(
)
l
l
l
l
l
l
l
l
J w b x y
J w b x y
z
f z
y f' z
a
y
f' z
b
z
b












    (5.16) 
更新l 层的参数
l
l
w
b
和
 
1
( , , , )
l
l
l
J w b x y
w
w
w





                       (5.17) 
      
1
( , , , )
l
l
l
J w b x y
b
b
b





                       (5.18) 
令 
  
( , , , )
( , , , )
l
l
l
l
l
l
l
l
l
J w b x y
J w b x y
z
z
z
δ
z
z
z
z
z
















-1
+1
-1
-2
            (5.19) 
则有 
    
1
( , , , )
( , , , )
(
)
l
l
l
T
l
l
l
J w b x y
J w b x y
z
δ a
w
z
w










              (5.20) 
        
( , , , )
( , , , )
l
l
l
l
l
J w b x y
J w b x y
z
δ
b
z
b









                 (5.21) 
可用数学归纳法得到
lδ ，见式(5.22)。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-65- 
   
1
1
1
1
( , , , )
( , , , )
l
l
l
l
l
l
l
l
J w b x y
J w b x y
z
z
δ
δ
z
z
z
z
















=
             (5.22) 
由于 
   
1
1
1
l
l
l
l
z
w a
b





                          (5.23) 
所以 
    
1
1
1
'
(
)
(
)











l
l
l
l
T
l
l
l
l
z
z
f
w
f
z
z
f
z
                   (5.24) 
  
1
1
1
1
1
'
1
( , , , )
( , , , )
(
)
(
)
l
l
l
l
l
l
T
l
l
l
l
l
J w b x y
J w b x y
z
z
δ
δ
δ
w
f
z
z
z
z
z



















=
   (5.25) 
因此第l 层参数
l
l
w
b
和
的更新见式(5.26)和(5.27)。 
  
1
1
1
1
( , , , )
(
)
(
)
(
)(
)
l
l
l
l
l
T
l
l
l
T
l
l
T
l
J w b x y
w
w
w
δ a
w
δ
w
f' z
a
w












     (5.26) 
    
1
1
( , , , )
(
)
(
)
l
l
l
l
l
l
l
T
l
l
J w b x y
b
b
b
δ
b
δ
w
f' z
b










         (5.27) 
通过正反向反馈机制便可以搭建神经网络模型，可以方便地进行数据地训练及存储。 
5.4 强化学习配对交易策略 
将强化学习的人工智能思想引入传统的配对交易策略中，通过实时优化动态阈值参
数，设计计算机系统的决策过程，利用Q-learning 算法结合神经网络对数据的存储和变
化进行实时更新，不断调整动态阈值参数以增强训练的即时性以及实现投资组合收益最
大化。 
5.4.1 评定绩效和风险指标的选定 
对量化交易模型进行评价通常从两个方面考虑其绩效表现，一方面是盈利能力，一
方面是抗风险水平，盈利能力指标通常从策略的收益角度对策略进行描述；抗风险水平
一般是从策略的波动性角度对策略进行描述。 
针对盈利能力，可以通过总收益率、收益波动率、资产回撤、收益率的偏度和峰度
来分析收益的变化。本文主要通过总收益率、日收益率以及回撤率进行考虑。其中，总
收益率是反映量化交易模型是否能有效带来收益的最直观指标，总收益率=（期末资产-
起初资产）/期初资产；日收益率可由（当日收盘价格-当日带盘价格）/开盘价格获得；
回测率作为衡量风险的重要指标，它表示在某一段时期内产品净值从最高点开始回落到
最低点的幅度，而最大回撤率表示在特定的周期内某个历史时点向后推，产品净值走到
最低点时的收益率回撤的最大值幅度，描述的是投资策略中买入后最大有可能的亏损资
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-66- 
金的比例。最大回撤率计算见式(5.28)。 
     
max(
)


i
j
i
D
D
drawdown
D
                       (5.28) 
其中D 并表示日净值，i 表示某一日，j 为i 后的某一日，
i
D 为第i 天对的期货品种
净值，
j
D 为第j 天的期货品种净值。 
针对抗风险能力，可以通过夏普比率、信息比率、Beta系数、风险价值对风险进行
评估，本文主要通过夏普比率进行考虑。 
夏普比率的计算见式(5.29)。 
     
(
) 

p
f
p
E R
R
SharpeRatio
σ
                        (5.29) 
其中，(
)
p
E R
为投资组合的报酬率；
f
R 为无风险利率；
p
σ 为投资组合的标准差。如
果夏普比率为正，则表明期货品种的平均净增长率超过了计量期间的无风险利率且单位
风险获得的风险收益较高。 
VaR 即风险价值(Value at Risk)，在市场正常波动下，某一期货产品或配对组合的最
大可能损失。举个例子来解释VaR：两种配对交易产品在未来1 天内的置信度为98%，
并在期货市场正常波动状态下，VaR 值为5 万。这表明在1 天内，有98%的把握判断由
于期货市场中出现的价格波动导致的配对交易产生的最大损失为5 万，而超过5 万的可
能性为2%。2%的可能性代表投资人对这两个品种进行投资组合的风险厌恶程度。 
5.4.2 强化学习算法的选择 
考虑使用Q 学习算法，所有与环境交互的经验将被连续存储在模型的Q 表中，避
免后续训练的相似性，防止陷入局部最优。回放模式的使用更类似于普通的监督学习模
式。 该过程还简化了调整和测试算法的过程。 
学习算法试图解决的问题之一是获取最大奖励。它可以及时传播奖励，直到它到达
关键决策点。Q 表作为经验存储开始是比较随机的，也可以说是不正确的，即算法要不
断进行训练以选择具有最高值的动作，如果函数收敛，模型趋向于返回更稳定的值。  
由于Q-learning 算法在状态空间上对数量和离散性的要求，在本研究的阈值参数中，
开仓阀值和平仓阀值又都是连续型参数，所以需要进行处理，本研究使用单位0.1 抽取
数值的均分方式做离散化处理，达到Q-learning 算法离散化处理要求。Q-learning 算法
的迭代见式(5.30)。 
  
1
1
1
1
1
( ,
)
( ,
)
[
((
,
)
(( ,
)]









k
t
t
k
t
t
t
k
t
t
k
t
t
Q s a
Q
s a
α r
γQ
s
a
Q
s a
       (5.30) 
 对于以上问题有着不仅简单而且比较有效的处理方法，就是在网络模型中采用ε -
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-67- 
贪心探索策略（
ε
greedy exploration ）。ε 选择一个随机动作，不然模型就将采用具备
最高Q 值的“贪心”动作。本文构建的模型中实际上采用的是随着时间推进不断地将ε
从1 降至0.1——最初的时候网络模型采取随机的行动操作以达到最大化地探索状态空
间的目的，随后模型的探索率再稳定在一个固定数值上。 
5.4.3 强化学习动态交易信号的配对交易决策 
基于第三章对强化学习原理的描述，将强化学习的思想引入到配对交易系统中，首
先要明确配对交易系统的核心。配对交易的目标是实现最大化收益，实现目标的关键在
于决策，所以配对交易系统的核心是交易决策系统，那么交易决策系统相当于强化学习
中的智能体；黑色金属期货市场相当于配对交易系统中的环境，黑色金属期货价格相当
于配对交易系统中的环境状态；评定绩效指标作为奖赏值；开仓阈值和平仓阈值作为构
成智能体的行为，并且它们是随着数据的不断调整发生动态变化的。基于强化学习下的
配对交易决策过程如图5.5 所示。 
 
图5.5 强化学习模式下配对交易决策过程 
Fig. 5.5 The process of paired trading decision-making in the reinforcement learning mode 
在整个强化学习的过程中，智能体会通过每次投资组合得到的开平仓结果以及经验
对环境状态做出相应的动态调整，即对交易信号做出实时性的动态变化，来寻找最大奖
励。学习一段时间后选择一定数量的价格数据来测试智能体进行策略的选择，通过协整
检验验证交易的有效性，通过价差确定交易信号并自适应更新阈值参数，然后夏普比率
作为评价绩效指标，智能体不断接收其反馈的奖励值，最后完成所有任务后输出夏普比
率，得到最终的绩效。配对交易流程图如图5.6所示。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-68- 
 
图5.6 强化学习模式下配对交易模型计算机流程图 
Fig. 5.6 Computer flow chart of paired transaction model in reinforcement learning mode 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-69- 
如图5.6所示，整个系统的工作流程从阈值参数的初始值开始，智能体也就是交易决
策系统实时观察环境状态，当配对品种之间的价格差距超过了开仓阈值，说明此时可以
进行交易，建立头寸，此时交易决策系统会对配对期货品种进行指示，令其进行开仓操
作；持仓建立过程中，智能化调整动态阈值。当价差超过止损阈值但配对品种间的价格
差距无法回归均值水平或价格差距继续扩大且无回归趋势时，都会出发平仓止损的报警
信号，决策系统在进行平仓操作的同时会输出奖赏值作为奖励；之后系统的决策信息和
值函数进行更新，通过算法的迭代，决策系统持续监控开仓阈值和平仓阈值两个动态阈
值，等待下一次投资组合时开仓，如此形成一个循环，直到交易期终止。 
5.5 数值与算例分析 
针对期货市场中黑色系金属期货品种的配对交易策略，探讨基于强化学习算法对配
对交易策略的优化。具体而言，在确定配对期货品种的基础上，分析强化学习模式下交
易信号的自适应变化、绩效结果及风险分析。 
本节算例仍考虑4.5 节中某物产集团旗下的大型钢铁贸易公司为研究背景，业务重
点主要为钢材的出口业务。基于目前钢材现货市场发展缓慢的现状，考虑结合期货市场
的期货交易进行收益。本节考虑在确定要进行配对期货品种的基础上，要求对选择的配
对品种价格趋势进行分析，并通过强化学习算法进行仿真训练与测试，以便可以在不同
的时期获得动态的交易信号及时进行开平仓的处理，以达到收益最大化的目的。 
5.5.1 配对品种数据需求 
在选择了期货品种进行配对的基础上，需要对选择的品种进一步分析和测试。图5.7
表明了铁矿石和螺纹钢在选定研究时间内的历史收盘价格对比趋势。 
 
图5.7 配对交易品种历史收益价格对比走势图 
Fig.5.7 Paired trading varieties historical income price chart 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-70- 
从万德数据库中可以找到关于黑色系金属期货的相关价格数据。考虑到铁矿石和螺
纹钢在期货市场的开始日期不同，需要对交易数据的时间做出统一，另外考虑到配对交
易所需产品的流动性，数据采用2015年5月31日-2018年5月31日的全部数据，配对组合为
铁矿石和螺纹钢。绿色线代表螺纹钢的收盘价格曲线，白色线代表铁矿石的收盘价格曲
线，由图5.8可以看出，铁矿石和螺纹钢之间的收盘价格走势十分相似。选择期货品种的
历史价格数据作为具体研究数据，配对交易关注的主要是收益的增长，数据的统计信息
见表5.1。 
表5.1 样本数据统计信息 
Table 5.1 Sample data statistics 
产品 
数据数量 
数据时间跨度 
铁矿石 
936 
2015.5.31-2018.5.31 
螺纹钢 
1121 
2015.5.31-2018.5.31 
对于仿真测试训练集和测试集的选择，因为强化学习需要提供有效数据进行测试和
训练，而2015年5月31日-2018年5月31日的所有交易日为样本区间，要尽可能的保证足够
的训练集数据进行迭代训练及数据存储，故训练集选取2015年5月31日-2017年5月31日
的数据，测试集选取2017年6月1日-2018年5月31日的数据，对协整配对交易进行进一步
分析。 
5.5.2 强化学习神经网络的正向更新 
Q-learning 的Q 值放在Q 表中存储搜索耗时且弹性较差，考虑将神经网络结合到强
化学习中，作为存储的功能，不再依赖Q 表决定行动。可以有两种形式，第一种就是将
状态和动作都作为输入值，通过神经网络分析得到动作的Q 值；还有一种形式只输入状
态值，输出所有的动作值，然后按照Q-learning 值的动作当做下一步要做的动作。本节
考虑使用第二种形式，将状态值作为输入值进行网络模型的构建，这样就没有必要在表
格记录Q 值，而是直接使用神经网络作为Q 值的存储形式。 
1
2
,
a a 的Q 值用Q 现实代替，Q 估计为通过NN 的
2
1
2
2
(
,
),
(
,
)
Q s a
Q s a
值。再通过神经
网络正向得到的Q 估计中最大值的动作来换取环境中的奖励reward，再进行神经网络
的更新。神经网络的更新如图5.8 所示。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-71- 
 
图5.8 强化学习神经网络的正向更新 
Fig. 5.8 Enhanced learning neural network forward update 
通过数据集的准备，前向传播中定义的输入、参数和输出，可以构建强化学习神经
网络模型，搭建网络结构。本文结合实现强化学习需要考虑的状态、动作以及奖励，将
铁矿石和螺纹钢的价差作为状态集输入神经网络，考虑输入层X 只有一个，第一层考虑
设置10 个节点，输出包括开仓阈值与止损阈值，分别有正向和反向之分，所以输出层
设置为4 个节点，构建二层神经网络，总参数应为总参数和总偏置项的和，故第一层应
为1*10+10=20 个参数，第二层应为10*4+4=44 个参数，共64 个参数。 
5.5.3 强化学习神经网络的反向存储 
通过反向传播实现新NN 的训练。以品种期货价差作为状态S，开平仓阈值作为动
作a，所获得的结果作为已学习到的Q 值，而Qtarget 则是根据在环境中不断计算得到
的目标值。见式(5.31)。 
( , )
(max( ( ', '))


Q s a
r
γ
Q s a
                     (5.31) 
正向传播后得到动作对应的Q值，再进行反向训练，用Qtarget 值不断更新旧的Q
值，实现神经网络反向训练，用函数表示为
.
_
( .
(
)


loss
tf reduce
mean tf square Qtarget
Q 。
引入损失函数，使模型在训练数据上的损失函数最小，见式(5.32)。 
     
2
(
)



Loss
Qtarget
Q
                      (5.32) 
前向网络搭建网络结构后，利用反向传播中定义损失函数、确定强化学习算法，训
练网络参数等方法，通过反向网络的训练实现Q 值的存储。其中，在对神经网络参数模
型进行优化时，需要考虑学习率，用它来决定参数每次更新的幅度。使用时可以选择一
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-72- 
个比较小的值，如0.01、0.001，本文学习率α设置为0.01，奖励递减速率γ设置为0.9，
ε 初步设置为0.9，代表90%的可能性选择Q 值最大的行为。 
5.5.4 强化学习配对交易信号的训练与测试 
(1) 历史数据经验回放训练 
对于训练集，是根据历史数据的波动趋势和历史经验进行学习并存储记忆。数据的
选择是基于2015-2017 年螺纹钢与铁矿石的日收盘价差波动趋势，通过训练集的学习与
数据的更新迭代，将阈值参数的学习表现应用到测试集中，给定测试集阈值参数的范围，
通过测试验证训练学习的有效性，找到不同状态下对应的最优阈值参数，来进行建仓平
仓的交易，从而进一步验证配对品种交易的适用性。 
首先对2015 年5 月31 日-2017 年5 月31 日的数据进行训练，得到训练集的交易信
号图，如图5.9 所示。当价差超过正向开仓阈值时，多头买入，在预测期货价格呈上涨
趋势时购买；当价差超过负向开仓阈值时，表示价格不理想，预计有下跌趋势，选择卖
出。而绿色线代表正向开仓信号，红色线代表正向平仓止损信号，紫色线代表反向开仓
信号，蓝色线代表反向平仓止损信号。 
 
图5.9 历史数据经验回放训练交易信号图 
Fig. 5.9 Historical data experience playback training trading signal table 
开仓线在平仓线上下各有一条，表示触碰到上面的绿色线边界时，正向开仓，建立
头寸，卖出RB 买入I，当触碰到下面的紫色边界时，反向开仓，即买入RB 卖出I。相
应地止损线也是同样的道理，不与赘述。 
(2) 参数模型测试 
将学习训练后的阈值参数模型应用到测试集中观察它们的表现，通过自适应学习根
据价差的变化调整阈值的取值，测试集交易信号如图5.10 所示。 
从图5.10 可以看出，价差的小波动大频率变化对阈值的影响较小，而价差的大波动
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-73- 
会使阈值产生较大的调整以适应配对交易，同时开仓与止损的变化趋势基本相同。当配
对资产价差小范围波动时，交易信号不发生变化或适当调整，而在一定时期内产生较大
的波动时，为了满足收益最大化，阈值也相应发生了调整和改变，通过交易信号图可以
直观地看到阈值随价差波动而变化。价差的波动会影响配对交易次数，从而带动收益的
变化。 
 
图5.10 强化学习模式下配对交易决策过程 
Fig. 5.10 The process of paired trading decision-making in the reinforcement learning mode 
2018 年3 月31 日-2018 年6 月31 日收益波动明显，是因为价差波动的影响，而可
能同时导致配对的次数增加，累计收益率迅速提高。根据历史自然对数价格数据进行训
练得到自学习的阈值，比传统的采用标准差作为常数阈值来说更为准确，考虑到了最大
期望收益，为参数的学习提供了更有效的数据支持。 
5.5.5 强化学习配对交易策略的绩效结果分析 
通过强化学习对铁矿石和螺纹钢进行配对交易，利用强化学习算法得到实时变化的
阈值进行开仓止损，并证明二者可以进行配对，通过强化学习配对交易输入数据的训练
与测试，可输出相关的绩效奖励收益表现，训练绩效表现图和测试绩效表现图分别如图
5.11 和图5.12 所示。 
 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-74- 
图5.11 训练绩效表现图 
Fig. 5.11 Training performance chart 
通过训练与测试的表现结果可以进一步证明配对交易选取品种的适用性以及模型
的有效性，可以直观的看出累计收益呈上升趋势，当迭代到6000 次时，累计收益率收
敛于最大值。另外，从图中可以看出，回测率呈下降趋势，降低投资风险。 
 
图5.12 测试绩效表现图 
Fig. 5.12 Test performance chart 
综合绩效表现结果如表5.2 所示。 
表5.2 配对交易综合绩效表现 
Table 5.2 Paired trading comprehensive performance 
测试结果 
螺纹钢--铁矿石 
训练集 
测试集 
累计收益率% 
37.52 
28.25 
年化收益率% 
7.2 
5.5 
最大回撤% 
4.41 
4.14 
夏普比率 
0.29 
0.33 
交易次数 
15 
10 
从表5.2 可以看到配对交易的统计结果。作为交易策略中最重要的指标之一，年化
收益率通过将当前收益率转换为年回报率来表示，从年化收益率角度可以看出，将螺纹
钢和铁矿石进行配对能实现较理想的收益。累计收益率会通过不断学习，持续上升直到
收敛于最大值。而夏普比率比例越高，说明配对交易越理想，投资组合的表现越突出，
也同样证明了所选取配对交易品种的适用性及合理性。最大回撤作为重要的风险指标，
它的绝对值越小，说明可较为有效的规避市场风险，实现盈利。另外，降低风险的同时，
通过强化学习，减少交易次数，可以降低套利成本，并为配对交易创造机会，实现收益
最大化。 
通过强化学习的自适应训练得到螺纹钢与铁矿石共产生10 次交易，产生的累计收
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-75- 
益率为28.3%，同时，对比传统配对交易，采用固定开平仓阈值作为交易信号进行交易，
共产生6 次配对，累计收益率为19.4%。这说明通过不断学习更新阈值对配对品种进行
交易，既增加了交易机会，又增加了收益，验证了模型的有效性，同时说明螺纹钢和铁
矿石可以进行配对交易并能获得稳定的收益。 
期货市场可以指导企业的生产经营活动，同时也能够为企业提供套期保值、规避风
险的机会。因此，结合期货进行现货分析提供了转移现货市场价格风险的平台，也为企
业提供了规避钢材现货市场价格风险的手段。 
表5.3 表示进行期货配对交易过程中买卖产生的费用，下面以一次交易为例结合现
货情况进行收益的计算。 
表5.3 配对交易过程买卖费用 
Table 5.3 Matching transaction process 
名称 
费用 
备注 
交易手续费 
按交易金额的万分之三计算 
开平仓双向 
资金成本 
期货价*15%*资金利率*N/365 
资金占用按期货价15%计算 
N 为资金占用天数 
期货合约总成本 
交易手续费+资金成本 
 
具体应用举例：2018 年6 月15 日，期货RB1810 的收盘价格为3897 元/吨，期货
I1809 的收盘价格为473.5 元/吨。交割日期为最后交易日后的五个工作日，最后交易日
为合约交割月份的15 日。从期货代码可知，螺纹钢RB1810 的交割日期为2018 年10 月
22 日，距离交割日还有128 天，铁矿石I1809 的交割日期为2018 年9 月19 日，距离交
割日还有95 天。由上一章可知，卖出一手螺纹钢需要对冲0.448 手铁矿石，而在期货市
场中，螺纹钢期货标准合约的交易单位为每手10 吨，铁矿石期货标准合约的交易单位
为每手100 吨，所以均按照买入10 手铁矿石要对冲25 手螺纹钢进行配对交易。 
操作：从6 月15 日开始到交割截至日期结束，完成一次配对交易，卖出RB1810，
25 手，买入I1809，10 手；费用按照表5.3 计算可得： 
(1) 交易手续费： 
买入I1809 合约：473.5*0.00003*2*10*100=28.41 元 
卖出RB1810 合约：3897*0.0003*2*25*10=58.46 元 
合计：86.87 元 
(2) 资金成本: 
交易只收取单边保证金，资金占用按期货价格的15%计算 
I1809 合约资金成本：473.5*10*100*15%*5.5%*95/365=1016.73 元 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-76- 
(3) 期货合约总成本： 
交易手续费+资金成本=86.87+1016.73=1103.60 元 
待期货配对交易完成即螺纹钢与铁矿石的价格回归长期均衡价格时，获取的价差收
益为： 
I1809：(489-473.5)*10*100=19800 元 
 
RB1810：(3897-3796)*25*10=25250 元 
故配对交易完成后的累计收益为39600 元。 
若只进行现货交易，截至到交割期结束，螺纹钢价格上涨131 元，累计收益32750
元，铁矿石价格上涨17 元，累计亏损17000 元。对于现货来说，出售螺纹钢给下游可
以获得较大的利润，然而原材料的上涨导致购买铁矿石现货亏损。所以通过铁矿石与螺
纹钢的期货配对交易，可以避免铁矿石价格波动的风险，反而盈利。 
 (4) 净利润： 
净利润=买入I 期货赚取的收益+卖出RB 现货赚取的收益+卖出RB 期货赚取的收
益-买入I 现货成本-期货合约总成本 
      =39600+32750-17000-1104 
         =59696 元 
5.6 管理启示 
通过基于强化学习的量化投资品种交易策略，可以更好地分析价格走势并获得最大
的利润。同时，研究期货市场的量化投资交易策略对企业也具有重要的管理启示，表现
为以下两点：  
(1) 通过提出的量化投资策略，在期货市场上进行期货品种的配对交易。通过售卖
螺纹期货，防止现货下跌的风险，也起到控制售价的目的；买入铁矿石期货，对冲现货
价格上涨造成的成本损失。通过这种方式，企业可以提前锁定采购成本和销售收入，只
需要遵循既定的业务计划，而不必担心原材料和成品价格的波动。 
(2) 通过提出的量化交易策略，可利用期货交易进行套期保值，即在期货市场上进
行期货品种的配对交易，通过强化学习算法自适应地调整开平仓信号设置，进行配对交
易，通过均衡回报获得的收入可以真实地反映供需趋势和价格变化为现货市场提供重要
的参考数据。 
企业可以通过期货价格，把握市场价格的波动规律，选择合适的时机囤货和出货，
利用期货市场锁定理想的价格，规避市场价格波动带来的风险，实现收益的最大化。 
东北大学硕士学位论文 
第5 章 基于强化学习的量化投资品种交易策略 
-77- 
5.7 本章小结 
本章在选定配对品种的基础上继续进行交易策略研究，主要研究交易信号的设置。
先构建了神经网络模型作为参数的存储盒，又将强化学习算法应用于配对交易模型中，
探讨交易期参数的选择及交易过程中规则的确定，并基于传统的阈值对协整配对交易模
型进行改进，运用强化学习算法对交易信号进行适时的调整。本章节具体的工作总结如
下： 
(1) 阈值的选择对期货品种配对交易的影响。在第四章的基础上对期货品种进行了
筛选，需要进一步对配对品种进行交易决策。传统的开平仓阈值为固定常数，已经无法
满足投资者的利益需求，达不到利益最大化，通过实时变化的调整参数作为交易信号，
完成配对交易，可以提高收益率。 
(2) 基于强化学习对交易信号进行设置。由于强化学习中，Q 表的弹性比较差，考
虑将神经网络结合到强化学习中，通过神经网络的正反网络搭建，不再依赖Q 表决定行
动。通过更新的神经网络取代Q 表中的表格单元，再应用强化学习中的Q-learning算法对
动态参数进行不断优化，最后通过夏普比率等验证模型的有效性也为下文实证研究提供
了有利的支撑。 
(3) 对选取的配对品种进行训练和测试，得到动态的参数阈值变化趋势，验证模型
的有效性。同时，说明研究黑色金属期货品种交易决策对企业的意义，可以有效规避企
业风险以及发现未来品种价格，为企业获取稳定的收益。
东北大学硕士学位论文 
 
第6 章  结论与展望 
-78- 
第6 章 结论与展望 
6.1 本文的主要工作及主要结论 
本文在对期货市场中黑色金属期货配对交易决策进行分析的基础上，对基于期货品
种的配对选择和进出场交易信号进行了研究。 
本文的主要研究工作如下： 
(1) 给出了基于复杂网络和强化学习的量化投资品种选择与交易策略的问题描述和
研究框架。首先给出了基于复杂网络和强化学习的量化投资品种选择与交易策略问题的
形式化描述，为研究框架奠定基础；然后针对基于复杂网络和强化学习的量化投资品种
选择与交易策略问题，给出了本文的研究框架。 
(2) 给出了考虑复杂网络拓扑性对品种进行组合选择。针对考虑投资者配对期对产
品的组合选择问题，主要包括以下三个方面的研究成果：一是考虑期货市场中与黑色
金属相关的期货品种并对其进行数据地选取和处理，计算品种间的波动相关性；二是
运用复杂网络的拓扑性对网络的度分布、平均路径、聚类系数等进行分析，进一步挑
选配对品种；三是将选择的品种进行配对，验证配对品种的有效性。四是通过算例与
数值分析，结合期货交易过程中出现的价格波动以及期货市场中的实际情况对所选择
的配对品种进行选择并检验，确定配对交易的品种。 
(3) 提出了基于强化学习算法对选定的配对品种进行投资决策。针对配对交易策略
的若干关键问题，主要包括以下几个方面的研究成果：一是利用神经网络模型作为代替
强化学习存储经验值Q 表的工具，更利于仿真测试的运行；二是针对期货市场数据的性
质，选择Q-learning 算法进行交易信号的训练与测试；三是结合算例实验对配对品种
进行仿真测试及训练，验证量化投资交易策略的有效性。 
通过论文的研究得到主要结论如下： 
(1) 使用复杂网络拓扑性分析品种之间的相关关系能更加准确的对期货的品种进行
选择。本文在传统量化配对交易品种选择的基础上，考虑期货品种间波动性的影响，综
合复杂网络拓扑性分析进行研究，并使用大宗期货市场数据进行统计分析，统计结果表
明，利用复杂网络对期货品种进行选择较传统的配对选种对象涉及更全面，且有效的提
高了配对品种选择的准确，为配对交易决策奠定了较好的前提条件。 
(2) 在判定量化投资交易策略的情况下，比起单使用传统的配对交易模型，引入强
化学习的改进办法，并融合神经网络更能提高模型的准确性。本文在考虑量化交易决策
交易策略的情况下，提出了强化学习协整配对交易策略研究，增强了模型在不同情况下
东北大学硕士学位论文 
 
第6 章  结论与展望 
-79- 
的实用性。 
6.2 本文的主要贡献 
本文针对基于强化学习黑色金属期货的量化配对交易决策问题，从理论和方法等方
面进行探讨，主要贡献如下： 
(1) 提出了基于复杂网络和强化学习的量化投资品种选择与交易策略的问题描述和
研究框架。在相关研究领域文献的基础上，对现实中大量存在的品种选择与交易策略问
题进行了分析和总结，尝试通过结合复杂网络拓扑性解决品种组合选择问题，引入强化
学习算法实现交易信号的自适应调整优化，为相关问题的研究提供了一般性的分析框架
和理论指导框架，也为其他相关研究提供了一个科学探讨的方向。 
(2) 考虑复杂网络拓扑性对品种进行组合选择。给出期货市场中数据的选取和处理
方法，期货间品种的波动相关性的计算从而构建价格波动网络，并运用复杂网络的拓扑
性对网络的度分布、平均路径、聚类系数等进行分析，进一步挑选配对品种；再将品种
进行配对检验，验证配对品种的有效性。最后通过算例与数值分析，结合期货交易过程
中出现的价格波动以及期货市场中的实际情况，对期货品种价格波动关联网络进行了研
究，运用数学统计的方法对度、加权度、集聚系数、路径长度等指标进行复杂网络的拓
扑性分析并反映期货价格波动网络总体特征。结合算例背景对所选择的配对品种进行配
对检验，确定配对交易的品种。 
(3) 基于强化学习算法对选定的配对品种进行交易信号的设置。结合期货量化交易
的特点以及期货市场中的实际情况，在选定配对交易品种的基础上进行配对交易策略。
基于强化学习探讨交易过程中规则的确定，并通过神经网络的正反向传播进行模型的构
建作为代替强化学习中存储经验值Q 表的工具，并结合算例实验对配对品种进行仿真测
试及训练，并进行绩效风险分析，验证量化投资交易策略的有效性。同时说明研究量化
交易策略对企业的意义，可以有效规避企业的风险以及发现未来品种价格，为企业提供
合理的模型支持与收益保障。 
6.3 本文的研究局限 
本文针对期货市场量化投资问题，在考虑量化投资中期货品种选择与交易信号情况
的基础上，结合复杂网络的拓扑性结构与机器学习中的强化学习算法，将复杂网络和强
化学习应用于传统的量化交易模型有着广泛的实际应用背景。虽然本文取得了一定的研
究成果，但是还有待进一步研究和发展。具体来说，本文的研究局限包括以下几个方面： 
(1) 本文中对于样本内区间和样本外区间的时间范围选择较短，导致本文的配对交
东北大学硕士学位论文 
 
第6 章  结论与展望 
-80- 
易投资次数太少，推测区间长度的设置会对交易阈值的优化以及最终结果产生影响，本
文并未对此问题展开研究。 
(2) 本文只选取了一对期货品种进行配对。一些研究也表明，对于期货市场中的一
些期货品种来说，考虑到季节因素、地域因素等情况也会影响配对品种的选择，本文也
未对此问题深入探讨。 
6.4 进一步研究展望 
本文只是对考虑期货市场中与黑色金属有关的期货品种量化投资进行了研究，并得
出了一些有益的结论，但仍有一些问题有待继续进行深入研究分析，进一步的研究展望
主要包括以下几个方面： 
(1) 现实中的期货市场品种繁多复杂，但鉴于作者所研读资料以及自身学术能力有
限，在考虑复杂网络的多品种量化投资组合选择的问题中，对于期货品种价格网络的构
建不可避免地进行了一定的简化，也有一些因素未考虑到，如季节因素、地域因素以及
不同交易期货市场价格的影响，未来的研究可以考虑引入更多的因素，考虑更复杂的期
货价格波动网络以及配对方式。 
(2) 受到样本条件的限制，选择的是与黑色金属有关的期货品种作为研究对象，研
究数据未能覆盖更多期货品种，这将令本研究结论具有一定的局限性，未来研究可以进
一步对更多期货品种间的关系进行全面检验。 
东北大学硕士学位论文 
 
参考文献 
-81- 
参考文献 
[1] Kou Y, Ye Q, Zhao F, et al. Effects of investor attention on commodity futures markets[J]. 
Finance Research Letters, 1973, 25: 190-195. 
[2] Baker M, Wurgler J. Investor sentiment in the stock market[J]. Journal of economic 
perspectives, 2007, 21(2): 129-152. 
[3] 柏林, 赵大萍, 房勇等. 基于投资者观点的多阶段投资组合选择模型[J]. 系统工程
理论与实践, 2010, 37(8): 2024-2032. 
[4] 吴晓求．证券投资学[J]．北京: 中国人民大学出版社，2014: 445-492． 
[5] Krauss C. Statistical arbitrage pairs trading strategies: Review and outlook[J]. Journal of 
Economic Surveys, 2017, 31(2): 513-545. 
[6] Kester L, Hultink E J, Lauche K. Portfolio decision-making genres: A case study[J]. 
Journal of engineering and technology management, 1987, 26(4): 327-341. 
[7] Tsiotas D, Charakopoulos A. Visibility in the topology of complex networks[J]. Physica 
A: Statistical Mechanics and its Applications, 2018, 505: 280-292. 
[8] Gatev E, Goetzmann W N, Rouwenhorst K G. Pairs trading: Performance of a relative-
value arbitrage rule[J]. The Review of Financial Studies, 2006, 19(3): 797-827. 
[9] Liu Q, Hong T. Sequential seeding for spreading in complex networks: Influence of the 
network topology[J]. Physica A: Statistical Mechanics and its Applications, 2018, 508: 
10-17. 
[10] Blondel V D, Guillaume J L, Lambiotte R, et al. Fast unfolding of communities in large 
networks[J]. Journal of statistical mechanics: theory and experiment, 2008, 2008(10): 
P10008. 
[11] Newman M E J, Girvan M. Finding and evaluating community structure in networks[J]. 
Physical review E, 2004, 69(2): 026113. 
[12] Zhou L, Lin J, Wang Y, et al. Critical phenomena of spreading dynamics on complex 
networks with diverse activity of nodes[J]. Physica A: Statistical Mechanics and its 
Applications, 2018, 47: 53-64. 
[13] 胡文伟, 胡建强, 李湛等. 基于强化学习算法的自适应配对交易模型[J]. 管理科学, 
2017, 30(2): 148-160. 
[14] Gower J C, Ross G J S. Minimum spanning trees and single linkage cluster analysis[J]. 
东北大学硕士学位论文 
 
参考文献 
-82- 
Applied statistics, 1969: 54-64. 
[15] 龙奥明, 毕秀春, 张曙光. 基于LSTM 复杂网络的黑色金属期货套利策略模型[J]. 
中国科学技术大学学报, 2018, 48(2): 125-132. 
[16] 胡钢, 徐翔, 过秀成. 基于解释结构模型的复杂网络节点重要性计算[J]. 浙江大学
学报 (工学版), 2018, 52(10): 1989-1997. 
[17] Li Y, Jiang X F, Tian Y, et al. Portfolio optimization based on network topology[J]. Physica 
A: Statistical Mechanics and its Applications, 2018, 128: 13-16. 
[18] Dolatabadi S, Nielsen M , Xu K. A fractionally cointegrated VAR model with deterministic 
trends and application to commodity futures markets[J]. Journal of Empirical Finance, 
2016, 38: 623-639. 
[19] Wen D, Ma C, Wang G J, et al. Investigating the features of pairs trading strategy: A 
network perspective on the Chinese stock market[J]. Physica A: Statistical Mechanics and 
its Applications, 2018, 505: 903-918. 
[20] Law K F, Li W K, Philip L H. A single-stage approach for cointegration-based pairs 
trading[J]. Finance Research Letters, 2018, 26: 177-184. 
[21] 李世伟. 基于协整理论的沪深300 股指期货跨期套利研究[J]. 中国计量学院学报, 
2011 (2): 198-202. 
[22] 刘永辉, 张帝. 基于协整-OU 过程的配对交易策略研究[J]. 管理评论, 2017, 29(9): 
28-36. 
[23] 于玮婷. 基于协整方法的统计套利策略的实证分析[J]. 科学决策, 2011 (3): 70-85. 
[24] Sutton R S, Barto A G, Bach F. Reinforcement learning: An introduction[M], MIT press, 
1998: 32-37. 
[25] Travers J, Milgram S. The small world problem[J]. Phychology Today, 1967, 1(1): 61-67. 
[26] Watts D J, Strogatz S H. Collective dynamics of ‘small-world’networks[J]. nature, 1998, 
393(6684): 440. 
[27] Barabási A L, Albert R. Emergence of scaling in random networks[J]. science, 2003, 
286(5439): 509-512. 
[28] Tsiotas D, Charakopoulos A. Visibility in the topology of complex networks[J]. Physica 
A: Statistical Mechanics and its Applications, 2017, 505: 280-292. 
[29] Liu Q, Hong T. Sequential seeding for spreading in complex networks: Influence of the 
network topology[J]. Physica A: Statistical Mechanics and its Applications, 2018, 508: 
东北大学硕士学位论文 
 
参考文献 
-83- 
10-17. 
[30] Chiu M C, Wong H Y. Robust dynamic pairs trading with cointegration[J]. Operations 
Research Letters, 1998, 46(2): 225-232. 
[31] Bilal K, Manzano M, Erbad A, et al. Robustness quantification of hierarchical complex 
networks under targeted failures[J]. Computers & Electrical Engineering, 2007, 72: 112-
124. 
[32] Guillaume J L,B, londel V D, Lambiotte R, et al. Fast unfolding of communities in large 
networks[J]. Journal of statistical mechanics: theory and experiment, 2018, 2018(10): 
P10010. 
[33] Christos, Gao X, Fang W, et al. Research on patterns in the fluctuation of the co-movement 
between crude oil futures and spot prices: A complex network approach[J]. Applied 
Energy, 2016, 136: 1067-1075. 
[34] 马源源, 庄新田, 李凌轩. 沪深两市股权关联网络的社团结构及其稳健性[J].系统
工程与实践, 2011, 31(12):2241-2249. 
[35] 金秀, 姜超, 孟婷婷等. 我国股票市场拓扑性及加权网络中行业主导性分析[J]. 东
北大学学报 (自然科学版), 2015, 36(10): 1516-1520. 
[36] 张伟平, 庄新田, 李延双. 股指极端波动下中国股票市场网络拓扑结构[J]. 东北大
学学报 (自然科学版), 2017,39(10): 1511-1515. 
[37] Perlin M S. Evaluation of pairs-trading strategy at the Brazilian financial market[J]. 
Journal of Derivatives & Hedge Funds, 2009, 15(2): 122-136. 
[38] Do B, Faff R. Does simple pairs trading still work?[J]. Financial Analysts Journal, 2010, 
66(4): 83-95. 
[39] Elliott R J, Van Der Hoek* J, Malcolm W P. Pairs trading[J]. Quantitative Finance, 2005, 
5(3): 271-276. 
[40] Engle R F, Granger C W J. Co-integration and error correction: representation, estimation, 
and testing[J]. Econometrica: journal of the Econometric Society, 1987, 25(6): 251-276. 
[41] Whistler M. Trading pairs: capturing profits and hedging risk with statistical arbitrage 
strategies[M], London: Wiley Macmillan, 2004: 53. 
[42] 曾俊涵. 权益成对交易策略于台湾股市之实证研究[D]. 台湾: 高雄第一科技大学, 
2017. 
[43] Dunis C L, Giorgioni G, Laws J, et al. Statistical arbitrage and high-frequency data with 
东北大学硕士学位论文 
 
参考文献 
-84- 
an application to Eurostoxx 50 equities[J]. Liverpool Business School, Working paper, 
2017, 37(1): 125-130. 
[44] Eberlein E, Madan D B. Hedge fund performance: sources and measures[J]. International 
Journal of Theoretical and Applied Finance, 2009, 12(3): 267-282. 
[45] Wang J, Rostoker C, Wagner A. A high performance pair trading application[M], Berlin, 
Parallel & Distributed Processing, 2009. IPDPS 2009. IEEE International Symposium on. 
IEEE, 2009: 1-8. 
[46] Chen H, Chen S, Chen Z, et al. Empirical investigation of an equity pairs trading 
strategy[J]. Management Science, 2017, 162(12): 241-249. 
[47] Gatev E, Goetzmann W N, Rouwenhorst K G. Pairs trading: Performance of a relative-
value arbitrage rule[J]. The Review of Financial Studies, 2006, 19(3): 797-827. 
[48] Herlemont D. Pairs trading, convergence trading, cointegration[J]. YATS Finances and 
Technology, 2003, 33: 1-31. 
[49] Papadakis G, Wysocki P. Pairs trading and accounting information[J]. Boston university 
and mit working paper, 2007, 12(3): 11-12. 
[50] Engelberg J, Gao P, Jagannathan R. An anatomy of pairs trading: the role of idiosyncratic 
news, common information and liquidity[J]. 2008, 254(9): 126-134. 
[51] Bolgün K E, Kurun E, Güven S. Dynamic pairs trading strategy for the companies listed 
in the Istanbul stock exchange[J]. International review of applied financial issues and 
economics, 2010, 2(1): 37. 
[52] 康瑞强. 基于高频数据的期货统计套利研究[D]. 镇江: 江苏大学, 2016. 
[53] 陈祥利. 统计套利理论及策略开发应用研究[D]. 济南: 山东大学, 2018. 
[54] 孔华强. 利用协整提高股指期货跨期套利的成功率[J]. 联合证券, 2017, 8(2): 45-48. 
[55] Samuel A L. Some studies in machine learning using the game of checkers[J]. IBM Journal 
of research and development, 1959, 3(3): 210-229. 
[56] An Y, Ding S, Shi S, et al. Discrete space reinforcement learning algorithm based on 
support vector machine classification[J]. Pattern Recognition Letters, 1988, 111: 30-35. 
[57] Watkins C J C H, Dayan P. Q-learning[J]. Machine learning, 1992, 8(3-4): 279-292. 
[58] Phark C, Kim W, Yoon Y S, et al. Prediction of issuance of emergency evacuation orders 
for chemical accidents using machine learning algorithm[J]. Journal of Loss Prevention 
in the Process Industries, 2018,123(4): 65-69. 
东北大学硕士学位论文 
 
参考文献 
-85- 
[59] Littman M L. Markov games as a framework for multi-agent reinforcement learning[M], 
Paris: Machine Learning Proceedings, 1994. 94(2): 157-163. 
[60] Littman M L. Friend-or-foe Q-learning in general-sum games[M], Paris: Machine 
Learning Proceedings, 2001, 1: 322-328. 
[61] Hu J, Wellman M P. Nash Q-learning for general-sum stochastic games[J]. Journal of 
machine learning research, 2003, 4(11): 1039-1069. 
[62] Kim H, Shin K. A hybrid approach based on neural networks and genetic algorithms for 
detecting temporal patterns in stock markets[J]. Applied Soft Computing, 2007, 7(2): 569-
576. 
[63] TanM. Multi-agent reinforcement learning: Independent vs.cooperative agents [M], 
London: Proceedings of the tenth international conference on machine learning, 2016, 10: 
330-337. 
[64] Matarić M J. Learning in behavior-based multi-robot systems: Policies, models, and other 
agents[J]. Cognitive Systems Research, 2017, 2(1): 81-93. 
[65] Arai S, Sycara K. Multi-agent reinforcement learning for planning and conflict resolution 
in a dynamic domain[M], London: Proceedings of the fourth international conference on 
Autonomous agents, ACM, 2017: 104-105. 
[66] Wiering M, Van Otterlo M. Reinforcement learning[J]. Adaptation, learning, and 
optimization, 2017, 12(3): 51-58. 
[67] McPartland M, Gallagher M. Reinforcement learning in first person shooter games[J]. 
IEEE Transactions on Computational Intelligence and AI in Games, 2017, 3(1): 43-56. 
[68] 高阳, 周志华. 基于Markov 对策的多 Agent 强化学习模型及算法研究[J]. 计算机
研究与发展, 2018, 37(3): 257-263. 
[69] 范波, 潘泉, 张洪才. 一种基于分布式强化学习的多智能体协调方法[J]. 计算机仿
真, 2018, 22(6): 115-117. 
[70] Scuteri A, Rovella V, Fegatelli D A, et al. An operational definition of SHATS (Systemic 
Hemodynamic Atherosclerotic Syndrome): Role of arterial stiffness and blood pressure 
variability in elderly hypertensive subjects[J]. International journal of cardiology, 2018, 
263: 132-137. 
[71] 张汝波, 周宁. 基于强化学习的智能机器人避碰方法研究[J]. 机器人, 2016, 21(3): 
204-209. 
东北大学硕士学位论文 
 
参考文献 
-86- 
[72] Bianchi R A C, Martins M F, Ribeiro C H C, et al. Heuristically-accelerated multiagent 
reinforcement learning[J]. IEEE transactions on cybernetics, 2018, 44(2): 252-265. 
[73] Yasunobu S, Matsubara T. Fuzzy target acquired by reinforcement learning for parking 
control[M], Dresden, SICE 2003 Annual Conference, IEEE, 2015, 2: 1242-1247. 
[74] Song Y, Li Y, Li C, et al. An efficient initialization approach of Q-learning for mobile 
robots[J]. International Journal of Control, Automation and Systems, 2017, 10(1): 166-
172. 
[75] 刘俊一. 人工智能领域的机器学习算法研究综述[J]. 数字通信世界, 2018, 11: 234-
235. 
[76] Padmanabhan R, Meskin N, Haddad W M. Reinforcement learning-based control of drug 
dosing for cancer chemotherapy treatment[J]. Mathematical biosciences, 2017, 293: 11-
20. 
[77] Shahrabi J, Adibi M A, Mahootchi M. A reinforcement learning approach to parameter 
estimation in dynamic job shop scheduling[J]. Computers & Industrial Engineering, 2017, 
110: 75-82. 
[78] Bogert K, Doshi P. Multi-robot inverse reinforcement learning under occlusion with 
estimation of state transitions[J]. Artificial Intelligence, 2018, 263: 46-73. 
[79] Todorova N, Worthington A, Souček M. Realized volatility spillovers in the non-ferrous 
metal futures market[J]. Resources Policy, 2014, 39: 21-31. 
[80]  
Watkins C, McAleer M. Cointegration analysis of metals futures[J]. Mathematics and 
computers in simulation, 2002, 59(1-3): 207-221. 
[81] Lahtinen T J, Hämäläinen R P, Liesiö J. Portfolio decision analysis methods in 
environmental decision making[J]. Environmental Modelling & Software, 2017, 94: 73-
86. 
[82] Paiva F D, Cardoso R T N, Hanaoka G P, et al. Decision-making for financial trading: A 
fusion approach of machine learning and portfolio selection[J]. Expert Systems with 
Applications, 2019, 115: 635-655. 
[83] Dickey S H, Fuller A K, Albulescu C T, et al. Time-frequency co-movements between the 
largest nonferrous metal futures markets[J]. Resources Policy, 1976, 140(11): 48-55. 
[84] Law K F, Li W K, Philip L H. A single-stage approach for cointegration-based pairs 
trading[J]. Finance Research Letters, 2018, 26: 177-184. 
东北大学硕士学位论文 
 
参考文献 
-87- 
[85] Wen D, Ma C, Wang G J, et al. Investigating the features of pairs trading strategy: A 
network perspective on the Chinese stock market[J]. Physica A: Statistical Mechanics and 
its Applications, 2018, 505: 903-918. 
[86] Travers J, Milgram S. The small world problem[J]. Phychology Today, 1967, 1(1): 61-67. 
[87] Zou Y, Donner R V, Marwan N, et al. Complex network approaches to nonlinear time 
series analysis[J]. Physics Reports, 2018, 39(8): 128-136. 
[88] Yao Y, Zhang R, Yang F, et al. Link prediction in complex networks based on the 
interactions among paths[J]. Physica A: Statistical Mechanics and its Applications, 2018, 
108(6): 112-119. 
[89] Wang H, Wang X, Zhang X, et al. Effective service composition using multi-agent 
reinforcement learning[J]. Knowledge-Based Systems, 2016, 92: 151-168. 
[90] Leonetti M, Iocchi L, Stone P. A synthesis of automated planning and reinforcement 
learning for efficient, robust decision-making[J]. Artificial Intelligence, 2016, 241: 103-
130. 
[91] dos Santos Mignon A, da Rocha R L A. An Adaptive Implementation of ε-Greedy in 
Reinforcement Learning[J]. Procedia Computer Science, 2017, 109: 1146-1151. 
[92] Hein D, Hentschel A, Runkler T, et al. Particle swarm optimization for generating 
interpretable fuzzy reinforcement learning policies[J]. Engineering Applications of 
Artificial Intelligence, 2017, 65: 87-98. 
[93] Brys T, Harutyunyan A, Vrancx P, et al. Multi-objectivization and ensembles of shapings 
in reinforcement learning[J]. Neurocomputing, 2017, 263: 48-59.  
 
 
 
东北大学硕士学位论文                                                            致谢 
-88- 
致谢 
研究生生活即将结束，也可以说这是最后一段学生时代的记忆，回想起第一次踏进
东北大学校门的那个时刻好像就在眼前。两年半的学习和生活少不了老师、同门、朋友
们的关心与帮助。 
首先，感谢我的导师张吉善副教授。老师做事严谨的工作作风，漫随天外云卷云舒
的生活态度，让我从学习和生活上都受益颇多。张老师在百忙之中都会保证每周一次的
研讨，每一次细致的修改都对论文的进展起着掌舵性地作用。在与张老师相处的两年半
时光里，张老师的幽默与大度，诚信与宽容，让我收获到远远不止学术上的锻炼与提升。
张老师就像我的亲人一样，学习上要求力争上游，生活上要求平安快乐。也记得老师在
我打球受伤后住院的日子里，对我说的一句话：生命在于静止。看似轻描淡写，但透露
着老师对我的提醒与关爱。我也从张老师身上学到，随遇而安，是多么惬意的事情。永
远记得张老师操着一口标准的大连话和我们谈天说地，永远记得张老师在研讨会上边擦
汗水边声如洪钟的讲话，永远记得张老师把我们当成孩子一样为大家带过的酸奶水果。
张老师，我想对您说：感谢您！您辛苦了！ 
其次，感谢张老师带领的学术科研团队。真的很庆幸能在这个集体里学习和生活，
让我感受到了同门之间的情谊。每周一次的研讨在我看来是很期待的日子，因为可以和
大家交流学术心得，分享日常琐事。罗振可以倾听和解答我的任何问题，不管是学术上
还是生活上；刘璐也总是能在不经意间解开我的疑惑，和他们在一起就像是亲兄妹一样
的快乐。师弟师妹们也都很热情，可能是因为张老师的原因，在这个研讨组总会得到很
多快乐，也真的感谢你们两年半的陪伴。 
再次，感谢我的同学。感谢我的六位室友，刘莉、刘春怡、李艳婷，你们带给我的
是谦让理解，平等沟通；杨亚静、李怡萍、王玮琪，你们带给我的是活力朝气，潇洒人
生。你们的陪伴让我在研究生生活的不同阶段，感受到了不同的安稳与自在。同时感谢 
2016级管理科学与工程的其他同学们，大家的努力和拼搏让我对生活充满斗志；再次感
谢冯绎方、都俊男、韩宏伟，我的室友和同门，感谢在我生病时对我的照顾。  
最后，感谢我的家人。感谢爸妈的信任与放手，让我成为一个独立自信的女生，让
我拥有开朗乐观的性格，不惧未来的勇气，也感谢你们所给予的理解与包容。同时，感
谢工商管理学院各位老师的指导和帮助，愿身体健康，工作顺利。
 
-89- 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
 
全文                        
页数：88 页 
字数：5.5 万字 
表数：17 个 
图数：34 个 
参考文献总数：93 篇 
 
